"""
Unit tests for the dynamic label selection functionality.
Tests the label selector, query builder, and configuration system.
"""

import unittest
import os
import sys
from unittest.mock import Mock, patch, MagicMock
from typing import List

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from query_builder import GmailQueryBuilder
from config import Config
from email_limit_manager import EmailLimitManager
from datetime_utils import ModernDateTimeHandler


class TestGmailQueryBuilder(unittest.TestCase):
    """Test cases for the Gmail query builder."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.builder = GmailQueryBuilder()
    
    def test_build_brady_query(self):
        """Test building query for Brady label."""
        query, max_results = self.builder.build_label_query('Brady')
        
        self.assertIn('has:attachment filename:pdf', query)
        self.assertIn('-label:processed', query)
        self.assertEqual(max_results, 100)  # <PERSON> should have higher limit
    
    def test_build_other_label_query(self):
        """Test building query for non-Brady labels."""
        query, max_results = self.builder.build_label_query('RSEA')
        
        self.assertIn('has:attachment filename:pdf', query)
        self.assertIn('-label:processed', query)
        self.assertIn('after:2024/01/01', query)  # Should have date filter
        self.assertEqual(max_results, 10)  # Other labels should have lower limit
    
    def test_build_unknown_label_query(self):
        """Test building query for unknown label."""
        query, max_results = self.builder.build_label_query('Unknown_Label')
        
        self.assertIn('has:attachment filename:pdf', query)
        self.assertIn('-label:processed', query)
        self.assertEqual(max_results, 10)  # Should use default settings
    
    def test_should_skip_packing_slips(self):
        """Test packing slip skip logic."""
        # Brady should skip packing slips
        self.assertTrue(self.builder.should_skip_packing_slips('Brady'))
        
        # Other labels should not skip packing slips
        self.assertFalse(self.builder.should_skip_packing_slips('RSEA'))
    
    def test_get_label_description(self):
        """Test getting label descriptions."""
        brady_desc = self.builder.get_label_description('Brady')
        self.assertIn('Brady Corporation', brady_desc)
        
        rsea_desc = self.builder.get_label_description('RSEA')
        self.assertIn('RSEA Safety', rsea_desc)
    
    def test_validate_query(self):
        """Test query validation."""
        # Valid queries
        self.assertTrue(self.builder.validate_query('has:attachment filename:pdf'))
        self.assertTrue(self.builder.validate_query('has:attachment filename:pdf after:2024/01/01'))
        
        # Invalid queries
        self.assertFalse(self.builder.validate_query(''))  # Empty query
        self.assertFalse(self.builder.validate_query('has:attachment "unclosed quote'))  # Unbalanced quotes
    
    def test_get_query_summary(self):
        """Test getting query summary."""
        summary = self.builder.get_query_summary('Brady')
        
        self.assertEqual(summary['label_name'], 'Brady')
        self.assertIn('query', summary)
        self.assertIn('max_results', summary)
        self.assertIn('description', summary)
        self.assertTrue(summary['valid'])
    
    def test_batch_queries(self):
        """Test building queries for multiple labels."""
        labels = ['Brady', 'RSEA', 'Unknown']
        queries = self.builder.build_batch_queries(labels)
        
        self.assertEqual(len(queries), 3)
        self.assertIn('Brady', queries)
        self.assertIn('RSEA', queries)
        self.assertIn('Unknown', queries)
        
        # Each query should be a tuple of (query, max_results)
        for label, (query, max_results) in queries.items():
            self.assertIsInstance(query, str)
            self.assertIsInstance(max_results, int)
            self.assertGreater(len(query), 0)
            self.assertGreater(max_results, 0)


class TestConfig(unittest.TestCase):
    """Test cases for the configuration system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = Config()
    
    def test_get_label_settings_brady(self):
        """Test getting Brady label settings."""
        settings = self.config.get_label_settings('Brady')
        
        self.assertEqual(settings['date_filter'], '')  # No date filter
        self.assertEqual(settings['max_results'], 100)
        self.assertTrue(settings['skip_packing_slips'])
        self.assertIn('Brady Corporation', settings['description'])
    
    def test_get_label_settings_rsea(self):
        """Test getting RSEA label settings."""
        settings = self.config.get_label_settings('RSEA')
        
        self.assertEqual(settings['date_filter'], 'after:2024/01/01')
        self.assertEqual(settings['max_results'], 10)
        self.assertFalse(settings['skip_packing_slips'])
        self.assertIn('RSEA Safety', settings['description'])
    
    def test_get_label_settings_unknown(self):
        """Test getting settings for unknown label."""
        settings = self.config.get_label_settings('Unknown_Label')
        
        # Should return default settings
        self.assertEqual(settings['date_filter'], 'after:2024/01/01')
        self.assertEqual(settings['max_results'], 10)
        self.assertFalse(settings['skip_packing_slips'])
        self.assertIn('Unknown_Label - Custom label', settings['description'])
    
    def test_case_insensitive_label_lookup(self):
        """Test that label lookup is case insensitive."""
        settings_lower = self.config.get_label_settings('brady')
        settings_upper = self.config.get_label_settings('BRADY')
        settings_mixed = self.config.get_label_settings('Brady')
        
        self.assertEqual(settings_lower, settings_upper)
        self.assertEqual(settings_upper, settings_mixed)
    
    @patch.dict(os.environ, {'RUNTIME_LABELS_TO_PROCESS': 'Brady,RSEA,Test'})
    def test_get_runtime_labels(self):
        """Test getting runtime labels from environment."""
        labels = self.config.get_runtime_labels()
        
        self.assertEqual(labels, ['Brady', 'RSEA', 'Test'])
    
    @patch.dict(os.environ, {'RUNTIME_LABELS_TO_PROCESS': ''})
    def test_get_runtime_labels_empty(self):
        """Test getting runtime labels when environment variable is empty."""
        labels = self.config.get_runtime_labels()
        
        # Should fall back to config labels
        self.assertEqual(labels, self.config.GMAIL_LABELS_TO_PROCESS)


class TestLabelSelector(unittest.TestCase):
    """Test cases for the label selector."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Mock the Gmail service to avoid actual API calls
        self.mock_gmail_service = Mock()
        self.mock_gmail_service.get_user_labels.return_value = ['Brady', 'RSEA', 'Woolworths']
        self.mock_gmail_service.get_available_processing_labels.return_value = ['Brady', 'RSEA']
    
    @patch('label_selector.GmailService')
    def test_validate_labels_valid(self, mock_gmail_class):
        """Test validating valid labels."""
        mock_gmail_class.return_value = self.mock_gmail_service
        
        from label_selector import LabelSelector
        selector = LabelSelector()
        
        valid_labels = selector.validate_labels(['Brady', 'RSEA'])
        self.assertEqual(valid_labels, ['Brady', 'RSEA'])
    
    @patch('label_selector.GmailService')
    def test_validate_labels_invalid(self, mock_gmail_class):
        """Test validating invalid labels."""
        mock_gmail_class.return_value = self.mock_gmail_service
        
        from label_selector import LabelSelector
        selector = LabelSelector()
        
        valid_labels = selector.validate_labels(['Invalid_Label'])
        self.assertEqual(valid_labels, ['Brady'])  # Should fall back to Brady
    
    @patch('label_selector.GmailService')
    def test_validate_labels_mixed(self, mock_gmail_class):
        """Test validating mix of valid and invalid labels."""
        mock_gmail_class.return_value = self.mock_gmail_service
        
        from label_selector import LabelSelector
        selector = LabelSelector()
        
        valid_labels = selector.validate_labels(['Brady', 'Invalid_Label', 'RSEA'])
        self.assertEqual(valid_labels, ['Brady', 'RSEA'])
    
    @patch.dict(os.environ, {'RUNTIME_LABELS_TO_PROCESS': 'Brady,RSEA'})
    @patch('label_selector.GmailService')
    def test_get_runtime_labels_from_env(self, mock_gmail_class):
        """Test getting runtime labels from environment."""
        mock_gmail_class.return_value = self.mock_gmail_service
        
        from label_selector import LabelSelector
        selector = LabelSelector()
        
        labels = selector.get_runtime_labels_from_env()
        self.assertEqual(labels, ['Brady', 'RSEA'])


class TestRuntimeConfig(unittest.TestCase):
    """Test cases for runtime configuration."""
    
    def test_parse_labels_argument(self):
        """Test parsing labels from command line arguments."""
        # Mock sys.argv to simulate command line arguments
        test_args = ['script.py', '--labels', 'Brady,RSEA,Woolworths']
        
        with patch.object(sys, 'argv', test_args):
            from runtime_config import RuntimeConfig
            config = RuntimeConfig()
            
            labels = config.get_processing_labels()
            self.assertEqual(labels, ['Brady', 'RSEA', 'Woolworths'])
    
    def test_parse_all_labels_argument(self):
        """Test parsing 'all' labels argument."""
        test_args = ['script.py', '--labels', 'all']
        
        with patch.object(sys, 'argv', test_args):
            from runtime_config import RuntimeConfig
            config = RuntimeConfig()
            
            labels = config.get_processing_labels()
            self.assertEqual(labels, 'all')
    
    def test_parse_brady_only_argument(self):
        """Test parsing Brady-only argument."""
        test_args = ['script.py', '--brady-only']
        
        with patch.object(sys, 'argv', test_args):
            from runtime_config import RuntimeConfig
            config = RuntimeConfig()
            
            labels = config.get_processing_labels()
            self.assertEqual(labels, ['Brady'])
    
    def test_parse_non_interactive_argument(self):
        """Test parsing non-interactive argument."""
        test_args = ['script.py', '--non-interactive']
        
        with patch.object(sys, 'argv', test_args):
            from runtime_config import RuntimeConfig
            config = RuntimeConfig()
            
            self.assertFalse(config.is_interactive_mode())
    
    def test_parse_dry_run_argument(self):
        """Test parsing dry-run argument."""
        test_args = ['script.py', '--dry-run']
        
        with patch.object(sys, 'argv', test_args):
            from runtime_config import RuntimeConfig
            config = RuntimeConfig()
            
            self.assertTrue(config.is_dry_run())


class TestEmailLimitManager(unittest.TestCase):
    """Test cases for the email limit manager."""

    def setUp(self):
        """Set up test fixtures."""
        self.manager = EmailLimitManager()

    def test_validate_limit_valid(self):
        """Test validating valid email limits."""
        valid, limit, error = self.manager.validate_limit(50)
        self.assertTrue(valid)
        self.assertEqual(limit, 50)
        self.assertEqual(error, "")

    def test_validate_limit_too_low(self):
        """Test validating email limit that's too low."""
        valid, limit, error = self.manager.validate_limit(0)
        self.assertFalse(valid)
        self.assertEqual(limit, self.manager.DEFAULT_LIMIT)
        self.assertIn("at least", error)

    def test_validate_limit_too_high(self):
        """Test validating email limit that's too high."""
        valid, limit, error = self.manager.validate_limit(2000)
        self.assertFalse(valid)
        self.assertEqual(limit, self.manager.DEFAULT_LIMIT)
        self.assertIn("cannot exceed", error)

    def test_validate_limit_string(self):
        """Test validating string email limit."""
        valid, limit, error = self.manager.validate_limit("25")
        self.assertTrue(valid)
        self.assertEqual(limit, 25)
        self.assertEqual(error, "")

    def test_validate_limit_invalid_string(self):
        """Test validating invalid string email limit."""
        valid, limit, error = self.manager.validate_limit("invalid")
        self.assertFalse(valid)
        self.assertEqual(limit, self.manager.DEFAULT_LIMIT)
        self.assertIn("Invalid limit value", error)

    def test_get_effective_limit_brady_default(self):
        """Test getting effective limit for Brady (should be higher default)."""
        limit = self.manager.get_effective_limit('Brady')
        self.assertEqual(limit, self.manager.BRADY_DEFAULT_LIMIT)

    def test_get_effective_limit_other_default(self):
        """Test getting effective limit for other labels."""
        limit = self.manager.get_effective_limit('RSEA')
        self.assertEqual(limit, self.manager.DEFAULT_LIMIT)

    @patch.dict(os.environ, {'MAX_GMAIL_RESULTS_OVERRIDE': '75'})
    def test_get_global_override_from_env(self):
        """Test getting global override from environment."""
        override = self.manager.get_global_override()
        self.assertEqual(override, 75)

    @patch.dict(os.environ, {'MAX_GMAIL_RESULTS_BRADY': '150'})
    def test_get_per_label_override_from_env(self):
        """Test getting per-label override from environment."""
        override = self.manager.get_per_label_override('Brady')
        self.assertEqual(override, 150)

    def test_get_limit_source_info(self):
        """Test getting limit source information."""
        info = self.manager.get_limit_source_info('Brady')

        self.assertEqual(info['label_name'], 'Brady')
        self.assertEqual(info['effective_limit'], self.manager.BRADY_DEFAULT_LIMIT)
        # Brady gets its limit from label-specific configuration, not default
        self.assertEqual(info['source'], 'label_specific')
        self.assertFalse(info['is_brady_default'])  # Not using default since it's configured

    def test_get_all_limits_summary(self):
        """Test getting summary for multiple labels."""
        labels = ['Brady', 'RSEA', 'Unknown']
        summary = self.manager.get_all_limits_summary(labels)

        self.assertEqual(len(summary), 3)
        self.assertIn('Brady', summary)
        self.assertIn('RSEA', summary)
        self.assertIn('Unknown', summary)

        # Brady should have higher default
        self.assertEqual(summary['Brady']['effective_limit'], self.manager.BRADY_DEFAULT_LIMIT)
        self.assertEqual(summary['RSEA']['effective_limit'], self.manager.DEFAULT_LIMIT)


class TestModernDateTimeHandler(unittest.TestCase):
    """Test cases for the modern datetime handler."""

    def setUp(self):
        """Set up test fixtures."""
        self.handler = ModernDateTimeHandler()

    def test_now_utc(self):
        """Test getting current UTC time."""
        now = self.handler.now_utc()
        self.assertIsNotNone(now.tzinfo)
        self.assertEqual(now.tzinfo.utcoffset(None).total_seconds(), 0)

    def test_format_for_gmail_query(self):
        """Test formatting datetime for Gmail query."""
        from datetime import datetime, timezone
        dt = datetime(2024, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
        formatted = self.handler.format_for_gmail_query(dt)
        self.assertEqual(formatted, "2024/01/15")

    def test_get_date_filter_for_hours_ago(self):
        """Test getting date filter for hours ago."""
        filter_str = self.handler.get_date_filter_for_hours_ago(48)
        self.assertTrue(filter_str.startswith("after:"))
        self.assertRegex(filter_str, r"after:\d{4}/\d{2}/\d{2}")

    def test_get_date_filter_for_days_ago(self):
        """Test getting date filter for days ago."""
        filter_str = self.handler.get_date_filter_for_days_ago(7)
        self.assertTrue(filter_str.startswith("after:"))
        self.assertRegex(filter_str, r"after:\d{4}/\d{2}/\d{2}")

    def test_parse_date_filter(self):
        """Test parsing Gmail date filter."""
        filter_str = "after:2024/01/15"
        parsed = self.handler.parse_date_filter(filter_str)
        self.assertIsNotNone(parsed)
        self.assertEqual(parsed.year, 2024)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 15)

    def test_validate_date_range_valid(self):
        """Test validating valid date range."""
        is_valid, error = self.handler.validate_date_range("2024/01/01", "2024/12/31")
        self.assertTrue(is_valid)
        self.assertEqual(error, "")

    def test_validate_date_range_invalid_order(self):
        """Test validating date range with invalid order."""
        is_valid, error = self.handler.validate_date_range("2024/12/31", "2024/01/01")
        self.assertFalse(is_valid)
        self.assertIn("before", error)


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
