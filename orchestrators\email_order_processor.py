# email_order_processor.py - Clean, modular email order processing pipeline

import os
import base64
import logging
import json
import time
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass

# External Libraries
from dotenv import load_dotenv
import requests

# Gmail API imports
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials as OAuth2Credentials
from google.auth.external_account_authorized_user import Credentials as ExternalAccountCredentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from bs4 import BeautifulSoup

# PDF text extraction
import fitz

# Gemini LLM imports
from google.generativeai import GenerativeModel, configure as configure_gemini

# Data validation
from pydantic import BaseModel, Field, ValidationError

# Load environment variables
load_dotenv(dotenv_path='.env')

# ==============================================================================
# --- CONFIGURATION ---
# ==============================================================================

@dataclass
class Config:
    """Configuration settings loaded from environment variables"""
    
    # Gmail API Configuration
    GMAIL_SCOPES = ['https://www.googleapis.com/auth/gmail.readonly', 'https://www.googleapis.com/auth/gmail.modify']
    GMAIL_CREDENTIALS_FILE = os.getenv('GMAIL_CREDENTIALS_FILE', 'credentials.json')
    GMAIL_TOKEN_FILE = os.getenv('GMAIL_TOKEN_FILE', 'token.json')
    GMAIL_LABELS_TO_PROCESS = os.getenv('GMAIL_LABELS_TO_PROCESS')
    MAX_GMAIL_RESULTS = int(os.getenv('MAX_GMAIL_RESULTS', 10))
    GMAIL_UNREAD_ONLY = os.getenv('GMAIL_UNREAD_ONLY', 'True').lower() in ('true', '1', 'yes')
    
    # Gemini LLM Configuration
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.5-flash-preview-05-20")
    
    # MYOB API Configuration
    EXO_IP = os.getenv("EXO_IP")
    EXO_PORT = os.getenv("EXO_PORT")
    USER = os.getenv("USER")
    PWD = os.getenv("PWD")
    API_KEY = os.getenv("API_KEY")
    EXO_TOK = os.getenv("EXO_TOK")
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    def __post_init__(self):
        """Validate required configuration"""
        if not all([self.EXO_IP, self.EXO_PORT, self.USER, self.PWD, self.API_KEY, self.EXO_TOK]):
            raise ValueError("Missing required MYOB API configuration variables")
        
        if not self.GEMINI_API_KEY:
            raise ValueError("Missing required GEMINI_API_KEY")
    
    @property
    def myob_base_url(self) -> str:
        return f"http://{self.EXO_IP}:{self.EXO_PORT}"
    
    @property
    def myob_headers(self) -> Dict[str, str]:
        auth_string = f"{self.USER}:{self.PWD}"
        base64_auth = base64.b64encode(auth_string.encode()).decode()
        return {
            "Authorization": f"Basic {base64_auth}",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-myobapi-key": os.getenv("API_KEY", ""),
            "x-myobapi-exoToken": os.getenv("EXO_TOK", "")
        }
    
    @property
    def gmail_query_filter(self) -> str:
        base_query = "has:attachment filename:pdf"
        return f"{base_query} is:unread" if self.GMAIL_UNREAD_ONLY else base_query

# ==============================================================================
# --- DATA MODELS ---
# ==============================================================================

class CustomerDetails(BaseModel):
    """Customer information for the order"""
    debtor_id: int
    customer_order_number: Optional[str] = None

class DeliveryAddress(BaseModel):
    """Delivery address information"""
    line1: Optional[str] = None
    line2: Optional[str] = None
    line3: Optional[str] = None

class OrderLine(BaseModel):
    """Individual order line item"""
    stockcode: str
    orderquantity: float

class ExtractedOrder(BaseModel):
    """Complete extracted order information"""
    customer_details: CustomerDetails
    order_status: int = Field(default=0, description="0=Not Processed, 3=Quotation, 4=Standing Order, 5=Layby")
    delivery_address: Optional[DeliveryAddress] = None
    order_lines: List[OrderLine]
    x_shipvia: Optional[str] = Field(default=None, description="Shipping method, e.g., 'CAPITAL'")

@dataclass
class EmailSummary:
    """Email summary information"""
    id: str
    subject: str
    sender: str
    date: str
    source_label: str

@dataclass
class EmailDetails:
    """Detailed email information including attachments"""
    id: str
    subject: str
    sender: str
    date: str
    body: str
    attachments: List[Dict[str, Any]]

@dataclass
class ProcessedOrder:
    """Order information after processing"""
    email_id: str
    parsed_data: Dict[str, Any]
    markdown_summary: str
    user_decision: Optional[str] = None

# ==============================================================================
# --- LOGGING SETUP ---
# ==============================================================================

def setup_logging(config: Config) -> logging.Logger:
    """Configure logging"""
    log_level = getattr(logging, config.LOG_LEVEL, logging.INFO)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

# ==============================================================================
# --- GMAIL SERVICE ---
# ==============================================================================

class GmailService:
    """Handles Gmail API operations"""
    
    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.service = None
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize Gmail API service with authentication"""
        creds = self._get_credentials()
        if not creds or not creds.valid:
            raise ValueError("Could not obtain valid Gmail credentials")
        
        try:
            self.service = build('gmail', 'v1', credentials=creds)
            self.logger.info("Gmail service initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to build Gmail service: {e}")
            raise
    
    def _get_credentials(self) -> Optional[Credentials]:
        """Get or refresh Gmail API credentials"""
        creds = None
        
        # Load existing credentials
        if os.path.exists(self.config.GMAIL_TOKEN_FILE):
            try:
                creds = Credentials.from_authorized_user_file(
                    self.config.GMAIL_TOKEN_FILE, 
                    self.config.GMAIL_SCOPES
                )
                self.logger.info(f"Loaded Gmail credentials from {self.config.GMAIL_TOKEN_FILE}")
            except Exception as e:
                self.logger.warning(f"Failed to load credentials: {e}")
                creds = None
        
        # Refresh or authenticate
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    self.logger.info("Gmail token refreshed successfully")
                except Exception as e:
                    self.logger.warning(f"Failed to refresh token: {e}")
                    creds = None
            
            if not creds or not creds.valid:
                creds = self._run_auth_flow()
        
        # Save credentials
        if creds and creds.valid:
            try:
                with open(self.config.GMAIL_TOKEN_FILE, 'w') as token:
                    token.write(creds.to_json())
                self.logger.info(f"Saved credentials to {self.config.GMAIL_TOKEN_FILE}")
            except Exception as e:
                self.logger.error(f"Failed to save credentials: {e}")
        
        return creds
    
    def _run_auth_flow(self) -> Union[OAuth2Credentials, ExternalAccountCredentials]:
        """Run OAuth flow for Gmail authentication"""
        if not os.path.exists(self.config.GMAIL_CREDENTIALS_FILE):
            raise FileNotFoundError(f"Gmail credentials file not found: {self.config.GMAIL_CREDENTIALS_FILE}")
        
        self.logger.info("Running Gmail authentication flow")
        flow = InstalledAppFlow.from_client_secrets_file(
            self.config.GMAIL_CREDENTIALS_FILE, 
            self.config.GMAIL_SCOPES
        )
        return flow.run_local_server(port=8080, open_browser=True)
    
    def _get_label_id(self, label_name: str) -> Optional[str]:
        """Get Gmail label ID by name"""
        try:
            results = self.service.users().labels().list(userId='me').execute()
            labels = results.get('labels', [])
            for label in labels:
                if label['name'].lower() == label_name.lower():
                    return label['id']
            return None
        except Exception as e:
            self.logger.error(f"Error getting label ID for '{label_name}': {e}")
            return None
    
    def _get_email_summary(self, message_id: str) -> Optional[EmailSummary]:
        """Get email summary information"""
        try:
            msg = self.service.users().messages().get(userId='me', id=message_id).execute()
            headers = msg['payload'].get('headers', [])
            
            subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
            sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown Sender')
            date = next((h['value'] for h in headers if h['name'] == 'Date'), 'Unknown Date')
            
            return EmailSummary(
                id=message_id,
                subject=subject,
                sender=sender,
                date=date,
                source_label=""  # Will be set by caller
            )
        except Exception as e:
            self.logger.error(f"Error getting email summary for {message_id}: {e}")
            return None
    
    def get_email_details(self, message_id: str) -> Optional[EmailDetails]:
        """Get detailed email information including attachments"""
        try:
            msg = self.service.users().messages().get(userId='me', id=message_id).execute()
            headers = msg['payload'].get('headers', [])
            
            subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
            sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown Sender')
            date = next((h['value'] for h in headers if h['name'] == 'Date'), 'Unknown Date')
            
            # Extract body and attachments
            body = self._extract_email_body(msg['payload'])
            attachments = self._extract_attachments(message_id, msg['payload'])
            
            # Process PDF attachments
            pdf_text = ""
            for attachment in attachments:
                if attachment['filename'].lower().endswith('.pdf'):
                    pdf_text += self._extract_pdf_text(attachment['data'])
            
            # Combine email body and PDF text
            full_text = f"{body}\n\n--- PDF Content ---\n{pdf_text}".strip()
            
            return EmailDetails(
                id=message_id,
                subject=subject,
                sender=sender,
                date=date,
                body=full_text,
                attachments=attachments
            )
            
        except Exception as e:
            self.logger.error(f"Error getting email details for {message_id}: {e}")
            return None
    
    def _extract_email_body(self, payload: Dict) -> str:
        """Extract text from email body"""
        body = ""
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    if 'data' in part['body']:
                        body += base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                elif part['mimeType'] == 'text/html':
                    if 'data' in part['body']:
                        html_content = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                        soup = BeautifulSoup(html_content, 'html.parser')
                        body += soup.get_text()
        elif payload['mimeType'] == 'text/plain':
            if 'data' in payload['body']:
                body = base64.urlsafe_b64decode(payload['body']['data']).decode('utf-8')
        elif payload['mimeType'] == 'text/html':
            if 'data' in payload['body']:
                html_content = base64.urlsafe_b64decode(payload['body']['data']).decode('utf-8')
                soup = BeautifulSoup(html_content, 'html.parser')
                body = soup.get_text()
        
        return body.strip()
    
    def _extract_attachments(self, message_id: str, payload: Dict) -> List[Dict[str, Any]]:
        """Extract attachments from email"""
        attachments = []
        
        def process_part(part):
            if 'filename' in part and part['filename']:
                if 'attachmentId' in part['body']:
                    attachment_id = part['body']['attachmentId']
                    attachment = self.service.users().messages().attachments().get(
                        userId='me', messageId=message_id, id=attachment_id
                    ).execute()
                    
                    attachments.append({
                        'filename': part['filename'],
                        'mimeType': part['mimeType'],
                        'data': base64.urlsafe_b64decode(attachment['data'])
                    })
            
            if 'parts' in part:
                for subpart in part['parts']:
                    process_part(subpart)
        
        process_part(payload)
        return attachments
    
    def _extract_pdf_text(self, pdf_data: bytes) -> str:
        """Extract text from PDF data"""
        try:
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            text_content = []
            for page_num in range(doc.page_count):
                page = doc.load_page(page_num)
                text_content.append(page.get_text())
            doc.close()
            self.logger.info(f"Extracted text from PDF ({len(text_content)} pages)")
            return "\n".join(text_content).strip()
        except Exception as e:
            self.logger.error(f"Error extracting PDF text: {e}")
            return ""
    
    def mark_email_as_read(self, message_id: str) -> bool:
        """Mark email as read"""
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            self.logger.info(f"Marked email {message_id} as read")
            return True
        except Exception as e:
            self.logger.error(f"Error marking email {message_id} as read: {e}")
            return False

# ==============================================================================
# --- LLM SERVICE ---
# ==============================================================================

class LLMService:
    """Handles LLM operations for parsing and processing"""
    
    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self._initialize_gemini()
    
    def _initialize_gemini(self):
        """Initialize Gemini model"""
        try:
            configure_gemini(api_key=self.config.GEMINI_API_KEY)
            self.model = GenerativeModel(self.config.GEMINI_MODEL)
            self.logger.info("Gemini model initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Gemini model: {e}")
            raise
    
    def create_markdown_summary(self, email_text: str) -> str:
        """Create a structured markdown summary of the email content"""
        prompt = f"""
        Analyze the following email and PDF content for order information and create a clear, structured markdown summary.
        
        Please extract and organize the following information:
        
        ## Order Summary
        - **Customer**: [Company name]
        - **Order Number**: [Customer PO number]
        - **Date**: [Order date]
        - **Delivery Address**: [If specified]
        - **Shipping Method**: [If specified]
        
        ## Order Items
        Create a table with the following columns:
        | Stock Code | Description | Quantity | Unit Price | Total |
        
        ## Additional Information
        - Any special instructions or notes
        - Payment terms
        - Delivery requirements
        
        Email/PDF Content:
        {email_text}
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            self.logger.error(f"Error creating markdown summary: {e}")
            return f"Error creating summary: {str(e)}"
    
    def parse_order_data(self, email_text: str) -> Optional[Dict[str, Any]]:
        """Parse order data from email text using LLM"""
        tool_schema = {
            "name": "extract_sales_order_data",
            "description": "Extracts sales order details from text content into a structured JSON format.",
            "parameters": {
                "type_": "OBJECT",
                "properties": {
                    "customer_details": {
                        "type_": "OBJECT",
                        "properties": {
                            "debtor_id": {
                                "type_": "INTEGER",
                                "description": "Customer ID: WOOLWORTHS LIMITED=10981, ENDEAVOUR GROUP=21570"
                            },
                            "customer_order_number": {
                                "type_": "STRING",
                                "description": "Customer's purchase order number"
                            }
                        },
                        "required": ["debtor_id"]
                    },
                    "order_status": {
                        "type_": "INTEGER",
                        "description": "Order status: 0=Not Processed, 3=Quotation. Default to 0."
                    },
                    "delivery_address": {
                        "type_": "OBJECT",
                        "properties": {
                            "line1": {"type_": "STRING"},
                            "line2": {"type_": "STRING"},
                            "line3": {"type_": "STRING"}
                        }
                    },
                    "order_lines": {
                        "type_": "ARRAY",
                        "items": {
                            "type_": "OBJECT",
                            "properties": {
                                "stockcode": {"type_": "STRING"},
                                "orderquantity": {"type_": "NUMBER"}
                            },
                            "required": ["stockcode", "orderquantity"]
                        }
                    },
                    "x_shipvia": {
                        "type_": "STRING",
                        "description": "Shipping method (e.g., 'CAPITAL')"
                    }
                },
                "required": ["customer_details", "order_lines"]
            }
        }
        
        prompt = f"""
        Extract sales order information from the following email/PDF content.
        
        Instructions:
        - Identify the customer and map to correct debtor_id
        - Extract all order line items with stock codes and quantities
        - Extract delivery address if present
        - Extract shipping method if specified
        - Extract customer purchase order number
        
        Content:
        {email_text}
        """
        
        try:
            response = self.model.generate_content(
                prompt,
                tools=[{
                    "function_declarations": [tool_schema]
                }]
            )
            
            if response.candidates and response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call') and part.function_call:
                        args = part.function_call.args
                        return self._convert_gemini_struct(args)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error parsing order data: {e}")
            return None
    
    def _convert_gemini_struct(self, value):
        """Convert Gemini struct to Python dict"""
        if hasattr(value, '__iter__') and not isinstance(value, (str, bytes)) and not hasattr(value, 'items'):
            return [self._convert_gemini_struct(item) for item in value]
        elif hasattr(value, 'items') and callable(value.items):
            return {key: self._convert_gemini_struct(v) for key, v in value.items()}
        return value

# ==============================================================================
# --- MYOB SERVICE ---
# ==============================================================================

class MYOBService:
    """Handles MYOB API operations"""
    
    def __init__(self, config: Config, logger: logging.Logger):
        self.config = config
        self.logger = logger
    
    def validate_sales_order(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Validate sales order with MYOB API"""
        url = f"{self.config.myob_base_url}/salesorder/validate"
        
        try:
            response = requests.post(
                url,
                headers=self.config.myob_headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            self.logger.info("MYOB sales order validation successful")
            return response.json()
        except Exception as e:
            self.logger.error(f"MYOB validation error: {e}")
            raise
    
    def post_sales_order(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Post sales order to MYOB API"""
        url = f"{self.config.myob_base_url}/salesorder/"
        
        try:
            response = requests.post(
                url,
                headers=self.config.myob_headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            self.logger.info("MYOB sales order posted successfully")
            return response.json()
        except Exception as e:
            self.logger.error(f"MYOB posting error: {e}")
            raise
    
    def create_sales_order(self, order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a complete sales order in MYOB"""
        try:
            # Build initial payload
            payload = {
                "debtorid": order_data['customer_details']['debtor_id'],
                "status": order_data.get('order_status', 0),
                "lines": order_data['order_lines']
            }
            
            # Add optional fields
            if order_data['customer_details'].get('customer_order_number'):
                payload["customerordernumber"] = order_data['customer_details']['customer_order_number']
            
            # Add delivery address
            delivery_address = order_data.get('delivery_address')
            if delivery_address and any(delivery_address.values()):
                payload["deliveryaddress"] = {k: v for k, v in delivery_address.items() if v}
            
            # Add shipping method
            if order_data.get('x_shipvia'):
                payload["extrafields"] = [{"key": "X_SHIPVIA", "value": order_data['x_shipvia']}]
            
            self.logger.info(f"Validating order payload: {json.dumps(payload, indent=2)}")
            
            # Validate order
            validated_response = self.validate_sales_order(payload)
            validated_payload = validated_response.get('order')
            
            if not validated_payload:
                raise ValueError("Validation response missing 'order' object")
            
            # Post order
            self.logger.info("Posting validated order to MYOB")
            result = self.post_sales_order(validated_payload)
            
            self.logger.info(f"Successfully created MYOB order with ID: {result.get('id')}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to create MYOB sales order: {e}")
            return None

# ==============================================================================
# --- MAIN PROCESSOR ---
# ==============================================================================

class EmailOrderProcessor:
    """Main orchestration class for the email order processing pipeline"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = setup_logging(config)
        
        # Initialize services
        self.gmail_service = GmailService(config, self.logger)
        self.llm_service = LLMService(config, self.logger)
        self.myob_service = MYOBService(config, self.logger)
        
        self.logger.info("Email Order Processor initialized successfully")
    
    def process_orders(self) -> Dict[str, int]:
        """Main processing workflow"""
        self.logger.info("Starting email order processing workflow")
        
        stats = {
            'emails_processed': 0,
            'orders_approved': 0,
            'orders_denied': 0,
            'orders_skipped': 0,
            'myob_success': 0,
            'myob_failed': 0
        }
        
        try:
            # Step 1: Fetch emails from all configured labels
            all_emails = self._fetch_all_emails()
            
            if not all_emails:
                self.logger.info("No emails found to process")
                return stats
            
            # Step 2: Process each email
            processed_orders = []
            for email_summary in all_emails:
                processed_order = self._process_single_email(email_summary)
                if processed_order:
                    processed_orders.append(processed_order)
                    stats['emails_processed'] += 1
            
            # Step 3: Present orders for approval and process
            if processed_orders:
                self._process_orders_with_approval(processed_orders, stats)
            
            self._log_final_stats(stats)
            return stats
            
        except Exception as e:
            self.logger.error(f"Error in main processing workflow: {e}")
            return stats
    
    def _fetch_all_emails(self) -> List[EmailSummary]:
        """Fetch emails from all configured labels"""
        all_emails = []
        
        for label_name in self.config.GMAIL_LABELS_TO_PROCESS:
            self.logger.info(f"Processing label: {label_name}")
            emails = self.gmail_service.get_emails_from_label(label_name)
            all_emails.extend(emails)
        
        self.logger.info(f"Found {len(all_emails)} total emails to process")
        return all_emails
    
    def _process_single_email(self, email_summary: EmailSummary) -> Optional[ProcessedOrder]:
        """Process a single email to extract order information"""
        self.logger.info(f"Processing email: {email_summary.subject} (ID: {email_summary.id})")
        
        try:
            # Get detailed email content
            email_details = self.gmail_service.get_email_details(email_summary.id)
            if not email_details:
                self.logger.error(f"Could not get details for email {email_summary.id}")
                return None
            
            # Create markdown summary
            self.logger.info("Creating markdown summary...")
            markdown_summary = self.llm_service.create_markdown_summary(email_details.body)
            
            # Parse order data
            self.logger.info("Parsing order data...")
            parsed_data = self.llm_service.parse_order_data(email_details.body)
            
            if not parsed_data:
                self.logger.warning(f"Could not parse order data from email {email_summary.id}")
                return None
            
            # Validate parsed data
            try:
                validated_order = ExtractedOrder(**parsed_data)
                self.logger.info(f"Successfully validated order data for email {email_summary.id}")
            except ValidationError as e:
                self.logger.error(f"Order data validation failed for email {email_summary.id}: {e}")
                return None
            
            return ProcessedOrder(
                email_id=email_summary.id,
                parsed_data=parsed_data,
                markdown_summary=markdown_summary
            )
            
        except Exception as e:
            self.logger.error(f"Error processing email {email_summary.id}: {e}")
            return None
    
    def _process_orders_with_approval(self, processed_orders: List[ProcessedOrder], stats: Dict[str, int]):
        """Present orders for user approval and process approved orders"""
        self.logger.info(f"\n--- Order Approval Process ({len(processed_orders)} orders) ---")
        
        for i, order in enumerate(processed_orders, 1):
            print(f"\n{'='*60}")
            print(f"ORDER {i} of {len(processed_orders)} (Email ID: {order.email_id})")
            print(f"{'='*60}")
            print(order.markdown_summary)
            print(f"\n{'='*60}")
            
            # Get user decision
            while True:
                choice = input("\nApprove this order? (a)pprove / (d)eny / (s)kip: ").lower().strip()
                if choice in ['a', 'd', 's']:
                    order.user_decision = choice
                    break
                print("Please enter 'a', 'd', or 's'")
            
            # Process based on decision
            if choice == 'a':
                stats['orders_approved'] += 1
                self.logger.info(f"Order approved, posting to MYOB...")
                
                # Post to MYOB
                result = self.myob_service.create_sales_order(order.parsed_data)
                if result:
                    stats['myob_success'] += 1
                    self.logger.info("Successfully posted to MYOB, marking email as read")
                    self.gmail_service.mark_email_as_read(order.email_id)
                else:
                    stats['myob_failed'] += 1
                    self.logger.error("Failed to post to MYOB, email remains unread")
            
            elif choice == 'd':
                stats['orders_denied'] += 1
                self.logger.info("Order denied by user, marking email as read")
                self.gmail_service.mark_email_as_read(order.email_id)
            
            elif choice == 's':
                stats['orders_skipped'] += 1
                self.logger.info("Order skipped by user, email remains unread")
    
    def _log_final_stats(self, stats: Dict[str, int]):
        """Log final processing statistics"""
        self.logger.info(f"\n{'='*60}")
        self.logger.info("FINAL PROCESSING STATISTICS")
        self.logger.info(f"{'='*60}")
        self.logger.info(f"Emails processed: {stats['emails_processed']}")
        self.logger.info(f"Orders approved: {stats['orders_approved']}")
        self.logger.info(f"Orders denied: {stats['orders_denied']}")
        self.logger.info(f"Orders skipped: {stats['orders_skipped']}")
        self.logger.info(f"MYOB posts successful: {stats['myob_success']}")
        self.logger.info(f"MYOB posts failed: {stats['myob_failed']}")
        self.logger.info(f"{'='*60}")

# ==============================================================================
# --- MAIN EXECUTION ---
# ==============================================================================

def main():
    """Main entry point"""
    try:
        # Load configuration
        config = Config()
        
        # Initialize processor
        processor = EmailOrderProcessor(config)
        
        # Process orders
        stats = processor.process_orders()
        
        print(f"\nProcessing complete. Check logs for details.")
        print(f"Summary: {stats['myob_success']} orders posted successfully to MYOB")
        
    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("Please check your .env file and ensure all required variables are set.")
    except FileNotFoundError as e:
        print(f"File Error: {e}")
        print("Please ensure credentials.json is present in the project directory.")
    except Exception as e:
        print(f"Unexpected Error: {e}")
        print("Check the logs for more details.")

if __name__ == "__main__":
    main()
