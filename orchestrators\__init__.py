"""
Orchestrators package - High-level workflow coordination and system management.

This package contains the main orchestration logic that coordinates between
different services and agents to accomplish complex workflows.

Modules:
    comprehensive_email_system: Main system orchestrator
    main_processor: Core email processing orchestrator
    email_order_processor: Email-to-order processing pipeline
    enhanced_email_processor: Enhanced processing with dashboard integration
    email_dashboard: Web dashboard orchestrator
"""

try:
    from .comprehensive_email_system import ComprehensiveEmailSystem
    from .main_processor import EmailOrderProcessor
    from .email_order_processor import EmailOrderProcessor  
    from .enhanced_email_processor import EnhancedEmailProcessor
    from .email_dashboard import EmailDashboard
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
    from orchestrators.comprehensive_email_system import ComprehensiveEmailSystem
    from orchestrators.main_processor import EmailOrderProcessor
    from orchestrators.email_order_processor import EmailOrderProcessor
    from orchestrators.enhanced_email_processor import EnhancedEmailProcessor
    from orchestrators.email_dashboard import EmailDashboard

__all__ = [
    'ComprehensiveEmailSystem',
    'EmailProcessor', 
    'EmailOrderProcessor',
    'EnhancedEmailProcessor',
    'EmailDashboard'
]