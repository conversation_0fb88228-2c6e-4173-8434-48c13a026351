#!/usr/bin/env python3
"""
Fix import issues in the restructured project by adding proper path handling.
"""
import os
import sys

def fix_orchestrator_imports():
    """Fix imports in orchestrator files by adding sys.path handling."""
    
    files_to_fix = [
        "orchestrators/comprehensive_email_system.py",
        "orchestrators/main_processor.py", 
        "orchestrators/enhanced_email_processor.py",
        "orchestrators/email_dashboard.py",
        "agents/enhanced_universal_agent.py",
        "agents/continuous_polling_agent.py"
    ]
    
    path_fix = """
# Add project root to Python path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
"""
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"Fixing imports in {file_path}...")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if path fix already exists
            if "sys.path.insert(0," not in content:
                # Find the first import statement and add path fix before it
                lines = content.split('\n')
                insert_index = 0
                
                for i, line in enumerate(lines):
                    if line.strip().startswith('from ') and ('agents.' in line or 'services.' in line or 'utils.' in line or 'orchestrators.' in line):
                        insert_index = i
                        break
                
                if insert_index > 0:
                    lines.insert(insert_index, path_fix)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(lines))
                    
                    print(f"  ✅ Fixed {file_path}")
                else:
                    print(f"  ⚠️  No package imports found in {file_path}")
            else:
                print(f"  ✅ Already fixed {file_path}")

def create_simple_entry_points():
    """Create simple entry points that work without complex imports."""
    
    # Create a simple system starter
    simple_system = '''#!/usr/bin/env python3
"""Simple system starter that works with the restructured project."""
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_dashboard():
    """Start the email dashboard."""
    try:
        from orchestrators.email_dashboard import EmailDashboard
        print("🚀 Starting Email Dashboard...")
        dashboard = EmailDashboard()
        dashboard.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Failed to start dashboard: {e}")
        import traceback
        traceback.print_exc()

def start_demo_dashboard():
    """Start demo dashboard."""
    try:
        print("🚀 Starting Demo Dashboard...")
        import demo_dashboard
    except Exception as e:
        print(f"❌ Failed to start demo dashboard: {e}")
        import traceback
        traceback.print_exc()

def start_main_processor():
    """Start main processor."""
    try:
        from orchestrators.main_processor import EmailOrderProcessor
        print("🚀 Starting Main Processor...")
        processor = EmailOrderProcessor()
        processor.run()
    except Exception as e:
        print(f"❌ Failed to start processor: {e}")
        import traceback
        traceback.print_exc()

def test_imports():
    """Test that all imports work."""
    print("🧪 Testing imports...")
    
    tests = [
        ("utils.config", "config"),
        ("utils.models", "EmailData"),
        ("services.gmail_service", "GmailService"),
        ("services.llm_service", "LLMService"),
        ("services.myob_service", "MyobService"),
        ("orchestrators.email_dashboard", "EmailDashboard"),
    ]
    
    passed = 0
    for module_name, class_name in tests:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
            passed += 1
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name}: {e}")
    
    print(f"\\n📊 Import test results: {passed}/{len(tests)} passed")
    return passed == len(tests)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Simple TeamsysV0.1 Launcher')
    parser.add_argument('action', choices=['dashboard', 'demo', 'processor', 'test'], 
                       help='Action to perform')
    
    args = parser.parse_args()
    
    if args.action == 'dashboard':
        start_dashboard()
    elif args.action == 'demo':
        start_demo_dashboard()
    elif args.action == 'processor':
        start_main_processor()
    elif args.action == 'test':
        test_imports()
'''
    
    with open('simple_launcher.py', 'w', encoding='utf-8') as f:
        f.write(simple_system)
    
    print("✅ Created simple_launcher.py")

def main():
    """Main function to fix all import issues."""
    print("🔧 Fixing import issues in restructured project...")
    print("=" * 60)
    
    # Step 1: Fix imports in main files
    fix_orchestrator_imports()
    
    # Step 2: Create simple entry points
    create_simple_entry_points()
    
    print("\\n" + "=" * 60)
    print("🎉 Import fixes completed!")
    print("\\n🧪 Test the fixes:")
    print("   python simple_launcher.py test")
    print("\\n🚀 Start components:")
    print("   python simple_launcher.py demo")
    print("   python simple_launcher.py dashboard")
    print("   python simple_launcher.py processor")

if __name__ == "__main__":
    main()