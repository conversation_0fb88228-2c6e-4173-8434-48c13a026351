# Dynamic Label Selection Guide

## Overview

The Email Order Processor now supports dynamic label selection, allowing you to process emails from multiple Gmail labels with flexible configuration options. This guide explains how to use the new features.

## Features

### 🎯 Interactive Label Selection
- Choose labels interactively from a user-friendly menu
- See only labels that contain emails with PDF attachments
- Support for single, multiple, or all label selection

### 🔧 Command-Line Control
- Specify labels directly via command-line arguments
- Non-interactive mode for automation
- Dry-run mode to preview what would be processed

### ⚙️ Label-Specific Configuration
- Different settings for each label (date filters, max results, etc.)
- Automatic packing slip detection and handling
- Customizable query filters per label

### 🌍 Environment Variable Support
- Override labels at runtime using environment variables
- Perfect for CI/CD and automated deployments

## Usage Examples

### Interactive Mode (Default)
```bash
python main_processor.py
```
This will show you an interactive menu to select labels:
```
📧 Available processing-ready (with PDF attachments) labels for processing:
============================================================
 1. Brady
 2. RSEA
 3. Woolworths

💡 Options:
   • Enter label numbers (comma-separated): 1,3,5
   • Enter 'all' to process all labels
   • Enter 'brady' for Brady-only (backward compatibility)
   • Enter 'show-all' to see all user labels
   • Enter 'quit' to exit

🔍 Your selection: 1,2
✅ Selected 2 label(s): Brady, RSEA
```

### Command-Line Label Selection
```bash
# Process specific labels
python main_processor.py --labels Brady,RSEA,Woolworths

# Process all available labels
python main_processor.py --labels all

# Brady-only mode (backward compatibility)
python main_processor.py --brady-only
```

### Non-Interactive Mode
```bash
# Use environment variables or defaults
python main_processor.py --non-interactive

# With specific labels
python main_processor.py --labels Brady,RSEA --non-interactive
```

### Dry Run Mode
```bash
# See what would be processed without actually processing
python main_processor.py --dry-run --labels all
```

### Information Commands
```bash
# Show all available labels
python main_processor.py --show-labels

# Validate configuration
python main_processor.py --validate-config
```

## Environment Variables

### Runtime Label Override
```bash
# Set labels via environment variable
export RUNTIME_LABELS_TO_PROCESS="Brady,RSEA,Woolworths"
python main_processor.py --non-interactive
```

### Configuration Options
```bash
# Existing configuration variables still work
export GMAIL_LABELS_TO_PROCESS="Brady,RSEA"
export MAX_GMAIL_RESULTS=50
export GMAIL_QUERY_FILTER="has:attachment filename:pdf"
```

## Label-Specific Configuration

The system includes pre-configured settings for common labels:

### Brady (Main Supplier)
- **Date Filter**: None (processes all emails)
- **Max Results**: 100
- **Packing Slips**: Automatically skipped
- **Description**: Brady Corporation - Main supplier processing

### RSEA (Safety Equipment)
- **Date Filter**: after:2024/01/01
- **Max Results**: 10
- **Packing Slips**: Processed normally
- **Description**: RSEA Safety - Safety equipment supplier

### Other Labels
- **Date Filter**: after:2024/01/01
- **Max Results**: 10
- **Packing Slips**: Processed normally
- **Description**: Auto-generated based on label name

## Advanced Usage

### Custom Label Configuration

To add or modify label-specific settings, edit the `LABEL_SPECIFIC_SETTINGS` in `config.py`:

```python
LABEL_SPECIFIC_SETTINGS = {
    'your_label': {
        'date_filter': 'after:2024/06/01',
        'max_results': 20,
        'skip_packing_slips': True,
        'query_filter': 'has:attachment filename:pdf',
        'description': 'Your Label - Custom description'
    }
}
```

### Query Customization

The system builds Gmail queries automatically based on label settings:

```python
# Example generated queries:
# Brady: "has:attachment filename:pdf -label:processed"
# RSEA: "has:attachment filename:pdf after:2024/01/01 -label:processed"
```

## Backward Compatibility

The system maintains full backward compatibility:

- **Default Behavior**: If no labels are specified, defaults to Brady-only processing
- **Existing Scripts**: All existing automation scripts continue to work
- **Configuration**: Existing environment variables are still respected

## Error Handling

### Invalid Labels
If you specify a label that doesn't exist:
```
⚠️  Warning: Label 'NonExistent' not found in your Gmail account
🔄 No valid labels found. Falling back to Brady.
```

### No Processing-Ready Labels
If no labels contain emails with PDF attachments:
```
❌ No processing-ready (with PDF attachments) labels found in your Gmail account.
Please ensure you have labels with emails containing PDF attachments.
```

### Configuration Issues
Use the validation command to check your setup:
```bash
python main_processor.py --validate-config
```

## Best Practices

### 1. Start with Dry Run
Always test with `--dry-run` first to see what would be processed:
```bash
python main_processor.py --dry-run --labels all
```

### 2. Use Specific Labels for Production
Instead of processing all labels, specify exactly what you need:
```bash
python main_processor.py --labels Brady,RSEA --non-interactive
```

### 3. Monitor Processing Labels
The system automatically creates and manages processing status labels:
- 🟢 **Processed**: Successfully processed emails
- 🟡 **Review**: Emails that need manual review (e.g., packing slips)
- 🔴 **Failed**: Emails that failed processing

### 4. Environment-Specific Configuration
Use environment variables for different environments:

**Development:**
```bash
export RUNTIME_LABELS_TO_PROCESS="Brady"
export MAX_GMAIL_RESULTS=5
```

**Production:**
```bash
export RUNTIME_LABELS_TO_PROCESS="Brady,RSEA,Woolworths"
export MAX_GMAIL_RESULTS=100
```

## Troubleshooting

### Common Issues

1. **"No labels selected"**
   - Ensure you have Gmail labels with emails containing PDF attachments
   - Check that your Gmail credentials are valid

2. **"Label not found"**
   - Verify the label exists in your Gmail account
   - Check spelling and case sensitivity

3. **"No processing-ready labels"**
   - Ensure your labels contain emails with PDF attachments
   - Check that emails aren't already processed (have processing labels)

### Getting Help

Use the built-in help:
```bash
python main_processor.py --help
```

Check available labels:
```bash
python main_processor.py --show-labels
```

Validate your configuration:
```bash
python main_processor.py --validate-config
```

## Migration from Previous Version

If you're upgrading from the Brady-only version:

1. **No Changes Required**: The system defaults to Brady-only processing
2. **Gradual Migration**: Start by adding one additional label at a time
3. **Test Thoroughly**: Use dry-run mode to verify behavior before processing

### Example Migration Path:
```bash
# Week 1: Test with Brady only (existing behavior)
python main_processor.py --brady-only

# Week 2: Add RSEA in dry-run mode
python main_processor.py --dry-run --labels Brady,RSEA

# Week 3: Process Brady and RSEA
python main_processor.py --labels Brady,RSEA

# Week 4: Add more labels as needed
python main_processor.py --labels Brady,RSEA,Woolworths
```

This gradual approach ensures a smooth transition while maintaining system reliability.
