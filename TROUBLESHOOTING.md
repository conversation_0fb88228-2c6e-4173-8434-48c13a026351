# Troubleshooting Guide - TeamsysV0.1

## Common Issues and Solutions

### Gmail API Issues

#### Authentication Errors

**Problem**: `google.auth.exceptions.RefreshError: The credentials do not contain the necessary fields`

**Solution**:
```bash
# Delete existing token and re-authenticate
rm token.pickle
python gmail_service.py
# Follow the browser authentication flow
```

**Problem**: `FileNotFoundError: credentials.json not found`

**Solution**:
1. Download credentials from Google Cloud Console
2. Place file in project root as `credentials.json`
3. Ensure file has correct OAuth 2.0 client configuration

#### Permission Errors

**Problem**: `HttpError 403: Insufficient Permission`

**Solution**:
1. Check Gmail API is enabled in Google Cloud Console
2. Verify OAuth scopes in credentials configuration
3. Re-authenticate with broader permissions:
```python
# In config.py, ensure all required scopes:
GMAIL_SCOPES = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.modify'
]
```

#### Rate Limiting

**Problem**: `HttpError 429: Rate Limit Exceeded`

**Solution**:
1. Implement exponential backoff (already included in GmailService)
2. Reduce `MAX_GMAIL_RESULTS` in configuration
3. Increase delays between API calls
4. Monitor quota usage in Google Cloud Console

### Gemini AI Issues

#### API Key Errors

**Problem**: `google.api_core.exceptions.Unauthenticated: Request is missing required authentication credential`

**Solution**:
1. Verify `GEMINI_API_KEY` in `.env` file
2. Check API key is active in Google AI Studio
3. Ensure billing is enabled for the project

**Problem**: `google.api_core.exceptions.PermissionDenied: API key not valid`

**Solution**:
1. Generate new API key in Google AI Studio
2. Update `.env` file with new key
3. Restart the application

#### Model Errors

**Problem**: `ValueError: Model not found: gemini-2.0-flash-exp`

**Solution**:
1. Check available models in Google AI Studio
2. Update `GEMINI_MODEL` in `.env` file:
```env
GEMINI_MODEL=gemini-1.5-flash
```

#### Content Processing Errors

**Problem**: AI fails to extract order data consistently

**Solution**:
1. Check email content quality and format
2. Review processing rules in `processing_rules.yaml`
3. Add more context to memory database:
```python
# Add successful examples to memory
memory_client.add_memory("successful_order_example", content, metadata)
```

### MYOB API Issues

#### Connection Errors

**Problem**: `requests.exceptions.ConnectionError: Failed to establish a new connection`

**Solution**:
1. Verify MYOB server is accessible:
```bash
ping your_myob_server_ip
telnet your_myob_server_ip your_port
```
2. Check firewall settings
3. Verify VPN connection if required
4. Test with MYOB API browser interface

#### Authentication Errors

**Problem**: `HTTPError 401: Unauthorized`

**Solution**:
1. Verify credentials in `.env` file:
```env
EXO_IP=*************
EXO_PORT=8080
USER=your_username
PWD=your_password
API_KEY=your_api_key
EXO_TOK=your_token
```
2. Check user permissions in MYOB EXO
3. Verify API access is enabled for the user

#### Validation Errors

**Problem**: `HTTPError 400: Bad Request` during order validation

**Solution**:
1. Check order payload structure:
```python
# Minimal required fields
{
    "debtorid": 6207,
    "status": 3,
    "lines": [
        {
            "stockcode": "ABC123",
            "orderquantity": 1.0
        }
    ]
}
```
2. Verify stock codes exist in MYOB inventory
3. Check customer/debtor ID is valid
4. Ensure required fields are present

### Dashboard Issues

#### Port Already in Use

**Problem**: `OSError: [Errno 48] Address already in use`

**Solution**:
```bash
# Find process using port 5000
lsof -i :5000
# Kill the process
kill -9 <process_id>
# Or use different port
python demo_dashboard.py --port 5001
```

#### Database Errors

**Problem**: `sqlite3.OperationalError: database is locked`

**Solution**:
1. Close all dashboard instances
2. Remove lock file:
```bash
rm email_dashboard.db-wal
rm email_dashboard.db-shm
```
3. Restart dashboard

#### Sync Issues

**Problem**: Dashboard not showing latest emails

**Solution**:
1. Click "Sync Emails" button in dashboard
2. Check Gmail API connectivity
3. Verify label configuration
4. Clear browser cache and refresh

### Configuration Issues

#### Environment Variables

**Problem**: `ValueError: Missing one or more MYOB API configuration variables`

**Solution**:
1. Copy template and fill in values:
```bash
cp env_template.txt .env
# Edit .env with your actual credentials
```
2. Verify all required variables are set:
```bash
python -c "from config import config; config.validate_config()"
```

#### File Permissions

**Problem**: `PermissionError: [Errno 13] Permission denied`

**Solution**:
```bash
# Fix file permissions
chmod 644 .env
chmod 755 *.py
# Ensure directories are writable
chmod 755 markdown/ myob/ logs/
```

### Processing Issues

#### PDF Extraction Failures

**Problem**: `fitz.FileDataError: cannot open document`

**Solution**:
1. Verify PDF file is not corrupted
2. Check PDF is not password protected
3. Update PyMuPDF library:
```bash
pip install --upgrade PyMuPDF
```

#### Memory Database Issues

**Problem**: `chromadb.errors.InvalidCollectionException`

**Solution**:
1. Reset ChromaDB:
```bash
rm -rf chroma_db/
python memory_client.py  # Reinitialize
```
2. Check disk space for database storage

#### Email Processing Stuck

**Problem**: Processing hangs on specific emails

**Solution**:
1. Check logs for specific error:
```bash
tail -f logs/system.log
```
2. Skip problematic email:
```python
# Add email ID to skip list in configuration
SKIP_EMAIL_IDS = ["problematic_email_id"]
```
3. Process emails individually for debugging

### Performance Issues

#### Slow Processing

**Problem**: Email processing takes too long

**Solution**:
1. Reduce batch size in configuration:
```json
{
    "batch_size": 5,
    "max_workers": 2
}
```
2. Optimize AI prompts for faster processing
3. Use faster Gemini model:
```env
GEMINI_MODEL=gemini-1.5-flash
```

#### Memory Usage

**Problem**: High memory consumption

**Solution**:
1. Reduce `MAX_GMAIL_RESULTS`
2. Clear processed emails from memory
3. Restart system periodically
4. Monitor with:
```bash
python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"
```

## Debugging Tools

### Log Analysis

#### Enable Debug Logging
```env
LOG_LEVEL=DEBUG
```

#### View Specific Component Logs
```bash
# Gmail service logs
grep "GmailService" logs/system.log

# LLM service logs  
grep "LLMService" logs/system.log

# MYOB service logs
grep "MyobService" logs/system.log
```

### Test Scripts

#### Test Individual Components
```bash
# Test Gmail connectivity
python test_gmail_service.py

# Test LLM processing
python demo_llm.py

# Test MYOB connectivity
python test_myob_service.py

# Test complete system
python test_system.py
```

#### Test Specific Orders
```bash
# Test Brady order processing
python test_brady_specific.py

# Test debtor mapping
python test_debtor_mapping.py
```

### System Status

#### Check System Health
```bash
# System status
python comprehensive_email_system.py --status

# Component status
python -c "
from gmail_service import GmailService
from llm_service import LLMService
from myob_service import MyobService

try:
    gmail = GmailService()
    print('✅ Gmail: Connected')
except Exception as e:
    print(f'❌ Gmail: {e}')

try:
    llm = LLMService()
    print('✅ LLM: Connected')
except Exception as e:
    print(f'❌ LLM: {e}')

try:
    myob = MyobService()
    print('✅ MYOB: Connected')
except Exception as e:
    print(f'❌ MYOB: {e}')
"
```

## Getting Help

### Log Collection

When reporting issues, collect these logs:
```bash
# System logs
cp logs/system.log issue_logs/
cp logs/gmail_service.log issue_logs/
cp logs/llm_service.log issue_logs/
cp logs/myob_service.log issue_logs/

# Configuration (remove sensitive data)
cp .env issue_logs/env_sanitized.txt
# Edit to remove passwords/keys

# System information
python -c "
import sys, platform
print(f'Python: {sys.version}')
print(f'Platform: {platform.platform()}')
print(f'Architecture: {platform.architecture()}')
" > issue_logs/system_info.txt
```

### Support Checklist

Before seeking help:
- [ ] Checked this troubleshooting guide
- [ ] Reviewed relevant log files
- [ ] Tested individual components
- [ ] Verified configuration settings
- [ ] Tried suggested solutions
- [ ] Collected debug information

### Common Solutions Summary

| Issue Type | Quick Fix | Full Solution |
|------------|-----------|---------------|
| Gmail Auth | `rm token.pickle` | Re-authenticate with proper scopes |
| MYOB Connection | Check IP/port | Verify network, credentials, permissions |
| AI Processing | Check API key | Verify quota, model availability |
| Dashboard | Restart service | Check port, database, permissions |
| Performance | Reduce batch size | Optimize configuration, resources |
| Configuration | Validate `.env` | Use setup script, check all variables |