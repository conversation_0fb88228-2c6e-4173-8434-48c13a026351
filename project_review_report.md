============================================================
Project Module Review and Redundancy Analysis
============================================================

Date Generated: July 2, 2025

------------------------------------------------------------
Module Breakdown:
------------------------------------------------------------

1.  **Configuration & Utilities:**
    *   `config.py`: Manages application configuration by loading environment variables from a `.env` file. It provides constants for Gmail API, Gemini LLM, MYOB API credentials, and logging. It's a foundational module used by almost all other components.

2.  **`customermap.py`**:
    *   **Purpose:** Defines data structures for mapping customer names to their corresponding `debtor_id` in MYOB and for defining rules to transform stock codes. It includes an `OrderStatus` enum and `CustomerMapping` and `StockcodeMapper` classes. Used by the LLM parsing logic (likely within `llm_service.py`) to map customer names to IDs and apply stock code transformations.

3.  **`demo_dashboard.py`**:
    *   **Purpose:** A demonstration script for the `EmailDashboard`. It creates sample data in an SQLite database and then launches the dashboard application.
    *   **Connections:** Imports and uses `EmailDashboard` and `GmailService`. It interacts with the database for demo data.

4.  **`demo_llm.py`**:
    *   **Purpose:** Demonstrates the capabilities of the `LLMService` by testing markdown summary generation, order data extraction from email/PDF content, and MYOB payload generation.
    *   **Connections:** Imports `LLMService`, `models.ExtractedOrder`, and uses sample data. It relies on `GEMINI_API_KEY` from `config`.

5.  **`demo_myob_poster.py`**:
    *   **Purpose:** Showcases the `MYOBPoster` class for order review, data validation, and batch processing information for MYOB interactions.
    *   **Connections:** Imports `MYOBPoster`, `batch_post_orders`, and `interactive_mode`. It interacts with the file system to list pending orders.

6.  **`email_dashboard.py`**:
    *   **Purpose:** Implements a Flask web application that provides a dashboard for managing and processing emails. It includes features for syncing emails from Gmail, displaying them in a table, filtering, searching, batch processing, and showing statistics.
    *   **Connections:** Integrates with `GmailService` for email fetching and status updates, `EmailOrderProcessor` for processing logic, and uses an SQLite database (`email_dashboard.db`) for storing email data and stats. It also includes an embedded HTML template (`templates/dashboard.html`).

7.  **`email_labeling_demo.py`**:
    *   **Purpose:** Demonstrates how to use `GmailService` to create and manage "traffic light" labels (Processed, Review, Failed) in Gmail for email status tracking.
    *   **Connections:** Imports and uses `GmailService`.

8.  **`email_order_processor.py`**:
    *   **Purpose:** Orchestrates the core pipeline: fetching emails via `GmailService` to parsing with `LLMService` and creating MYOB payloads. It includes an interactive CLI for user approval before posting to MYOB.
    *   **Connections:** Heavily relies on `Config`, `GmailService`, `LLMService`, `MYOBService`, `models` (for data structures), and `pdf_extractor`.

9.  **`enhanced_email_processor.py`**:
    *   **Purpose:** Enhances the `EmailOrderProcessor` by integrating it with the `EmailDashboard`. It processes emails, updates the dashboard's database, and sends an enhanced completion email with dashboard statistics and links.
    *   **Connections:** Imports `EmailOrderProcessor`, `EmailDashboard`, `GmailService`, and `send_completion_email`.

10. **`exo_api.py`**:
    *   **Purpose:** A utility script to fetch discovery data from an EXO API endpoint and save it. It makes HTTP requests to a specified IP address and port.
    *   **Connections:** Uses `requests` library and interacts with the file system to save data.

11. **`gmail_context.py`**:
    *   **Purpose:** Extracts richer context from emails (including thread and sender history) for LLM processing, saving this enhanced context to files.
    *   **Connections:** Imports `GmailService`, `models.EmailData`, `config`, `LLMService` (implicitly via `PersistentMemoryClient`), and `PersistentMemoryClient`. It handles attachment downloads.

12. **`gmail_service.py`**:
    *   **Purpose:** Provides core functionalities for interacting with the Gmail API, including authentication, fetching emails by label/query, extracting email details and attachments, managing labels (including custom ones with colors), and marking emails.
    *   **Connections:** Relies on `config` for credentials, `models.EmailData` for data structures, `fitz` (PyMuPDF) for PDF text extraction (via `_extract_pdf_text`), `BeautifulSoup` for HTML parsing, and Google API client libraries.

13. **`llm_service.py`**:
    *   **Purpose:** Manages interactions with the Gemini LLM for tasks like generating markdown summaries, extracting structured order data (using tool calls and specific rules), and generating MYOB payloads. It also integrates with `PersistentMemoryClient` for context retrieval.
    *   **Connections:** Imports `config`, `models.ExtractedOrder`, `PersistentMemoryClient`, and `google.generativeai`.

14. **`main_processor.py`**:
    *   **Purpose:** Acts as a standalone script for the email processing pipeline (fetching, LLM parsing, MYOB creation) with CLI approval. It orchestrates the workflow and handles user interaction for approval.
    *   **Connections:** Imports various services and models.

15. **`memory_client.py`**:
    *   **Purpose:** Provides a client for interacting with ChromaDB, enabling storage and retrieval of "memories" (contextual data) for LLM use.
    *   **Connections:** Uses the `chromadb` library.

16. **`models.py`**:
    *   **Purpose:** Defines Pydantic data models (`CustomerDetails`, `DeliveryAddress`, `OrderLine`, `ExtractedOrder`, `EmailData`, `ProcessedOrder`) for structuring and validating data throughout the application.
    *   **Connections:** Used across multiple modules.

17. **`myob_poster.py`**:
    *   **Purpose:** An enhanced MYOB poster with order review, validation (structural and API-based), batch posting, and interactive CLI features.
    *   **Connections:** Imports `MyobService`, `config`, and uses file system operations.

18. **`myob_poster_old.py`**:
    *   **Purpose:** Appears to be a legacy or older version of the MYOB posting logic.
    *   **Recommendation:** Potentially redundant; consider removing if fully superseded by `myob_poster.py` and `myob_service.py`.

19. **`myob_service.py`**:
    *   **Purpose:** Encapsulates interactions with the MYOB EXO API for order validation and creation.
    *   **Connections:** Uses `requests` and `config`.

20. **`script.py`**:
    *   **Purpose:** A main integrated pipeline script combining Gmail fetching, LLM parsing, MYOB creation, and CLI approval. Acts as a primary driver for the end-to-end workflow.
    *   **Connections:** Imports and utilizes `GmailFetcher`, `pdf_extractor`, `LLMService`, `MYOBService`, and `ExtractedOrder` (from `models`).

21. **`setup.py`**:
    *   **Purpose:** Utility script for initial project setup: environment file creation, credential checks, dependency installation, and environment variable validation.
    *   **Connections:** Uses `shutil`, `subprocess`, `dotenv`, `os`, `pathlib`, and `config`.

22. **`supabase.py`**:
    *   **Purpose:** Setup script for Supabase database connection, including credential management and connection testing.
    *   **Connections:** Imports `create_client`, `Client` from `supabase`, `config`, and `EmailSummaryProcessor`.

23. **`test_brady_order.py`**:
    *   **Purpose:** Test script for MYOB API interaction with Brady orders (minimal payload).
    *   **Connections:** Imports `MyobService`.

24. **`test_brady_specific.py`**:
    *   **Purpose:** Tests a specific real Brady order against MYOB API validation and creation.
    *   **Recommendation:** Consolidate testing; prioritize this test due to its use of actual data.

------------------------------------------------------------
Redundancy Analysis & Recommendations:
------------------------------------------------------------

1.  **`myob_poster_old.py`**:
    *   **Purpose:** Legacy MYOB posting logic.
    *   **Recommendation:** Likely redundant. Consider removing if `myob_poster.py` and `myob_service.py` cover all necessary functionality.

2.  **Demo Scripts (`demo_*`)**:
    *   **Purpose:** Showcase specific features.
    *   **Recommendation:** Keep for documentation/testing, but ensure they don't interfere with main execution.

3.  **`script.py` vs. `main_processor.py`**:
    *   **Purpose:** `script.py` is a comprehensive pipeline with CLI approval; `main_processor.py` is extraction-only.
    *   **Recommendation:** Clarify roles. If `script.py` is the main driver, consider refactoring `main_processor.py` into a library module used by `script.py`.

4.  **Test Scripts (`test_brady_*`)**:
    *   **Purpose:** Test MYOB interactions for Brady orders.
    *   **Recommendation:** Consolidate testing or prioritize `test_brady_specific.py` due to its use of actual data.

------------------------------------------------------------
End of Report
============================================================