"""
Test script to verify debtor ID mappings for all customers.
"""
from llm_service import LLMService

# Test data for each customer type
test_orders = [
    {
        "name": "WOOLWORTHS",
        "content": """
        From: <EMAIL>
        Subject: Purchase Order LVA4401196688
        
        WOOLWORTHS LIMITED
        Order: LVA4401196688
        Product: Orange Juice - Qty: 100
        """
    },
    {
        "name": "ENDEAVOUR GROUP", 
        "content": """
        From: <EMAIL>
        Subject: Purchase Order DTS4401193941
        
        ENDEAVOUR GROUP
        Order: DTS4401193941
        Product: Apple Juice - Qty: 50
        """
    },
    {
        "name": "RSEA",
        "content": """
        From: <EMAIL>
        Subject: Purchase Order RS123456
        
        RSEA
        Order: RS123456
        Product: Safety Equipment - Qty: 25
        """
    },
    {
        "name": "BRADY",
        "content": """
        From: <EMAIL>
        Subject: Purchase Order BR789012
        
        BRADY
        Order: BR789012
        Product: Industrial Labels - Qty: 10
        """
    }
]

expected_debtor_ids = {
    "WOOLWORTHS": 10981,
    "ENDEAVOUR GROUP": 21570, 
    "RSEA": 6207,
    "BRADY": 5760
}

def test_debtor_mappings():
    """Test that each customer maps to the correct debtor ID."""
    print("🧪 TESTING DEBTOR ID MAPPINGS")
    print("=" * 50)
    
    try:
        llm_service = LLMService()
        
        for test_case in test_orders:
            customer_name = test_case["name"]
            content = test_case["content"]
            expected_id = expected_debtor_ids[customer_name]
            
            print(f"\n🔍 Testing {customer_name}...")
            
            # Extract order data
            extracted_data = llm_service.extract_order_data(content)
            
            if extracted_data and "customer_details" in extracted_data:
                actual_id = extracted_data["customer_details"].get("debtor_id")
                
                if actual_id == expected_id:
                    print(f"✅ {customer_name}: Correct mapping ({actual_id})")
                else:
                    print(f"❌ {customer_name}: Wrong mapping (got {actual_id}, expected {expected_id})")
            else:
                print(f"❌ {customer_name}: Failed to extract data")
        
        print("\n" + "=" * 50)
        print("Debtor ID mapping test completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")

if __name__ == "__main__":
    test_debtor_mappings()
