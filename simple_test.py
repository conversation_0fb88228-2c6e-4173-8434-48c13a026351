#!/usr/bin/env python3
"""Simple test to verify the restructured system works."""

print("🧪 Testing Restructured TeamsysV0.1 System")
print("=" * 50)

# Test 1: Basic Python
print("✅ Python is working")

# Test 2: Package imports
try:
    from orchestrators import ComprehensiveEmailSystem
    print("✅ Orchestrators package imported successfully")
except Exception as e:
    print(f"❌ Orchestrators package failed: {e}")

try:
    from agents import EnhancedUniversalAgent
    print("✅ Agents package imported successfully")
except Exception as e:
    print(f"❌ Agents package failed: {e}")

try:
    from services import GmailService, LLMService
    print("✅ Services package imported successfully")
except Exception as e:
    print(f"❌ Services package failed: {e}")

try:
    from utils import config, EmailData
    print("✅ Utils package imported successfully")
except Exception as e:
    print(f"❌ Utils package failed: {e}")

# Test 3: Data models
try:
    from utils.models import ExtractedOrder, CustomerDetails, OrderLine
    
    # Create test order
    order = ExtractedOrder(
        customer_details=CustomerDetails(debtor_id=6207),
        order_lines=[OrderLine(stockcode="TEST", orderquantity=1.0)]
    )
    print("✅ Data models working correctly")
except Exception as e:
    print(f"❌ Data models failed: {e}")

print("\n🎉 Basic tests completed!")
print("If you see this message, the restructured system is working.")