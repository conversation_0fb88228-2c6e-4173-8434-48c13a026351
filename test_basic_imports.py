#!/usr/bin/env python3
"""Basic import test without packages - direct file imports."""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🧪 Testing Basic Imports (Direct File Access)")
print("=" * 50)

# Test 1: Direct file imports
try:
    # Import config directly
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'utils'))
    import config
    print("✅ utils/config.py imported successfully")
    print(f"   Gmail model: {config.config.GEMINI_MODEL}")
except Exception as e:
    print(f"❌ utils/config.py failed: {e}")

try:
    # Import models directly
    import models
    print("✅ utils/models.py imported successfully")
    
    # Test creating a model
    customer = models.CustomerDetails(debtor_id=6207)
    print(f"   Created customer with ID: {customer.debtor_id}")
except Exception as e:
    print(f"❌ utils/models.py failed: {e}")

try:
    # Import gmail service directly
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services'))
    import gmail_service
    print("✅ services/gmail_service.py imported successfully")
except Exception as e:
    print(f"❌ services/gmail_service.py failed: {e}")

try:
    # Import llm service directly
    import llm_service
    print("✅ services/llm_service.py imported successfully")
except Exception as e:
    print(f"❌ services/llm_service.py failed: {e}")

try:
    # Import myob service directly
    import myob_service
    print("✅ services/myob_service.py imported successfully")
except Exception as e:
    print(f"❌ services/myob_service.py failed: {e}")

print("\n🎯 Testing Service Initialization")
print("-" * 30)

try:
    # Test MYOB service initialization
    myob = myob_service.MyobService()
    print("✅ MyobService initialized successfully")
except Exception as e:
    print(f"❌ MyobService initialization failed: {e}")

try:
    # Test LLM service initialization  
    llm = llm_service.LLMService()
    print("✅ LLMService initialized successfully")
except Exception as e:
    print(f"❌ LLMService initialization failed: {e}")

print("\n🎉 Basic import test completed!")
print("If services initialized successfully, the core functionality is working.")