{"email": {"id": "197c9c27260211cf", "headers": {"Return-Path": "<<EMAIL>>", "Received": "from MYOBEXOW6 (61-68-27-250.static.tpgi.com.au. [************])        by smtp.gmail.com with ESMTPSA id d9443c01a7336-23acb3ba060sm127384345ad.197.2025.***********.51        (version=TLS1_2 cipher=ECDHE-ECDSA-AES128-GCM-SHA256 bits=128/128);        Tue, 01 Jul 2025 23:10:53 -0700 (PDT)", "From": "<PERSON> <<EMAIL>>", "To": "<<EMAIL>>", "Cc": "\"'<PERSON>'\" <<PERSON><PERSON>@bradycorp.com>", "Subject": "Invoice 167395/01 from TEAM SYSTEMS (VIC) PTY LTD", "Date": "Wed, 2 Jul 2025 16:10:48 +1000", "Message-ID": "<017401dbeb18$10630970$31291c50$@teamsystems.net.au>", "MIME-Version": "1.0", "Content-Type": "multipart/mixed; boundary=\"----=_NextPart_000_0175_01DBEB6B.E20FDCC0\"", "X-Mailer": "Microsoft Outlook 16.0", "Thread-Index": "AdvrGA4YemTYj2g0QsabE8Fx3aLTQw==", "Content-Language": "en-au"}, "sender_display": "<PERSON> <<EMAIL>>", "sender_for_history": "<PERSON> <<EMAIL>>", "body": "", "attachments": [{"id": "ANGjdJ-WO-CKxBiU4t2iOBnYhpnxYLQHlSa16lhGo3J-PkuvXGiScMPsRcH8_RURSWUEo67wlhiJw87oJe6sgidrW25pyGH7EwOQqd0EkhJA9V_1-i844nPHStyq5J-OFfvYBjrDeBhPk5IVSNRkI_yANzb5fSBPUC_cFOX2VEX9PnZOD0YBEfM8iNBMSP_NjccIXNixTCORmjtaF0fx2EQJFyPB-uxUOxIAK0NcjH3lJ3ypTOp_Kkgp-37wnE1pKliux08l4dEz8tuo_X0qtUnNFC51oh4qJUmm5c64m2waiV71A-S5HvvTcXt-KObH8XlzcuwyqoYYm8UpguOiIOMK9n3V-QGqX5iF9IDzbHjMGNt7jlxA4AVu8BbWsKJfym_B3TAK55wgk4UNTAoZ", "filename": "Invoice167395-01.pdf", "mimeType": "application/pdf", "size": 66871, "local_path": "extracted_data/enhanced_context\\attachments\\197c9c27260211cf_Invoice167395-01.pdf"}], "metadata": {"date": "Wed, 2 Jul 2025 16:10:48 +1000", "threadId": "197c9c27260211cf", "labelIds": ["Label_1390645524959035782", "Label_14", "SENT"], "snippet": "Hi, Please find attached your requested Invoice #167395/01 If you have any queries regarding the above, please do not hesitate to contact us. Alternatively, if you are happy to proceed you can make", "internalDate": "1751436648000"}}, "thread_context": [{"id": "197c9c27260211cf", "headers": {"Return-Path": "<<EMAIL>>", "Received": "from MYOBEXOW6 (61-68-27-250.static.tpgi.com.au. [************])        by smtp.gmail.com with ESMTPSA id d9443c01a7336-23acb3ba060sm127384345ad.197.2025.***********.51        (version=TLS1_2 cipher=ECDHE-ECDSA-AES128-GCM-SHA256 bits=128/128);        Tue, 01 Jul 2025 23:10:53 -0700 (PDT)", "From": "<PERSON> <<EMAIL>>", "To": "<<EMAIL>>", "Cc": "\"'<PERSON>'\" <<PERSON><PERSON>@bradycorp.com>", "Subject": "Invoice 167395/01 from TEAM SYSTEMS (VIC) PTY LTD", "Date": "Wed, 2 Jul 2025 16:10:48 +1000", "Message-ID": "<017401dbeb18$10630970$31291c50$@teamsystems.net.au>", "MIME-Version": "1.0", "Content-Type": "multipart/mixed; boundary=\"----=_NextPart_000_0175_01DBEB6B.E20FDCC0\"", "X-Mailer": "Microsoft Outlook 16.0", "Thread-Index": "AdvrGA4YemTYj2g0QsabE8Fx3aLTQw==", "Content-Language": "en-au"}, "snippet": "Hi, Please find attached your requested Invoice #167395/01 If you have any queries regarding the above, please do not hesitate to contact us. Alternatively, if you are happy to proceed you can make", "date": "Wed, 2 Jul 2025 16:10:48 +1000"}], "sender_history": [{"id": "197ca8fd2979aa8e", "subject": "Re: TS10PD - Fallshaw", "date": "Wed, 2 Jul 2025 19:55:08 +1000", "snippet": "<PERSON>, due in couple of days ago On 2/07/2025 6:59 pm, <PERSON> wrote: Hi mate, are you sure there&#39;s enough stock? I&#39;ve got 30 on order for <PERSON>, <PERSON>"}, {"id": "197c9db868538646", "subject": "Re: TS10PD - Fallshaw", "date": "Wed, 2 Jul 2025 16:38:16 +1000", "snippet": "<PERSON>, In stock 70 + GST for shipping Kind Regards, <PERSON>ates Sales --- HEAD OFFICE 121 Logis Blvd, Dandenong South, Vic 3175 T (03) 8791 5777 | W www.teamsystems.net.au | E mitch@teamsystems."}, {"id": "197c9cadb28b519a", "subject": "Invoice 167407/01 from TEAM SYSTEMS (VIC) PTY LTD", "date": "Wed, 2 Jul 2025 16:20:00 +1000", "snippet": "Hi, Please find attached your requested Invoice #167407/01 If you have any queries regarding the above, please do not hesitate to contact us. Alternatively, if you are happy to proceed you can make"}, {"id": "197c9c73af5351c8", "subject": "TEAM SYSTEMS (VIC) PTY LTD - PACKSLIP167397.PDF", "date": "Wed, 2 Jul 2025 16:16:00 +1000", "snippet": "please send thank, no invoice with goods ------------------------------------------------ TEAM SYSTEMS (VIC) PTY LTD Web: www.teamsystems.net.au ------------------------------------------------"}, {"id": "197c9c5cb2eb4e3e", "subject": "Invoice 167397/01 from TEAM SYSTEMS (VIC) PTY LTD", "date": "Wed, 2 Jul 2025 16:14:28 +1000", "snippet": "Hi, Please find attached your requested Invoice #167397/01 If you have any queries regarding the above, please do not hesitate to contact us. Alternatively, if you are happy to proceed you can make"}], "label_context": "<PERSON>", "extraction_time": "2025-07-03T05:24:35.700695"}