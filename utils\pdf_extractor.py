"""
PDF text extraction utilities with fallback to LlamaParse for low-confidence results.
"""
import logging
import fitz  # PyMuPDF
import requests
import os

logger = logging.getLogger(__name__)

LLAMAPARSE_API_KEY = os.getenv("LLAMA_CLOUD_API_KEY")
LLAMAPARSE_API_URL = "https://api.cloud.llamaindex.ai/api/parsing/pdf"


def extract_text_from_pdf(pdf_bytes: bytes, min_length: int = 200) -> str:
    """
    Extract text content from PDF bytes using PyMuPDF (fitz).
    If the extracted text is too short or not clear, fallback to LlamaParse API.
    """
    try:
        doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        text_content = []
        for page_num in range(doc.page_count):
            page = doc.load_page(page_num)
            text_content.append(page.get_text())
        doc.close()
        extracted_text = "\n".join(text_content).strip()
        logger.info(f"Extracted text from PDF ({len(text_content)} pages, {len(extracted_text)} characters)")
        # If text is too short, fallback to LlamaParse
        if len(extracted_text) < min_length:
            logger.warning(f"PDF extraction yielded only {len(extracted_text)} characters. Trying LlamaParse...")
            llama_text = extract_text_with_llamaparse(pdf_bytes)
            if llama_text:
                logger.info(f"LlamaParse extraction successful ({len(llama_text)} characters)")
                return llama_text
            else:
                logger.error("LlamaParse extraction failed. Returning PyMuPDF result.")
        return extracted_text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}. Trying LlamaParse fallback.")
        llama_text = extract_text_with_llamaparse(pdf_bytes)
        return llama_text or ""

def extract_text_with_llamaparse(pdf_bytes: bytes) -> str:
    """
    Extract text from PDF using LlamaParse API (cloud.llamaindex.ai).
    Returns extracted text or empty string on failure.
    """
    if not LLAMAPARSE_API_KEY:
        logger.error("LLAMA_CLOUD_API_KEY not set. Cannot use LlamaParse.")
        return ""
    try:
        files = {"file": ("document.pdf", pdf_bytes, "application/pdf")}
        headers = {"x-api-key": LLAMAPARSE_API_KEY}
        response = requests.post(LLAMAPARSE_API_URL, files=files, headers=headers, timeout=60)
        response.raise_for_status()
        data = response.json()
        # LlamaParse returns {"text": ...}
        text = data.get("text", "")
        return text.strip()
    except Exception as e:
        logger.error(f"LlamaParse API extraction failed: {e}")
        return ""
