# TeamsysV0.1 - Advanced Email Order Processing System

A comprehensive, AI-powered email processing system with **dynamic label selection** and **intelligent email limit management**. Automatically extracts, validates, and processes purchase orders from business emails with flexible configuration options, modern datetime handling, and seamless MYOB EXO integration.

## 🎯 System Overview

TeamsysV0.1 transforms email-based order processing from manual work into an automated, intelligent workflow. The system monitors multiple Gmail labels with user-friendly selection, uses AI to extract order information from emails and PDFs, validates data against business rules, and creates sales orders in MYOB EXO - all with comprehensive tracking and flexible configuration.

### 🌟 Latest Features (v0.1.2)
- **🎯 Dynamic Label Selection**: Interactive label selection with support for multiple suppliers
- **📊 Intelligent Email Limits**: Configurable email processing limits with validation (1-1000 emails)
- **🕒 Modern DateTime Handling**: Timezone-aware operations with robust date filtering
- **🔧 Flexible Configuration**: Command-line arguments, environment variables, and per-label settings
- **🧪 Comprehensive Testing**: 41+ test cases ensuring system reliability
- **📖 Enhanced Documentation**: Complete guides for all features and configurations

### Core Capabilities
- **🤖 AI-Powered Processing**: Google Gemini AI extracts structured data from emails and PDFs
- **📧 Multi-Label Gmail Integration**: Process emails from Brady, RSEA, Woolworths, Brierley, Gateway, Highgate, Sitecraft, and custom labels
- **🏢 MYOB EXO Integration**: Direct sales order creation with validation
- **📊 Web Dashboard**: Modern interface for email management and analytics
- **🔄 Real-time Processing**: Continuous monitoring with intelligent prioritization
- **📈 Analytics & Reporting**: Comprehensive processing statistics and trends
- **🛡️ Robust Validation**: Multi-layer validation before order creation

## 🚀 Quick Start

### Option 1: Interactive Label Selection (Recommended)
```bash
# Interactive mode - choose labels from a user-friendly menu
python main_processor.py

# Process specific labels
python main_processor.py --labels Brady,RSEA,Woolworths

# Process all available labels
python main_processor.py --labels all

# Brady-only mode (backward compatibility)
python main_processor.py --brady-only
```

### Option 2: Advanced Configuration
```bash
# Custom email limits
python main_processor.py --labels Brady,RSEA --max-results 50

# Non-interactive mode with environment variables
export RUNTIME_LABELS_TO_PROCESS="Brady,RSEA"
export MAX_GMAIL_RESULTS_OVERRIDE=75
python main_processor.py --non-interactive

# Dry run to preview configuration
python main_processor.py --dry-run --labels all --max-results 25
```

### Option 3: Web Dashboard
```bash
# Start with demo data for immediate exploration
python demo_dashboard.py

# Or use the new restructured entry point
python run_restructured_system.py dashboard --demo

# Start the full system with dashboard
python run_restructured_system.py system
```

### Option 4: Information Commands
```bash
# Show available labels
python main_processor.py --show-labels

# Validate configuration
python main_processor.py --validate-config

# Get help with all options
python main_processor.py --help
```

## 📊 Web Dashboard Features

The system includes a comprehensive web-based dashboard for complete email order management:

- **📧 Real-time Email Sync**: Automatic Gmail synchronization with live updates
- **🚦 Visual Status Tracking**: Color-coded status system (🟢 Processed, 🟡 Review, 🔴 Failed, ⚪ Unprocessed)
- **🔍 Advanced Filtering**: Filter by status, supplier, date range, and custom search
- **⚡ Batch Operations**: Process multiple emails simultaneously with progress tracking
- **📈 Analytics Dashboard**: Processing statistics, trends, and performance metrics
- **📁 File Management**: Direct download of generated markdown and MYOB files
- **🔄 Real-time Updates**: Live processing status and automatic refresh

# Option 2: Enhanced processing + dashboard
python enhanced_email_processor.py --start-dashboard

# Option 3: Dashboard only (production)
python run_dashboard.py
```

Access at: **http://localhost:5000**

## 📚 Documentation

### 🎯 New Features Documentation
- **🏷️ [Dynamic Label Selection Guide](DYNAMIC_LABEL_SELECTION_GUIDE.md)** - Complete guide to the new label selection features
- **📊 Email Limit Configuration** - Flexible email processing limits with validation
- **🕒 Modern DateTime Handling** - Timezone-aware operations and robust date filtering

### Quick Links
- **🚀 [Installation Guide](INSTALLATION.md)** - Complete setup instructions
- **🏗️ [System Architecture](ARCHITECTURE.md)** - Technical architecture overview
- **📖 [Dashboard Guide](EMAIL_DASHBOARD_README.md)** - Web dashboard documentation
- **🔧 [API Documentation](API_DOCUMENTATION.md)** - Service APIs and integration
- **🛠️ [Troubleshooting](TROUBLESHOOTING.md)** - Common issues and solutions
- **🤝 [Contributing Guide](CONTRIBUTING.md)** - Development and contribution guidelines

### Component Documentation
- **[MYOB Poster Usage](MYOB_POSTER_USAGE.md)** - MYOB integration guide
- **[Refactor Summary](REFACTOR_SUMMARY.md)** - System architecture changes
- **[Project Review](project_review_report.md)** - Module analysis and recommendations
- **[Security Policy](SECURITY.md)** - Security considerations and best practices

### Additional Resources
- **[Documentation Summary](DOCUMENTATION_SUMMARY.md)** - Overview of all documentation improvements
- **[Restructure Summary](PROJECT_RESTRUCTURE_SUMMARY.md)** - Complete restructure details
- **[Final Status](FINAL_STATUS.md)** - Project completion status

## Core Features

- **Gmail Integration**: Automatically fetches emails from specified labels with PDF attachments
- **LLM-Powered Parsing**: Uses Google Gemini to intelligently extract order information from emails and PDFs
- **Structured Data Processing**: Creates markdown summaries and structured JSON payloads
- **MYOB EXO Integration**: Validates and posts sales orders to MYOB EXO via API
- **User Approval Workflow**: Interactive approval process for order posting
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **🆕 Web Dashboard**: Modern web interface for email management and processing

## Workflow

1. **Initialize Gmail Service**: Authenticates with Gmail API and connects to your account
2. **Initialize LLM Agent**: Sets up Google Gemini for intelligent text processing
3. **Parse Emails**: Fetches emails from specified labels containing PDF orders
4. **Create Markdown Summary**: Generates structured markdown of important order information
5. **Extract JSON Data**: Uses LLM to create structured JSON payload for MYOB
6. **User Approval**: Presents orders for manual approval before posting
7. **Post to MYOB**: Creates sales orders in MYOB EXO via API

## Quick Start

1. **Clone and Setup**:
   ```bash
   cd your_project_directory
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and configuration
   ```

3. **Setup Gmail API**:
   - Download OAuth 2.0 credentials from Google Cloud Console
   - Save as `credentials.json` in the project directory

4. **Run the Application**:
   ```bash
   python email_order_processor.py
   ```

## 🔧 Configuration

The application supports multiple configuration methods with flexible priority handling:

### 📊 Email Limit Configuration (NEW)

#### Priority Order (Highest to Lowest)
1. **Global Override** - `--max-results N` or `MAX_GMAIL_RESULTS_OVERRIDE=N`
2. **Per-Label Override** - `MAX_GMAIL_RESULTS_LABELNAME=N`
3. **Label-Specific Configuration** - Configured in `config.py`
4. **Default Limits** - Brady: 100, Others: 10

#### Examples
```bash
# Global override for all labels
export MAX_GMAIL_RESULTS_OVERRIDE=75
python main_processor.py --labels all

# Per-label email limits
export MAX_GMAIL_RESULTS_BRADY=200
export MAX_GMAIL_RESULTS_RSEA=25
python main_processor.py --labels Brady,RSEA

# Command-line override (highest priority)
python main_processor.py --max-results 50 --labels all
```

### 🏷️ Label Selection Configuration (NEW)

#### Runtime Label Selection
```bash
# Environment variable
export RUNTIME_LABELS_TO_PROCESS="Brady,RSEA,Woolworths"
python main_processor.py --non-interactive

# Command-line arguments
python main_processor.py --labels Brady,RSEA,Woolworths
python main_processor.py --labels all
python main_processor.py --brady-only
```

### Gmail Settings
- `GMAIL_CREDENTIALS_FILE`: Path to Gmail OAuth 2.0 credentials file
- `GMAIL_TOKEN_FILE`: Path where Gmail token will be stored
- `GMAIL_LABELS_TO_PROCESS`: Comma-separated list of Gmail labels to monitor (legacy)
- `GMAIL_UNREAD_ONLY`: Process only unread emails (True/False)
- `MAX_GMAIL_RESULTS`: Maximum number of emails to fetch per run (legacy)

### Gemini LLM Settings
- `GEMINI_API_KEY`: Your Google Gemini API key
- `GEMINI_MODEL`: Gemini model to use (default: gemini-1.5-flash-latest)

### MYOB API Settings
- `EXO_IP`: MYOB EXO server IP address
- `EXO_PORT`: MYOB EXO API port
- `USER`: MYOB API username
- `PWD`: MYOB API password
- `API_KEY`: MYOB API key
- `EXO_TOK`: MYOB EXO token

### Logging
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## 🏗️ System Architecture

TeamsysV0.1 follows a modular architecture with clear separation of concerns:

### Core Components
```
📧 Gmail Integration → 🤖 AI Processing → 🏢 MYOB Integration
     ↓                    ↓                    ↓
📊 Web Dashboard ← 💾 Data Storage ← 📈 Analytics
```

### 🆕 Enhanced Features (v0.1.2)

#### Dynamic Label Selection System
- **Interactive Selection**: User-friendly command-line interface for label selection
- **Multi-Label Support**: Process Brady, RSEA, Woolworths, Brierley, Gateway, Highgate, Sitecraft, and custom labels
- **Flexible Input Methods**: Command-line arguments, environment variables, and interactive prompts
- **Label Validation**: Automatic validation against available Gmail labels
- **Backward Compatibility**: Maintains Brady-only default behavior

#### Intelligent Email Limit Management
- **Multi-Source Configuration**: Command-line, environment variables, and per-label settings
- **Validation System**: Ensures limits are between 1-1000 emails with detailed error messages
- **Priority Handling**: Clear precedence order for different configuration sources
- **Real-Time Feedback**: Shows limit sources and overrides during processing
- **Comprehensive Logging**: Detailed tracking of limit applications and sources

#### Modern DateTime Handling
- **Timezone Awareness**: All datetime operations use timezone-aware objects
- **Robust Date Parsing**: Handles multiple Gmail date formats with fallback mechanisms
- **Enhanced Date Filtering**: Improved Gmail query date filters with validation
- **Relative Time Support**: "N hours ago", "N days ago" filter generation
- **Best Practices**: Uses current Python datetime best practices

### Project Structure
```
TeamsysV0.1/
├── 📁 Core Processing (Enhanced)
│   ├── main_processor.py       # Enhanced main processor with dynamic labels
│   ├── label_selector.py       # NEW: Interactive label selection
│   ├── query_builder.py        # NEW: Dynamic Gmail query builder
│   ├── email_limit_manager.py  # NEW: Email limit management
│   ├── datetime_utils.py       # NEW: Modern datetime utilities
│   └── runtime_config.py       # NEW: Command-line argument handling
├── 📁 orchestrators/          # High-level workflow coordination
│   ├── comprehensive_email_system.py # Main system orchestrator
│   ├── email_order_processor.py # Email processing pipeline
│   ├── enhanced_email_processor.py # Enhanced processing
│   └── email_dashboard.py      # Web dashboard application
├── 📁 agents/                 # Autonomous processing agents
│   ├── enhanced_universal_agent.py # Universal email processing
│   └── continuous_polling_agent.py # Real-time monitoring
├── 📁 services/               # External API integrations
│   ├── gmail_service.py        # Enhanced Gmail API operations
│   ├── llm_service.py          # AI processing with Gemini
│   ├── myob_service.py         # MYOB API operations
│   └── memory_client.py        # ChromaDB memory service
├── 📁 utils/                  # Shared utilities and configuration
│   ├── config.py              # Enhanced configuration management
│   ├── models.py               # Pydantic data models
│   ├── pdf_extractor.py       # PDF text extraction
│   └── setup.py               # System setup
├── 🧪 Testing & Demos (Enhanced)
│   ├── test_label_selection.py # NEW: Comprehensive test suite (41+ tests)
│   ├── demo_*.py              # Demo scripts
│   ├── test_*.py              # Additional test scripts
│   └── run_restructured_system.py # Main entry point
├── 📁 Data & Configuration
│   ├── requirements*.txt      # Dependencies
│   ├── *.yaml, *.json        # Configuration files
│   ├── .env                   # Environment variables
│   └── credentials.json       # Gmail API credentials
└── 📚 Documentation (Enhanced)
    ├── README.md              # This file (updated)
    ├── DYNAMIC_LABEL_SELECTION_GUIDE.md # NEW: Complete feature guide
    ├── INSTALLATION.md        # Setup guide
    ├── ARCHITECTURE.md        # Technical details
    └── *.md                   # Additional guides
```

**🔗 [View Complete Architecture Documentation](ARCHITECTURE.md)**

## Key Classes and Components

### EmailOrderProcessor
Main orchestration class that coordinates the entire workflow.

### GmailService
- Handles Gmail API authentication and operations
- Fetches emails from specified labels
- Extracts text from PDF attachments
- Manages email read/unread status

### LLMService
- Creates structured markdown summaries from email content
- Parses order data using Google Gemini
- Converts unstructured text to structured JSON

### MYOBService
- Validates sales orders with MYOB API
- Posts validated orders to MYOB EXO
- Handles MYOB API authentication and error handling

### Data Models (Pydantic)
- `CustomerDetails`: Customer information and order numbers
- `DeliveryAddress`: Shipping address details
- `OrderLine`: Individual order line items
- `ExtractedOrder`: Complete order structure

## Setup Instructions

### 1. Gmail API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable the Gmail API
4. Create OAuth 2.0 credentials (Desktop application)
5. Download credentials and save as `credentials.json`

### 2. Google Gemini API
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create an API key
3. Add to your `.env` file

### 3. MYOB EXO API
Ensure you have:
- MYOB EXO server access
- API user credentials
- API key and EXO token

## Usage

1. **First Run**: The application will open a browser for Gmail authorization
2. **Email Processing**: Automatically fetches and processes emails from configured labels
3. **Review Orders**: Each parsed order is presented with a markdown summary
4. **Approve/Deny**: Choose to approve, deny, or skip each order
5. **MYOB Integration**: Approved orders are automatically posted to MYOB

## Example Workflow Output

```
=============================================================
ORDER 1 of 3 (Email ID: abc123)
=============================================================

## Order Summary
- **Customer**: WOOLWORTHS LIMITED
- **Order Number**: LVA4401196688
- **Date**: 2024-01-15
- **Delivery Address**: 123 Distribution Center, Sydney NSW 2000
- **Shipping Method**: CAPITAL

## Order Items
| Stock Code | Description | Quantity | Unit Price | Total |
|------------|-------------|----------|------------|-------|
| SKU001     | Product A   | 100      | $10.50     | $1,050|
| SKU002     | Product B   | 50       | $25.00     | $1,250|

=============================================================

Approve this order? (a)pprove / (d)eny / (s)kip: a
```

## Error Handling

The application includes comprehensive error handling for:
- Gmail API authentication issues
- PDF extraction failures
- LLM parsing errors
- MYOB API communication problems
- Network connectivity issues

## Logging

Detailed logging is provided for:
- Authentication processes
- Email fetching and parsing
- LLM interactions
- MYOB API calls
- User decisions and outcomes

## Troubleshooting

### Common Issues

1. **Gmail Authentication Errors**
   - Delete `token.pickle` and re-run for fresh authentication
   - Verify `credentials.json` is valid and in correct location

2. **MYOB API Errors**
   - Check server connectivity and credentials
   - Verify API user has required permissions
   - Check logs for specific error messages

3. **LLM Parsing Issues**
   - Verify Gemini API key is valid
   - Check internet connectivity
   - Review email content for parsing complexity

4. **PDF Extraction Problems**
   - Some PDFs may be image-based (requires OCR)
   - Complex layouts may need manual review

## 📦 Dependencies

### Core Dependencies
- `python-dotenv`: Environment variable management
- `google-api-python-client`: Gmail API client
- `google-auth-*`: Google authentication libraries
- `beautifulsoup4`: HTML parsing
- `PyMuPDF`: PDF text extraction
- `google-generativeai`: Gemini LLM integration
- `pydantic`: Data validation
- `requests`: HTTP requests for MYOB API

### Enhanced Features Dependencies
- `argparse`: Command-line argument parsing (built-in)
- `typing`: Type hints for better code quality (built-in)
- `unittest`: Comprehensive testing framework (built-in)
- `datetime`: Modern timezone-aware datetime handling (built-in)
- `re`: Regular expressions for date parsing (built-in)

### Development Dependencies
- `unittest.mock`: Mocking for testing (built-in)
- `os`: Environment variable access (built-in)
- `sys`: System-specific parameters (built-in)
- `logging`: Enhanced logging capabilities (built-in)

## 🧪 Testing & Validation

### Comprehensive Test Suite
The system includes extensive testing with 41+ test cases covering:

```bash
# Run all tests
python test_label_selection.py

# Test specific components
python -m unittest test_label_selection.TestEmailLimitManager
python -m unittest test_label_selection.TestModernDateTimeHandler
python -m unittest test_label_selection.TestGmailQueryBuilder
```

### Test Coverage
- **Email Limit Manager**: Validation, priority handling, environment variables
- **Modern DateTime Handler**: Timezone handling, Gmail date parsing, validation
- **Gmail Query Builder**: Dynamic query generation, label-specific settings
- **Label Selector**: Interactive selection, validation, fallback mechanisms
- **Runtime Configuration**: Command-line parsing, environment integration

### Validation Commands
```bash
# Validate system configuration
python main_processor.py --validate-config

# Show available labels
python main_processor.py --show-labels

# Test with dry run
python main_processor.py --dry-run --labels all --max-results 25
```

## 🤝 Contributing

### Development Guidelines
1. **Follow existing patterns**: Maintain consistency with current code structure
2. **Add comprehensive logging**: Include detailed logging for new features
3. **Include error handling**: Robust error handling for all external API calls
4. **Update documentation**: Keep documentation current with any changes
5. **Write tests**: Add test cases for new functionality
6. **Validate changes**: Use dry-run mode to test configurations

### Code Quality Standards
- **Type hints**: Use typing for better code quality and IDE support
- **Docstrings**: Comprehensive documentation for all functions and classes
- **Error handling**: Graceful degradation and informative error messages
- **Logging**: Appropriate log levels and detailed context information
- **Testing**: Unit tests with mock objects to avoid external dependencies

## License

This project is for internal use. Please ensure compliance with all API terms of service for Gmail, Gemini, and MYOB.
