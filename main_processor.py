"""
Main email order processor - orchestrates the complete workflow.
"""

import logging
import os
import json
from datetime import datetime
from typing import List
from pydantic import ValidationError
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart


# Add project root to Python path for imports
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import config
from models import ExtractedOrder, ProcessedOrder, EmailData
from gmail_service import GmailService
from pdf_extractor import extract_text_from_pdf
from llm_service import LLMService

logger = logging.getLogger(__name__)


class EmailOrderProcessor:
    """Main processor that orchestrates the email-to-markdown/JSON workflow (no MYOB, no approval)."""

    def __init__(self):
        logger.info("Initializing Email Order Processor (extraction-only mode)")
        self.gmail_service = GmailService()
        self.llm_service = LLMService()
        self.processing_labels = ['Brady']  # Default to <PERSON> for backward compatibility
        self._ensure_directories()
        self._setup_processing_labels()
        logger.info("Email Order Processor initialized successfully")

    def set_processing_labels(self, labels: List[str]):
        """Set the labels to process for this run."""
        self.processing_labels = labels
        logger.info(f"Set processing labels to: {labels}")

    def get_processing_labels(self) -> List[str]:
        """Get the current processing labels."""
        return self.processing_labels

    def set_runtime_config(self, runtime_config):
        """Set the runtime configuration for limit management."""
        self.runtime_config = runtime_config
        logger.debug("Set runtime configuration for email limit management")

    def _setup_processing_labels(self):
        """Setup traffic light processing labels in Gmail."""
        try:
            labels = self.gmail_service.get_or_create_processing_labels()
            if labels:
                logger.info(
                    "Traffic light processing labels ready: Processed, Review, Failed"
                )
            else:
                logger.warning(
                    "Failed to setup processing labels - labeling will be disabled"
                )
        except Exception as e:
            logger.error(f"Error setting up processing labels: {e}")

    def _ensure_directories(self):
        directories = ["markdown", "myob", "brady/markdown", "brady/myob"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        logger.info("Created output directories: markdown/, myob/, brady/markdown/, brady/myob/")

    def _sanitize_filename(self, text: str) -> str:
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            text = text.replace(char, "_")
        return text.strip()[:50]

    def _generate_filename_prefix(self, email_subject: str) -> str:
        now = datetime.now()
        date_prefix = now.strftime("%d-%m_%H%M")
        sanitized_subject = self._sanitize_filename(email_subject)
        return f"{date_prefix}_{sanitized_subject}"

    def _save_markdown_file(self, email_subject: str, markdown_content: str, source_label: str = "") -> str:
        filename_prefix = self._generate_filename_prefix(email_subject)
        
        # Use brady folder for Brady emails, regular folder for others
        if source_label.lower() == "brady":
            filepath = os.path.join("brady", "markdown", f"{filename_prefix}.md")
        else:
            filepath = os.path.join("markdown", f"{filename_prefix}.md")
            
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(markdown_content)
            logger.info(f"Saved markdown summary to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save markdown file: {e}")
            return ""

    def _save_myob_file(self, email_subject: str, myob_payload: dict, source_label: str = "") -> str:
        filename_prefix = self._generate_filename_prefix(email_subject)
        
        # Use brady folder for Brady emails, regular folder for others
        if source_label.lower() == "brady":
            filepath = os.path.join("brady", "myob", f"{filename_prefix}.json")
        else:
            filepath = os.path.join("myob", f"{filename_prefix}.json")
            
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(myob_payload, f, indent=2)
            logger.info(f"Saved MYOB payload to: {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Failed to save MYOB file: {e}")
            return ""

    def process_emails(self) -> List[ProcessedOrder]:
        logger.info("Starting email processing workflow (extraction-only)")
        from query_builder import GmailQueryBuilder
        from email_limit_manager import EmailLimitManager

        emails = []

        # Initialize query builder with runtime config for limit management
        runtime_config = getattr(self, 'runtime_config', None)
        query_builder = GmailQueryBuilder(runtime_config)
        limit_manager = EmailLimitManager(runtime_config)

        # Use the configured processing labels
        label_names = self.processing_labels
        logger.info(f"Processing {len(label_names)} labels: {label_names}")

        # Log email limits summary
        limit_manager.log_limits_summary(label_names)

        for label_name in label_names:
            label_id = self.gmail_service.get_label_id(label_name)
            gmail_service_instance = getattr(self.gmail_service, "service", None)
            if label_id and gmail_service_instance is not None:
                try:
                    # Build query using the dynamic query builder
                    query, max_results = query_builder.build_label_query(label_name)
                    logger.info(f"{label_name} Gmail query: {query} (max: {max_results})")

                    # Get label description for logging
                    description = query_builder.get_label_description(label_name)
                    logger.info(f"Processing {label_name}: {description}")

                    response = (
                        gmail_service_instance.users()
                        .messages()
                        .list(
                            userId="me",
                            labelIds=[label_id],
                            maxResults=max_results,
                            q=query,
                        )
                        .execute()
                    )
                    messages = response.get("messages", [])
                    logger.info(f"Found {len(messages)} emails in label '{label_name}'")
                    for msg_ref in messages:
                        email_data = self.gmail_service._get_email_details(
                            msg_ref["id"], label_name
                        )

                        if not email_data:
                            continue

                        # Check if we should skip packing slips for this label
                        if query_builder.should_skip_packing_slips(label_name):
                            # Check if this might be a packing slip
                            subject_lower = email_data.subject.lower()
                            if any(term in subject_lower for term in ['packing slip', 'packing list', 'shipment']):
                                logger.info(f"Skipping potential packing slip for {label_name}: {email_data.subject}")
                                # Mark as review since it was skipped but might need attention
                                try:
                                    self.gmail_service.mark_email_review(email_data.id)
                                    logger.info(f"Marked packing slip as 'Review': {email_data.id}")
                                except Exception as e:
                                    logger.error(f"Failed to label packing slip: {e}")
                                continue

                        # Skip emails that don't have PDF attachments
                        if not email_data.attachments or not any(
                            att["filename"].lower().endswith(".pdf")
                            for att in email_data.attachments
                        ):
                            logger.debug(f"Skipping email {email_data.id} - no PDF attachments")
                            continue

                        # Skip emails that already have a processing label
                        existing_label = self.gmail_service.has_processing_label(email_data.id)
                        if existing_label:
                            logger.info(f"Skipping email {email_data.id} - already has label: {existing_label}")
                            continue

                        emails.append(email_data)
                except Exception as e:
                    logger.error(f"Error fetching emails from {label_name}: {e}")
            elif not label_id:
                logger.error(
                    f"Label ID for '{label_name}' not found. Cannot fetch emails."
                )
            else:
                logger.error(
                    "Gmail service is not initialized or failed to authenticate. Cannot fetch emails."
                )
        if not emails:
            logger.info("No emails found to process")
            return []
        logger.info(f"Found {len(emails)} emails to process")
        processed_orders = []
        for email in emails:
            try:
                processed_order = self._process_single_email(email)
                if processed_order:
                    processed_orders.append(processed_order)
            except Exception as e:
                logger.error(f"Error processing email {email.id}: {e}")
                # Mark email as failed
                try:
                    self.gmail_service.mark_email_failed(email.id)
                    logger.info(f"Marked email as 'Failed': {email.id}")
                except Exception as label_error:
                    logger.error(
                        f"Failed to add 'Failed' label to email {email.id}: {label_error}"
                    )
                continue
        logger.info(f"Successfully processed {len(processed_orders)} orders")
        return processed_orders

    def _process_single_email(self, email: EmailData) -> ProcessedOrder:
        logger.info(f"Processing email: {email.subject}")
        full_content = email.body
        pdf_content = ""
        for attachment in email.attachments:
            if attachment["filename"].lower().endswith(".pdf"):
                try:
                    logger.info(f"Processing PDF: {attachment['filename']}")
                    pdf_bytes = attachment["data"]
                    pdf_text = extract_text_from_pdf(pdf_bytes)
                    if pdf_text:
                        pdf_content += (
                            f"\n--- PDF: {attachment['filename']} ---\n{pdf_text}\n"
                        )
                        full_content += f"\n\n{pdf_text}"
                except Exception as e:
                    logger.error(f"Error processing PDF {attachment['filename']}: {e}")
        if not full_content.strip():
            raise ValueError("No content found in email or attachments")

        # Skip Brady packing slip emails
        if "packing slip" in email.subject.lower() and "brady" in email.sender.lower():
            logger.info(f"Skipping Brady packing slip email: {email.subject}")
            raise ValueError("Brady packing slip email - skipped")

        logger.info("Generating markdown summary")
        markdown_summary = self.llm_service.generate_markdown_summary(
            email, pdf_content
        )
        logger.info("Extracting order data with LLM")
        try:
            extracted_data_dict = self.llm_service.extract_order_data(full_content)
            if not extracted_data_dict:
                logger.error(
                    "LLM service returned None or empty dict for order data extraction"
                )
                raise ValueError("Failed to extract order data - LLM returned no data")
        except Exception as e:
            logger.error(f"Order data extraction failed: {e}")
            raise ValueError(f"Failed to extract order data: {e}")
        try:
            extracted_order = ExtractedOrder(**extracted_data_dict)
        except ValidationError as e:
            logger.error(f"Order data validation failed: {e}")
            logger.error(f"Raw extracted data: {extracted_data_dict}")
            raise ValueError(f"Invalid order data: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during order validation: {e}")
            raise ValueError(f"Order validation error: {e}")
        logger.info("Generating MYOB payload")
        try:
            myob_payload = self.llm_service.generate_myob_payload_direct(
                extracted_order
            )
            if not myob_payload:
                logger.error("Failed to generate MYOB payload")
                raise ValueError("Failed to generate MYOB payload")
        except Exception as e:
            logger.error(f"MYOB payload generation failed: {e}")
            raise ValueError(f"Failed to generate MYOB payload: {e}")
        markdown_filepath = self._save_markdown_file(email.subject, markdown_summary, email.source_label)
        myob_filepath = self._save_myob_file(email.subject, myob_payload, email.source_label)
        order_id = self._generate_order_id()

        # Mark email as successfully processed
        try:
            self.gmail_service.mark_email_processed(email.id)
            logger.info(f"Marked email as 'Processed': {email.id}")
        except Exception as label_error:
            logger.error(
                f"Failed to add 'Processed' label to email {email.id}: {label_error}"
            )

        return ProcessedOrder(
            email_id=email.id,
            email_subject=email.subject,
            extracted_data=extracted_order,
            markdown_summary=markdown_summary,
            myob_payload=myob_payload,
            markdown_filepath=markdown_filepath,
            myob_filepath=myob_filepath,
        )

    def _generate_order_id(self) -> str:
        import glob

        pattern = os.path.join("myob", "*.json")
        existing_files = glob.glob(pattern)
        existing_numbers = []
        for filepath in existing_files:
            filename = os.path.basename(filepath)
            if filename.startswith("TS") and filename.endswith(".json"):
                try:
                    number_str = filename[2:-5]
                    existing_numbers.append(int(number_str))
                except ValueError:
                    continue
        next_number = max(existing_numbers, default=0) + 1
        return f"TS{next_number:03d}"

    def run_extraction_workflow(self):
        logger.info("Starting email extraction workflow (no MYOB posting, no approval)")
        try:
            processed_orders = self.process_emails()
            if not processed_orders:
                print("No orders found to process.")
                return []
            self._display_extraction_results(processed_orders)
            return processed_orders
        except Exception as e:
            logger.error(f"Workflow error: {e}")
            print(f"Error in workflow: {e}")
            return []

    def _display_extraction_results(self, processed_orders: List[ProcessedOrder]):
        print("\n" + "=" * 80)
        print("EMAIL EXTRACTION COMPLETED")
        print("=" * 80)
        for i, order in enumerate(processed_orders, 1):
            print(f"\n--- ORDER {i} of {len(processed_orders)} ---")
            print(f"Order ID: {getattr(order, 'order_id', 'Unknown')}")
            print(f"Email Subject: {order.email_subject}")
            print(f"Email ID: {order.email_id}")
            print(
                f"📄 Markdown Summary: {getattr(order, 'markdown_filepath', 'Not saved')}"
            )
            print(f"📊 MYOB Payload: {getattr(order, 'myob_filepath', 'Not saved')}")
            print("\n📊 EXTRACTED DATA SUMMARY:")
            print("-" * 50)
            customer = order.extracted_data.customer_details
            print(f"• Debtor ID: {customer.debtor_id}")
            print(f"• Customer PO: {customer.customer_order_number or 'N/A'}")
            print(f"• Order Status: {order.extracted_data.order_status}")
            print(f"• Ship Via: {order.extracted_data.X_SHIPVIA or 'N/A'}")
            if order.extracted_data.delivery_address:
                addr = order.extracted_data.delivery_address
                print(f"• Delivery Address:")
                for i in range(1, 7):
                    line = getattr(addr, f"line{i}", None)
                    if line:
                        print(f"  - {line}")
            print(f"• Order Lines ({len(order.extracted_data.order_lines)}):")
            for j, line in enumerate(order.extracted_data.order_lines, 1):
                print(f"  {j}. {line.stockcode} - Qty: {line.orderquantity}")
            print("\n" + "-" * 60)
        print("\n" + "=" * 80)
        print("EXTRACTION SUMMARY")
        print("=" * 80)
        print(f"📧 Total emails processed: {len(processed_orders)}")
        
        # Count Brady vs other emails
        brady_count = sum(1 for order in processed_orders if order.extracted_data.customer_details.debtor_id == 5760)
        other_count = len(processed_orders) - brady_count
        
        if brady_count > 0:
            print(f"📄 Brady markdown summaries saved in: brady/markdown/")
            print(f"📊 Brady MYOB payloads saved in: brady/myob/")
        if other_count > 0:
            print(f"📄 Other markdown summaries saved in: markdown/")
            print(f"📊 Other MYOB payloads saved in: myob/")
        print("=" * 80)


def send_completion_email(subject, body=None, processed_orders=None, recipient=None):
    """Send a detailed HTML completion email with order summary."""
    recipient = recipient or os.getenv("LOG_RECIPIENT_EMAIL")
    if not recipient:
        logger.warning("No LOG_RECIPIENT_EMAIL set, skipping notification email.")
        return

    # Generate HTML body if not provided
    if body is None:
        body = generate_html_completion_email(processed_orders or [])

    from base64 import urlsafe_b64encode
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    from gmail_service import GmailService

    try:
        gmail = GmailService()

        # Create multipart message for HTML
        message = MIMEMultipart("alternative")
        message["to"] = recipient
        message["from"] = os.getenv("USER_EMAIL", "me")
        message["subject"] = subject

        # Create HTML part
        html_part = MIMEText(body, "html")
        message.attach(html_part)

        raw = urlsafe_b64encode(message.as_bytes()).decode()
        send_body = {"raw": raw}

        service = getattr(gmail, "service", None)
        if service is None:
            logger.error(
                "Gmail API service not initialized. Cannot send notification email."
            )
            return

        service.users().messages().send(userId="me", body=send_body).execute()
        logger.info(f"HTML notification email sent to {recipient} via Gmail API")

    except Exception as e:
        logger.error(f"Failed to send notification email via Gmail API: {e}")


def generate_html_completion_email(processed_orders: List[ProcessedOrder]) -> str:
    """Generate a simple HTML email with table-based layout."""
    current_time = datetime.now().strftime("%B %d, %Y at %I:%M %p")

    # Calculate summary stats
    total_line_items = sum(
        len(order.extracted_data.order_lines) for order in processed_orders
    )
    unique_customers = len(
        set(
            order.extracted_data.customer_details.debtor_id
            for order in processed_orders
        )
    )

    # Start building simple HTML with inline styles for better email client compatibility
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Email Extraction Complete</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px;">
        
        <div style="max-width: 800px; margin: 0 auto; background-color: white;">
            <!-- Header -->
            <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">✅ Email Extraction Complete</h1>
                <p style="margin: 10px 0 0 0;">Workflow completed successfully on {current_time}</p>
            </div>
            
            <!-- Summary Table -->
            <div style="padding: 20px;">
                <h2 style="color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Summary</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                    <tr style="background-color: #f8f9fa;">
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Emails Processed</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center; font-size: 18px; color: #4CAF50; font-weight: bold;">{len(processed_orders)}</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Total Line Items</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center; font-size: 18px; color: #4CAF50; font-weight: bold;">{total_line_items}</td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Unique Customers</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center; font-size: 18px; color: #4CAF50; font-weight: bold;">{unique_customers}</td>
                    </tr>
                </table>
    """

    if processed_orders:
        html += """
                <h2 style="color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Processed Orders</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <thead>
                        <tr style="background-color: #4CAF50; color: white;">
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">#</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Email Subject</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Debtor ID</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Customer PO</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Line Items</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        for i, order in enumerate(processed_orders, 1):
            customer = order.extracted_data.customer_details
            row_style = "background-color: #f8f9fa;" if i % 2 == 0 else ""

            html += f"""
                        <tr style="{row_style}">
                            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">{i}</td>
                            <td style="border: 1px solid #ddd; padding: 12px;">{order.email_subject}</td>
                            <td style="border: 1px solid #ddd; padding: 12px;"><span style="background-color: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 12px; font-size: 12px;">{customer.debtor_id}</span></td>
                            <td style="border: 1px solid #ddd; padding: 12px;">{customer.customer_order_number or 'N/A'}</td>
                            <td style="border: 1px solid #ddd; padding: 12px; text-align: center; font-weight: bold;">{len(order.extracted_data.order_lines)}</td>
                        </tr>
            """

        html += """
                    </tbody>
                </table>
        """

        # Detailed order information
        html += """
                <h2 style="color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px;">Order Details</h2>
        """

        for i, order in enumerate(processed_orders, 1):
            customer = order.extracted_data.customer_details
            delivery_addr = order.extracted_data.delivery_address

            # Build address string
            address_lines = []
            if delivery_addr:
                for j in range(1, 7):
                    line = getattr(delivery_addr, f"line{j}", None)
                    if line and line.strip():
                        address_lines.append(line.strip())

            address_text = (
                "<br>".join(address_lines)
                if address_lines
                else "No delivery address specified"
            )

            html += f"""
                <div style="margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px;">
                    <div style="background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd;">
                        <h3 style="margin: 0; color: #333;">Order #{i}: {order.email_subject}</h3>
                        <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Email ID: {order.email_id}</p>
                    </div>
                    
                    <div style="padding: 15px;">
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                            <tr>
                                <td style="border: 1px solid #eee; padding: 8px; font-weight: bold; background-color: #f9f9f9; width: 150px;">Debtor ID:</td>
                                <td style="border: 1px solid #eee; padding: 8px;">{customer.debtor_id}</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #eee; padding: 8px; font-weight: bold; background-color: #f9f9f9;">Customer PO:</td>
                                <td style="border: 1px solid #eee; padding: 8px;">{customer.customer_order_number or 'N/A'}</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #eee; padding: 8px; font-weight: bold; background-color: #f9f9f9;">Order Status:</td>
                                <td style="border: 1px solid #eee; padding: 8px;">{order.extracted_data.order_status}</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #eee; padding: 8px; font-weight: bold; background-color: #f9f9f9;">Ship Via:</td>
                                <td style="border: 1px solid #eee; padding: 8px;">{order.extracted_data.X_SHIPVIA or 'N/A'}</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #eee; padding: 8px; font-weight: bold; background-color: #f9f9f9;">Delivery Address:</td>
                                <td style="border: 1px solid #eee; padding: 8px;">{address_text}</td>
                            </tr>
                        </table>
                        
                        <h4 style="color: #333; margin: 15px 0 10px 0;">Order Lines ({len(order.extracted_data.order_lines)} items)</h4>
                        <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                            <thead>
                                <tr style="background-color: #f0f0f0;">
                                    <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">#</th>
                                    <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Stock Code</th>
                                    <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
            """

            for j, line in enumerate(order.extracted_data.order_lines, 1):
                line_style = "background-color: #f8f9fa;" if j % 2 == 0 else ""
                html += f"""
                                <tr style="{line_style}">
                                    <td style="border: 1px solid #ddd; padding: 8px;">{j}</td>
                                    <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">{line.stockcode}</td>
                                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">{line.orderquantity}</td>
                                </tr>
                """

            html += """
                            </tbody>
                        </table>
                        
                        <div style="background-color: #e8f5e8; border-left: 4px solid #4CAF50; padding: 10px; margin-top: 15px;">
                            <strong>Generated Files:</strong><br>
            """

            if hasattr(order, "markdown_filepath") and order.markdown_filepath:
                html += f"📄 Markdown: {order.markdown_filepath}<br>"

            if hasattr(order, "myob_filepath") and order.myob_filepath:
                html += f"📊 MYOB Payload: {order.myob_filepath}<br>"

            html += """
                        </div>
                    </div>
                </div>
            """
    else:
        html += """
                <div style="text-align: center; padding: 40px; color: #666; background-color: #f8f9fa; border-radius: 5px;">
                    <h3>No orders were processed in this run.</h3>
                    <p>No emails were found matching the criteria or all emails were filtered out.</p>
                </div>
        """

    html += f"""
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; border-top: 1px solid #ddd;">
                <p style="margin: 0;"><strong>TeamsysV0.1 Email Order Processor</strong></p>
                <p style="margin: 5px 0;">Files saved to: markdown/ and myob/ directories</p>
                <p style="margin: 5px 0;">Generated on {current_time}</p>
            </div>
        </div>
    </body>
    </html>
    """

    return html


def main():
    print(f"\n{'='*60}")
    print("📧 EMAIL ORDER PROCESSOR (Enhanced Label Selection)")
    print(f"{'='*60}")

    try:
        # Get runtime configuration
        from runtime_config import get_runtime_config, show_available_labels, validate_configuration

        runtime_config = get_runtime_config()
        runtime_config.configure_logging()

        # Handle special modes
        if runtime_config.should_show_labels():
            show_available_labels()
            return

        if runtime_config.should_validate_config():
            validate_configuration()
            return

        # Print configuration summary
        runtime_config.print_configuration_summary()

        # Get labels to process
        from label_selector import LabelSelector

        effective_labels = runtime_config.get_effective_labels()

        if effective_labels == 'all':
            # Process all available labels
            gmail_service = GmailService()
            selected_labels = gmail_service.get_available_processing_labels()
            if not selected_labels:
                print("❌ No processing-ready labels found. Exiting...")
                return
            print(f"🔄 Processing all {len(selected_labels)} available labels")
        elif effective_labels:
            # Use specified labels
            selected_labels = effective_labels
            print(f"🎯 Processing specified labels: {', '.join(selected_labels)}")
        else:
            # Interactive or default selection
            selector = LabelSelector()
            interactive = runtime_config.is_interactive_mode()
            selected_labels = selector.get_labels_with_fallback(interactive=interactive)

        if not selected_labels:
            print("❌ No labels selected. Exiting...")
            return

        logger.info(f"Processing labels: {selected_labels}")

        # Handle dry run mode
        if runtime_config.is_dry_run():
            print(f"\n🧪 DRY RUN MODE - Would process {len(selected_labels)} labels:")
            from query_builder import GmailQueryBuilder
            from email_limit_manager import EmailLimitManager

            builder = GmailQueryBuilder(runtime_config)
            limit_manager = EmailLimitManager(runtime_config)

            # Show detailed limit information
            print("\n📊 Email Limit Configuration:")
            limit_summary = limit_manager.get_all_limits_summary(selected_labels)

            for label in selected_labels:
                summary = builder.get_query_summary(label)
                limit_info = limit_summary[label]
                source_desc = limit_info['source_detail']
                print(f"  📧 {label}: {summary['query']}")
                print(f"      └─ Limit: {summary['max_results']} emails ({source_desc})")

            # Show global override if active
            global_override = limit_manager.get_global_override()
            if global_override:
                print(f"\n🌍 Global limit override active: {global_override} emails")

            print("\n✅ Dry run complete. No emails were actually processed.")
            return

        # Initialize processor and set labels
        processor = EmailOrderProcessor()
        processor.set_runtime_config(runtime_config)
        processor.set_processing_labels(selected_labels)

        # Run the extraction workflow
        processed_orders = processor.run_extraction_workflow()

        # Send detailed completion email with order information
        send_completion_email(
            subject=f"📧 Email Extraction Complete - {len(selected_labels)} Labels Processed",
            processed_orders=processed_orders,
        )

    except KeyboardInterrupt:
        print("\n👋 Interrupted by user. Exiting...")
        logger.info("Process interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ Fatal error: {e}")

        # Send error notification email
        try:
            send_completion_email(
                subject="❌ Email Extraction Failed - TeamsysV0.1",
                body=f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                        .error-container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                        .error-header {{ background-color: #f44336; color: white; padding: 20px; text-align: center; border-radius: 5px; }}
                        .error-content {{ background-color: #fff3cd; padding: 20px; margin-top: 20px; border-radius: 5px; border-left: 4px solid #f44336; }}
                        .error-details {{ background-color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; }}
                    </style>
                </head>
                <body>
                    <div class="error-container">
                        <div class="error-header">
                            <h1>❌ Email Extraction Failed</h1>
                            <p>TeamsysV0.1 encountered an error during processing</p>
                        </div>
                        <div class="error-content">
                            <h3>Error Details:</h3>
                            <div class="error-details">{str(e)}</div>
                            <p><strong>Time:</strong> {datetime.now().strftime("%B %d, %Y at %I:%M %p")}</p>
                            <p>Please check the logs for more detailed information.</p>
                        </div>
                    </div>
                </body>
                </html>
                """,
            )
        except Exception as email_error:
            logger.error(f"Failed to send error notification email: {email_error}")


if __name__ == "__main__":
    main()
