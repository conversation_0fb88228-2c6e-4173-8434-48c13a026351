"""
Interactive label selection module for email processing.
Provides user-friendly command-line interface for selecting Gmail labels to process.
"""

import logging
from typing import List, Optional
from gmail_service import GmailService

logger = logging.getLogger(__name__)


class LabelSelector:
    """Interactive label selection interface for email processing."""
    
    def __init__(self, gmail_service: Optional[GmailService] = None):
        """Initialize label selector with optional Gmail service."""
        self.gmail_service = gmail_service or GmailService()
    
    def get_user_label_selection(self, show_all_labels: bool = False) -> List[str]:
        """
        Interactive label selection via command line.
        
        Args:
            show_all_labels: If True, shows all user labels. If False, shows only labels with PDF attachments.
            
        Returns:
            List of selected label names.
        """
        try:
            # Get available labels
            if show_all_labels:
                available_labels = self.gmail_service.get_user_labels()
                label_type = "user-created"
            else:
                available_labels = self.gmail_service.get_available_processing_labels()
                label_type = "processing-ready (with PDF attachments)"
            
            if not available_labels:
                print(f"\n❌ No {label_type} labels found in your Gmail account.")
                print("Please ensure you have labels with emails containing PDF attachments.")
                return []
            
            # Display available labels with limit information
            print(f"\n📧 Available {label_type} labels for processing:")
            print("=" * 60)

            # Show limits for each label
            from email_limit_manager import EmailLimitManager
            limit_manager = EmailLimitManager()

            for i, label in enumerate(available_labels, 1):
                limit_info = limit_manager.get_limit_source_info(label)
                limit = limit_info['effective_limit']
                source = limit_info['source']

                # Format source indicator
                if source == 'default':
                    source_indicator = ""
                elif source == 'global_override':
                    source_indicator = " (global override)"
                elif source == 'per_label_override':
                    source_indicator = " (env override)"
                else:
                    source_indicator = " (configured)"

                print(f"{i:2d}. {label:<15} (max: {limit:3d} emails{source_indicator})")
            
            print(f"\n💡 Options:")
            print(f"   • Enter label numbers (comma-separated): 1,3,5")
            print(f"   • Enter 'all' to process all labels")
            print(f"   • Enter 'brady' for Brady-only (backward compatibility)")
            if not show_all_labels:
                print(f"   • Enter 'show-all' to see all user labels")
            print(f"   • Enter 'quit' to exit")
            
            while True:
                try:
                    choice = input(f"\n🔍 Your selection: ").strip()
                    
                    if choice.lower() == 'quit':
                        print("👋 Exiting...")
                        return []
                    
                    if choice.lower() == 'brady':
                        if 'Brady' in available_labels:
                            print("✅ Selected: Brady (backward compatibility mode)")
                            return ['Brady']
                        else:
                            print("❌ Brady label not found in available labels.")
                            continue
                    
                    if choice.lower() == 'all':
                        print(f"✅ Selected: All {len(available_labels)} labels")
                        return available_labels
                    
                    if choice.lower() == 'show-all' and not show_all_labels:
                        print("🔄 Switching to show all user labels...")
                        return self.get_user_label_selection(show_all_labels=True)
                    
                    # Parse comma-separated numbers
                    selected_indices = [int(x.strip()) for x in choice.split(",")]
                    selected_labels = []
                    
                    for i in selected_indices:
                        if 1 <= i <= len(available_labels):
                            selected_labels.append(available_labels[i-1])
                        else:
                            print(f"❌ Invalid selection: {i}. Please choose numbers between 1 and {len(available_labels)}.")
                            break
                    else:
                        # All selections were valid
                        if selected_labels:
                            print(f"✅ Selected {len(selected_labels)} label(s): {', '.join(selected_labels)}")
                            return selected_labels
                    
                except ValueError:
                    print("❌ Invalid input. Please enter numbers separated by commas, 'all', 'brady', or 'quit'.")
                except KeyboardInterrupt:
                    print("\n👋 Interrupted by user. Exiting...")
                    return []
                    
        except Exception as e:
            logger.error(f"Error in label selection: {e}")
            print(f"❌ Error during label selection: {e}")
            return []
    
    def get_runtime_labels_from_env(self) -> List[str]:
        """Get labels from environment variable override."""
        import os
        runtime_labels = os.getenv("RUNTIME_LABELS_TO_PROCESS")
        if runtime_labels:
            labels = [label.strip() for label in runtime_labels.split(",") if label.strip()]
            logger.info(f"Using runtime labels from environment: {labels}")
            return labels
        return []
    
    def get_labels_with_fallback(self, interactive: bool = True) -> List[str]:
        """
        Get labels with fallback strategy.
        
        Args:
            interactive: If True, use interactive selection. If False, use environment or default.
            
        Returns:
            List of selected label names.
        """
        # First try environment variable override
        runtime_labels = self.get_runtime_labels_from_env()
        if runtime_labels:
            print(f"🔧 Using labels from environment variable: {', '.join(runtime_labels)}")
            return runtime_labels
        
        # If interactive mode is disabled, use Brady as default
        if not interactive:
            print("🔄 Non-interactive mode: Using Brady as default label")
            return ['Brady']
        
        # Interactive selection
        selected_labels = self.get_user_label_selection()
        
        # Fallback to Brady if no selection made
        if not selected_labels:
            print("🔄 No labels selected. Falling back to Brady for backward compatibility.")
            return ['Brady']
        
        return selected_labels
    
    def validate_labels(self, labels: List[str]) -> List[str]:
        """
        Validate that the selected labels exist in Gmail.
        
        Args:
            labels: List of label names to validate.
            
        Returns:
            List of valid label names.
        """
        valid_labels = []
        all_user_labels = self.gmail_service.get_user_labels()
        
        for label in labels:
            if label in all_user_labels:
                valid_labels.append(label)
                logger.info(f"✅ Label '{label}' validated")
            else:
                logger.warning(f"❌ Label '{label}' not found in Gmail account")
                print(f"⚠️  Warning: Label '{label}' not found in your Gmail account")
        
        if not valid_labels:
            logger.warning("No valid labels found. Falling back to Brady.")
            print("🔄 No valid labels found. Falling back to Brady.")
            return ['Brady']
        
        return valid_labels


def get_user_label_selection() -> List[str]:
    """
    Convenience function for backward compatibility.
    
    Returns:
        List of selected label names.
    """
    selector = LabelSelector()
    return selector.get_labels_with_fallback()


if __name__ == "__main__":
    # Test the label selector
    print("🧪 Testing Label Selector...")
    selector = LabelSelector()
    selected = selector.get_user_label_selection()
    print(f"Selected labels: {selected}")
