# API Documentation - TeamsysV0.1

## Overview

TeamsysV0.1 provides several APIs and interfaces for email processing, order management, and system integration. This document covers the internal APIs, external integrations, and usage patterns.

## Core Service APIs

### GmailService API

#### Class: `GmailService`
Handles all Gmail API operations including authentication, email fetching, and label management.

```python
from gmail_service import GmailService

# Initialize service
gmail = GmailService()
```

#### Methods

##### `fetch_emails_by_labels(labels: List[str], max_results: int = 10) -> List[EmailData]`
Fetches emails from specified Gmail labels.

**Parameters:**
- `labels`: List of Gmail label names to search
- `max_results`: Maximum number of emails to return (default: 10)

**Returns:** List of `EmailData` objects

**Example:**
```python
emails = gmail.fetch_emails_by_labels(['Brady', 'RSEA'], max_results=5)
for email in emails:
    print(f"Subject: {email.subject}")
    print(f"Sender: {email.sender}")
```

##### `create_label(label_name: str, color: str = None) -> str`
Creates a new Gmail label with optional color.

**Parameters:**
- `label_name`: Name of the label to create
- `color`: Optional color for the label

**Returns:** Label ID

**Example:**
```python
label_id = gmail.create_label("Processed Orders", color="green")
```

##### `apply_label_to_email(email_id: str, label_name: str) -> bool`
Applies a label to a specific email.

**Parameters:**
- `email_id`: Gmail message ID
- `label_name`: Name of the label to apply

**Returns:** Success boolean

##### `mark_email_as_read(email_id: str) -> bool`
Marks an email as read.

**Parameters:**
- `email_id`: Gmail message ID

**Returns:** Success boolean

### LLMService API

#### Class: `LLMService`
Handles AI processing using Google Gemini for content analysis and data extraction.

```python
from llm_service import LLMService

# Initialize service
llm = LLMService()
```

#### Methods

##### `generate_markdown_summary(email_data: EmailData) -> str`
Generates a markdown summary of email content.

**Parameters:**
- `email_data`: EmailData object containing email information

**Returns:** Markdown formatted summary string

**Example:**
```python
summary = llm.generate_markdown_summary(email_data)
print(summary)
```

##### `extract_order_data(email_data: EmailData) -> ExtractedOrder`
Extracts structured order data from email content.

**Parameters:**
- `email_data`: EmailData object with email and attachment content

**Returns:** `ExtractedOrder` object with structured data

**Example:**
```python
order = llm.extract_order_data(email_data)
print(f"Customer ID: {order.customer_details.debtor_id}")
print(f"Order Lines: {len(order.order_lines)}")
```

##### `generate_myob_payload(extracted_order: ExtractedOrder) -> dict`
Generates MYOB-compatible JSON payload from extracted order data.

**Parameters:**
- `extracted_order`: ExtractedOrder object

**Returns:** Dictionary formatted for MYOB API

**Example:**
```python
payload = llm.generate_myob_payload(extracted_order)
# Ready for MYOB API submission
```

### MyobService API

#### Class: `MyobService`
Handles MYOB EXO API operations for order validation and creation.

```python
from myob_service import MyobService

# Initialize service
myob = MyobService()
```

#### Methods

##### `validate_order(order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]`
Validates order data with MYOB API.

**Parameters:**
- `order_data`: Dictionary containing order information

**Returns:** Validated order data or None if validation fails

**Example:**
```python
validated_order = myob.validate_order(order_payload)
if validated_order:
    print("Order validation successful")
```

##### `create_order(order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]`
Creates a sales order in MYOB EXO.

**Parameters:**
- `order_data`: Validated order data dictionary

**Returns:** Created order response or None if creation fails

**Example:**
```python
created_order = myob.create_order(validated_order)
if created_order:
    print(f"Order created with ID: {created_order.get('id')}")
```

##### `validate_and_create_order(payload: Dict[str, Any]) -> Optional[Dict[str, Any]]`
Combined validation and creation in a single operation.

**Parameters:**
- `payload`: Order data dictionary

**Returns:** Created order response or None if process fails

## Data Models

### EmailData
Represents email content and metadata.

```python
class EmailData(BaseModel):
    id: str                    # Gmail message ID
    subject: str              # Email subject line
    sender: str               # Sender email address
    timestamp: str            # Email timestamp
    body: str                 # Email body content
    attachments: List[dict]   # List of attachment data
    source_label: str         # Gmail label source
```

### ExtractedOrder
Represents structured order data extracted from emails.

```python
class ExtractedOrder(BaseModel):
    customer_details: CustomerDetails      # Customer information
    defaultlocationid: int = 1            # Default location ID
    order_status: int = 0                 # Order status code
    delivery_address: Optional[DeliveryAddress]  # Shipping address
    order_lines: List[OrderLine]          # Order line items
    X_SHIPVIA: Optional[str]              # Shipping method
```

### CustomerDetails
Customer and debtor information.

```python
class CustomerDetails(BaseModel):
    debtor_id: int                        # MYOB debtor ID
    customer_order_number: Optional[str]  # Customer's order number
```

### OrderLine
Individual order line item.

```python
class OrderLine(BaseModel):
    stockcode: str           # Product stock code
    orderquantity: float     # Quantity ordered
```

## Web Dashboard API

### EmailDashboard Class
Provides web interface for email management.

#### Endpoints

##### `GET /`
Main dashboard interface.

**Response:** HTML dashboard page

##### `POST /sync_emails`
Synchronizes emails from Gmail.

**Request Body:**
```json
{
    "labels": ["Brady", "RSEA"],
    "max_results": 50
}
```

**Response:**
```json
{
    "success": true,
    "emails_synced": 25,
    "message": "Successfully synced emails"
}
```

##### `POST /process_emails`
Processes selected emails.

**Request Body:**
```json
{
    "email_ids": ["email1", "email2"],
    "batch_mode": true
}
```

**Response:**
```json
{
    "success": true,
    "processed": 2,
    "failed": 0,
    "results": [...]
}
```

##### `GET /api/emails`
Retrieves email list with filtering.

**Query Parameters:**
- `status`: Filter by processing status
- `label`: Filter by Gmail label
- `search`: Text search in subject/sender
- `page`: Page number for pagination
- `per_page`: Items per page

**Response:**
```json
{
    "emails": [...],
    "total": 100,
    "page": 1,
    "per_page": 25
}
```

##### `GET /api/statistics`
Retrieves processing statistics.

**Response:**
```json
{
    "total_emails": 150,
    "processed": 120,
    "failed": 10,
    "review": 15,
    "unprocessed": 5,
    "daily_stats": [...]
}
```

## Comprehensive System API

### ComprehensiveEmailSystem Class
Main system orchestrator.

#### Methods

##### `start_system() -> bool`
Starts the comprehensive email processing system.

**Returns:** Success boolean

##### `stop_system() -> bool`
Stops the system gracefully.

**Returns:** Success boolean

##### `get_system_status() -> Dict[str, Any]`
Retrieves current system status.

**Returns:** Status information dictionary

**Example:**
```python
from comprehensive_email_system import ComprehensiveEmailSystem

system = ComprehensiveEmailSystem()
system.start_system()

# Get status
status = system.get_system_status()
print(f"System running: {status['running']}")
print(f"Uptime: {status['uptime']}")
```

## Configuration API

### Config Class
Centralized configuration management.

#### Properties

##### Gmail Configuration
- `GMAIL_SCOPES`: Required Gmail API scopes
- `GMAIL_CREDENTIALS_FILE`: Path to credentials file
- `GMAIL_TOKEN_FILE`: Path to token file
- `GMAIL_LABELS_TO_PROCESS`: List of labels to monitor
- `MAX_GMAIL_RESULTS`: Maximum emails per fetch

##### Gemini Configuration
- `GEMINI_API_KEY`: API key for Gemini AI
- `GEMINI_MODEL`: Model name to use

##### MYOB Configuration
- `MYOB_BASE_URL`: Base URL for MYOB API
- `MYOB_HEADERS`: Authentication headers

#### Methods

##### `validate_config() -> None`
Validates all configuration settings.

**Raises:** `ValueError` if configuration is invalid

## Error Handling

### Exception Types

#### `GmailServiceError`
Raised for Gmail API related errors.

#### `LLMServiceError`
Raised for AI processing errors.

#### `MyobServiceError`
Raised for MYOB API errors.

#### `ConfigurationError`
Raised for configuration issues.

### Error Response Format

All API methods return consistent error information:

```python
{
    "success": false,
    "error": "Error message",
    "error_type": "ErrorClassName",
    "details": {...}
}
```

## Usage Examples

### Complete Order Processing
```python
from gmail_service import GmailService
from llm_service import LLMService
from myob_service import MyobService

# Initialize services
gmail = GmailService()
llm = LLMService()
myob = MyobService()

# Fetch emails
emails = gmail.fetch_emails_by_labels(['Brady'], max_results=5)

for email in emails:
    try:
        # Extract order data
        order = llm.extract_order_data(email)
        
        # Generate MYOB payload
        payload = llm.generate_myob_payload(order)
        
        # Create order in MYOB
        result = myob.validate_and_create_order(payload)
        
        if result:
            # Mark as processed
            gmail.apply_label_to_email(email.id, "Processed")
            gmail.mark_email_as_read(email.id)
            
    except Exception as e:
        print(f"Error processing email {email.id}: {e}")
        gmail.apply_label_to_email(email.id, "Failed")
```

### Dashboard Integration
```python
from email_dashboard import EmailDashboard

# Start dashboard
dashboard = EmailDashboard()
dashboard.run(host='0.0.0.0', port=5000, debug=False)
```

### System Monitoring
```python
from comprehensive_email_system import ComprehensiveEmailSystem

system = ComprehensiveEmailSystem()

# Start system
if system.start_system():
    print("System started successfully")
    
    # Monitor status
    while True:
        status = system.get_system_status()
        if not status['healthy']:
            print("System health check failed")
            break
        
        time.sleep(60)  # Check every minute
```

## Rate Limiting and Best Practices

### Gmail API Limits
- **Quota**: 1 billion quota units per day
- **Rate Limit**: 250 quota units per user per 100 seconds
- **Best Practice**: Implement exponential backoff for retries

### Gemini AI Limits
- **Rate Limit**: Varies by model and tier
- **Best Practice**: Batch requests when possible
- **Token Limits**: Monitor input/output token usage

### MYOB API Limits
- **Rate Limit**: Depends on server configuration
- **Best Practice**: Validate before creating orders
- **Connection Limits**: Reuse connections when possible

### General Best Practices
1. **Error Handling**: Always implement proper error handling
2. **Logging**: Use structured logging for debugging
3. **Validation**: Validate data at each processing step
4. **Monitoring**: Monitor API usage and system health
5. **Security**: Secure credential storage and transmission