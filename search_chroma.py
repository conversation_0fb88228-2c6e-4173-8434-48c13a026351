#!/usr/bin/env python3
"""
Chroma Database Search Tool
Lists collections and allows searching through them.
"""

import chromadb
import sys
from typing import List, Optional, Dict, Any

class ChromaSearchTool:
    def __init__(self, persist_directory: str = "./chroma_db"):
        """Initialize the Chroma client."""
        self.client = chromadb.PersistentClient(path=persist_directory)
        
    def list_collections(self) -> List[str]:
        """List all collections in the database."""
        try:
            collections = self.client.list_collections()
            collection_names = [col.name for col in collections]
            return collection_names
        except Exception as e:
            print(f"Error listing collections: {e}")
            return []
    
    def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get information about a specific collection."""
        try:
            collection = self.client.get_collection(collection_name)
            count = collection.count()
            
            # Get a sample of documents to show structure
            sample = collection.peek(limit=3)
            
            return {
                "name": collection_name,
                "count": count,
                "sample_documents": sample.get("documents", []),
                "sample_metadatas": sample.get("metadatas", []),
                "sample_ids": sample.get("ids", [])
            }
        except Exception as e:
            print(f"Error getting collection info for '{collection_name}': {e}")
            return {}
    
    def search_collection(self, collection_name: str, query: str, n_results: int = 10, where: Optional[Dict] = None) -> Dict[str, Any]:
        """Search within a specific collection."""
        try:
            collection = self.client.get_collection(collection_name)
            results = collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where
            )
            return results
        except Exception as e:
            print(f"Error searching collection '{collection_name}': {e}")
            return {}
    
    def search_all_collections(self, query: str, n_results: int = 5) -> Dict[str, Any]:
        """Search across all collections."""
        collections = self.list_collections()
        all_results = {}
        
        for collection_name in collections:
            results = self.search_collection(collection_name, query, n_results)
            if results and results.get("documents") and results["documents"][0]:
                all_results[collection_name] = results
        
        return all_results

def main():
    """Main CLI interface."""
    tool = ChromaSearchTool()
    
    if len(sys.argv) == 1:
        # List collections
        print("Available Collections:")
        print("=" * 50)
        collections = tool.list_collections()
        
        if not collections:
            print("No collections found.")
            return
        
        for i, collection_name in enumerate(collections, 1):
            print(f"{i}. {collection_name}")
            info = tool.get_collection_info(collection_name)
            if info:
                print(f"   Documents: {info.get('count', 0)}")
                if info.get('sample_metadatas'):
                    sample_meta = info['sample_metadatas'][0] if info['sample_metadatas'] else {}
                    meta_keys = list(sample_meta.keys()) if sample_meta else []
                    print(f"   Metadata fields: {', '.join(meta_keys)}")
                print()
        
        print("\nUsage:")
        print("  python search_chroma.py 'search query'           - Search all collections")
        print("  python search_chroma.py 'collection_name' 'query' - Search specific collection")
        
    elif len(sys.argv) == 2:
        # Search all collections
        query = sys.argv[1]
        print(f"Searching all collections for: '{query}'")
        print("=" * 50)
        
        results = tool.search_all_collections(query)
        
        if not results:
            print("No results found.")
            return
        
        for collection_name, collection_results in results.items():
            print(f"\n📂 Collection: {collection_name}")
            print("-" * 40)
            
            documents = collection_results.get("documents", [[]])[0]
            metadatas = collection_results.get("metadatas", [[]])[0]
            distances = collection_results.get("distances", [[]])[0]
            ids = collection_results.get("ids", [[]])[0]
            
            for i, (doc, meta, distance, doc_id) in enumerate(zip(documents, metadatas, distances, ids)):
                print(f"\n🔍 Result {i+1} (Score: {1-distance:.3f})")
                print(f"ID: {doc_id}")
                if meta:
                    for key, value in meta.items():
                        print(f"{key}: {value}")
                print(f"Content: {doc[:200]}{'...' if len(doc) > 200 else ''}")
    
    elif len(sys.argv) == 3:
        # Search specific collection
        collection_name = sys.argv[1]
        query = sys.argv[2]
        
        print(f"Searching collection '{collection_name}' for: '{query}'")
        print("=" * 50)
        
        results = tool.search_collection(collection_name, query)
        
        if not results or not results.get("documents") or not results["documents"][0]:
            print("No results found.")
            return
        
        documents = results.get("documents", [[]])[0]
        metadatas = results.get("metadatas", [[]])[0]
        distances = results.get("distances", [[]])[0]
        ids = results.get("ids", [[]])[0]
        
        for i, (doc, meta, distance, doc_id) in enumerate(zip(documents, metadatas, distances, ids)):
            print(f"\n🔍 Result {i+1} (Score: {1-distance:.3f})")
            print(f"ID: {doc_id}")
            if meta:
                for key, value in meta.items():
                    print(f"  {key}: {value}")
            print(f"Content: {doc[:300]}{'...' if len(doc) > 300 else ''}")
            print("-" * 40)
    
    else:
        print("Usage:")
        print("  python search_chroma.py                         - List collections")
        print("  python search_chroma.py 'search query'          - Search all collections")
        print("  python search_chroma.py 'collection_name' 'query' - Search specific collection")

if __name__ == "__main__":
    main()
