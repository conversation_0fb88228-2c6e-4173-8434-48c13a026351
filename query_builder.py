"""
Dynamic Gmail query builder for email processing.
Generates appropriate Gmail search queries based on label-specific configurations.
"""

import logging
from typing import <PERSON><PERSON>, Dict, Any
from config import config

logger = logging.getLogger(__name__)


class GmailQueryBuilder:
    """Dynamic query builder for Gmail search operations."""
    
    def __init__(self):
        """Initialize the query builder with configuration."""
        self.config = config
    
    def build_label_query(self, label_name: str) -> Tuple[str, int]:
        """
        Build Gmail search query and max results for a specific label.
        
        Args:
            label_name: Name of the Gmail label to process.
            
        Returns:
            Tuple of (query_string, max_results).
        """
        try:
            # Get label-specific settings
            label_settings = self.config.get_label_settings(label_name)
            
            # Build query components
            query_parts = []
            
            # Base query filter (usually for attachments)
            base_query = label_settings.get('query_filter', 'has:attachment filename:pdf')
            if base_query:
                query_parts.append(base_query)
            
            # Date filter
            date_filter = label_settings.get('date_filter', '')
            if date_filter.strip():
                query_parts.append(date_filter)
            
            # Exclude already processed emails
            query_parts.append("-label:processed")
            
            # Combine all query parts
            query = " ".join(query_parts)
            
            # Get max results
            max_results = label_settings.get('max_results', 10)
            
            logger.info(f"Built query for '{label_name}': {query} (max: {max_results})")
            return query, max_results
            
        except Exception as e:
            logger.error(f"Error building query for label '{label_name}': {e}")
            # Fallback to default query
            return "has:attachment filename:pdf -label:processed", 10
    
    def build_batch_queries(self, label_names: list) -> Dict[str, Tuple[str, int]]:
        """
        Build queries for multiple labels.
        
        Args:
            label_names: List of label names to build queries for.
            
        Returns:
            Dictionary mapping label names to (query, max_results) tuples.
        """
        queries = {}
        
        for label_name in label_names:
            try:
                query, max_results = self.build_label_query(label_name)
                queries[label_name] = (query, max_results)
            except Exception as e:
                logger.error(f"Failed to build query for '{label_name}': {e}")
                # Use fallback query
                queries[label_name] = ("has:attachment filename:pdf -label:processed", 10)
        
        logger.info(f"Built queries for {len(queries)} labels")
        return queries
    
    def get_label_description(self, label_name: str) -> str:
        """
        Get human-readable description for a label.
        
        Args:
            label_name: Name of the label.
            
        Returns:
            Description string for the label.
        """
        label_settings = self.config.get_label_settings(label_name)
        return label_settings.get('description', f'{label_name} - Email processing label')
    
    def should_skip_packing_slips(self, label_name: str) -> bool:
        """
        Check if packing slips should be skipped for this label.
        
        Args:
            label_name: Name of the label.
            
        Returns:
            True if packing slips should be skipped, False otherwise.
        """
        label_settings = self.config.get_label_settings(label_name)
        return label_settings.get('skip_packing_slips', False)
    
    def validate_query(self, query: str) -> bool:
        """
        Validate a Gmail search query for basic syntax.
        
        Args:
            query: Gmail search query string.
            
        Returns:
            True if query appears valid, False otherwise.
        """
        try:
            # Basic validation checks
            if not query or not query.strip():
                return False
            
            # Check for balanced quotes
            quote_count = query.count('"')
            if quote_count % 2 != 0:
                logger.warning(f"Unbalanced quotes in query: {query}")
                return False
            
            # Check for valid Gmail operators
            valid_operators = [
                'has:', 'filename:', 'from:', 'to:', 'subject:', 'after:', 'before:',
                'label:', '-label:', 'in:', 'is:', 'newer_than:', 'older_than:'
            ]
            
            # Extract operators from query
            words = query.split()
            for word in words:
                if ':' in word and not word.startswith('-'):
                    operator = word.split(':')[0] + ':'
                    if operator not in valid_operators:
                        logger.warning(f"Unknown operator in query: {operator}")
                        # Don't fail validation for unknown operators, just warn
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating query '{query}': {e}")
            return False
    
    def get_query_summary(self, label_name: str) -> Dict[str, Any]:
        """
        Get a summary of query settings for a label.
        
        Args:
            label_name: Name of the label.
            
        Returns:
            Dictionary containing query summary information.
        """
        try:
            query, max_results = self.build_label_query(label_name)
            label_settings = self.config.get_label_settings(label_name)
            
            return {
                'label_name': label_name,
                'query': query,
                'max_results': max_results,
                'description': self.get_label_description(label_name),
                'skip_packing_slips': self.should_skip_packing_slips(label_name),
                'date_filter': label_settings.get('date_filter', ''),
                'base_filter': label_settings.get('query_filter', ''),
                'valid': self.validate_query(query)
            }
        except Exception as e:
            logger.error(f"Error getting query summary for '{label_name}': {e}")
            return {
                'label_name': label_name,
                'query': 'has:attachment filename:pdf -label:processed',
                'max_results': 10,
                'description': f'{label_name} - Error in configuration',
                'skip_packing_slips': False,
                'date_filter': '',
                'base_filter': 'has:attachment filename:pdf',
                'valid': False,
                'error': str(e)
            }


def build_label_query(label_name: str) -> Tuple[str, int]:
    """
    Convenience function for building a single label query.
    
    Args:
        label_name: Name of the label.
        
    Returns:
        Tuple of (query_string, max_results).
    """
    builder = GmailQueryBuilder()
    return builder.build_label_query(label_name)


if __name__ == "__main__":
    # Test the query builder
    print("🧪 Testing Gmail Query Builder...")
    builder = GmailQueryBuilder()
    
    test_labels = ['Brady', 'RSEA', 'Unknown_Label']
    
    for label in test_labels:
        print(f"\n📧 Testing label: {label}")
        summary = builder.get_query_summary(label)
        print(f"   Query: {summary['query']}")
        print(f"   Max Results: {summary['max_results']}")
        print(f"   Description: {summary['description']}")
        print(f"   Valid: {summary['valid']}")
