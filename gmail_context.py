"""
Enhanced Gmail Context Extractor for Gemini LLM
This module provides richer email context extraction for Gemini LLM supervision.
"""
import os
import base64
import logging
from typing import List, Optional
from bs4 import BeautifulSoup

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledApp<PERSON>low
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from config import config
from models import EmailData
from gmail_service import GmailService
from datetime import datetime
import json
import re


class GmailContextExtractor:
    def __init__(self):
        """Initialize the Enhanced Gmail Context Extractor with the Gmail API service."""
        self.service = GmailService().service  # Fixed: call GmailService instead of undefined gmail_service()
        self.target_labels = ['Woolworths', 'RSEA', 'Brady']
        self.output_dir = 'extracted_data/enhanced_context'
        self.attachments_dir = os.path.join(self.output_dir, 'attachments')  # For storing downloaded attachments

        # Ensure output directories exist
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        if not os.path.exists(self.attachments_dir):  # Create attachments directory
            os.makedirs(self.attachments_dir)

    def extract_enhanced_context(self):
        """
        Extract enhanced context from emails with target labels.

        Returns:
            Dictionary mapping email IDs to their enhanced context.
        """
        all_context = {}

        for label in self.target_labels:
            print(f"Extracting enhanced context from emails with label: {label}")
            emails = self.search_emails_by_label(label)

            for email_msg in emails:
                msg_id = email_msg['id']
                if msg_id in all_context:
                    continue  # Skip if already processed

                # Get basic email content
                email_content = self.get_email_content(msg_id)
                if not email_content:
                    continue

                # Get thread context
                thread_id = email_content.get('metadata', {}).get('threadId') or email_content.get('threadId')
                thread_context = self.get_thread_context(thread_id) if thread_id else []

                # Get sender history
                sender = email_content.get('headers', {}).get('From')
                sender_email_header = email_content.get('headers', {}).get('Reply-To', sender)
                sender_history = self.get_sender_history(sender_email_header) if sender_email_header else []

                # Combine all context
                enhanced_context = {
                    'email': email_content,
                    'thread_context': thread_context,
                    'sender_history': sender_history,
                    'label_context': label,
                    'extraction_time': datetime.now().isoformat()
                }

                # Save enhanced context
                context_path = self.save_enhanced_context(msg_id, enhanced_context)
                enhanced_context['context_path'] = context_path

                all_context[msg_id] = enhanced_context
                print(f"Enhanced context extracted for email: {email_content['headers'].get('Subject', 'No Subject')}")

        return all_context

    def search_emails_by_label(self, label_name):
        """
        Search for emails with a specific label.

        Args:
            label_name: The name of the Gmail label to search for.

        Returns:
            A list of email IDs matching the label.
        """
        try:
            # Query for emails with the specified label
            query = f'label:{label_name}'
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=1  # Increased limit to recent 20 emails
            ).execute()

            messages = results.get('messages', [])
            return messages
        except Exception as e:
            print(f"Error searching emails with label '{label_name}': {e}")
            return []

    def get_email_content(self, msg_id):
        """
        Retrieve the content of an email by its ID with enhanced metadata and download attachments.

        Args:
            msg_id: The ID of the email to retrieve.

        Returns:
            A dictionary containing email details and attachments (with local_path).
        """
        try:
            message = self.service.users().messages().get(
                userId='me',
                id=msg_id,
                format='full'  # Request full format to get all parts and body
            ).execute()

            headers = {}
            for header in message['payload']['headers']:
                headers[header['name']] = header['value']

            # Prefer X-Original-From, then X-Original-Sender, then Reply-To, then actual From
            x_orig_from = headers.get('X-Original-From')
            x_orig_sender = headers.get('X-Original-Sender')
            reply_to = headers.get('Reply-To')
            raw_from = headers.get('From')
            sender_display_header = x_orig_from or x_orig_sender or reply_to or raw_from
            # Use raw 'From' for history lookup
            sender_for_history_search = raw_from
            # Override headers['From'] to show the chosen display header
            headers['From'] = sender_display_header

            if 'parts' in message['payload']:
                parts = message['payload']['parts']
            else:  # Handle emails that are not multipart
                parts = [message['payload']]

            body = ""
            attachments_data = []

            for part in parts:
                self._extract_part_content(part, body, attachments_data, msg_id)

            return {
                'id': msg_id,
                'headers': headers,  # Original headers for reference
                'sender_display': sender_display_header,  # The derived sender for display
                'sender_for_history': sender_for_history_search,  # The sender to use for history lookup
                'body': body,
                'attachments': attachments_data,
                'metadata': {
                    'date': headers.get('Date'),
                    'threadId': message.get('threadId'),
                    'labelIds': message.get('labelIds', []),
                    'snippet': message.get('snippet', ''),
                    'internalDate': message.get('internalDate')
                }
            }
        except Exception as e:
            print(f"Error retrieving email content for ID '{msg_id}': {e}")
            return None

    def _extract_part_content(self, part, body_accumulator, attachments_accumulator, msg_id):
        """Helper function to recursively extract content from email parts."""
        mime_type = part.get('mimeType')
        filename = part.get('filename')

        if 'parts' in part:
            for sub_part in part['parts']:
                self._extract_part_content(sub_part, body_accumulator, attachments_accumulator, msg_id)

        if mime_type == 'text/plain' and 'data' in part['body']:
            try:
                body_data = part['body']['data']
                body_accumulator += base64.urlsafe_b64decode(body_data).decode('utf-8', errors='replace')
            except Exception as e:
                # print(f"Error decoding text/plain part for email {msg_id}: {e}")
                pass  # Added pass to fix indentation error

        elif mime_type == 'text/html' and 'data' in part['body'] and not body_accumulator:
            try:
                # Basic extraction, consider a proper HTML to text library if needed
                html_data = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8', errors='replace')
                soup = BeautifulSoup(html_data, "html.parser")
                body_accumulator += soup.get_text()
            except Exception as e:
                # print(f"Error decoding text/html part for email {msg_id}: {e}")
                pass  # Added pass to fix indentation error

        if filename and part['body'].get('attachmentId'):
            attachment_id = part['body']['attachmentId']
            try:
                attachment_content = self.service.users().messages().attachments().get(
                    userId='me', messageId=msg_id, id=attachment_id
                ).execute()

                file_data = base64.urlsafe_b64decode(attachment_content['data'].encode('UTF-8'))

                # Sanitize filename to prevent path traversal or invalid characters
                safe_filename = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)
                local_path = os.path.join(self.attachments_dir, f"{msg_id}_{safe_filename}")

                with open(local_path, 'wb') as f:
                    f.write(file_data)

                attachments_accumulator.append({
                    'id': attachment_id,
                    'filename': filename,
                    'mimeType': mime_type,
                    'size': attachment_content.get('size'),
                    'local_path': local_path  # Crucial for main script
                })
                print(f"Downloaded attachment: {filename} to {local_path}")
            except Exception as e:
                print(f"Error downloading attachment {filename} for email {msg_id}: {e}")
                attachments_accumulator.append({
                    'id': attachment_id,
                    'filename': filename,
                    'mimeType': mime_type,
                    'error': str(e),
                    'local_path': None
                })

    def get_thread_context(self, thread_id):
        """
        Get the context of an email thread.

        Args:
            thread_id: The ID of the thread.

        Returns:
            A list of messages in the thread.
        """
        try:
            thread = self.service.users().threads().get(
                userId='me',
                id=thread_id
            ).execute()

            thread_messages = []
            for message in thread['messages']:
                # Extract headers
                headers = {}
                for header in message['payload']['headers']:
                    headers[header['name']] = header['value']

                # Extract snippet
                snippet = message.get('snippet', '')

                thread_messages.append({
                    'id': message['id'],
                    'headers': headers,
                    'snippet': snippet,
                    'date': headers.get('Date')
                })

            # Sort by date
            thread_messages.sort(key=lambda x: x.get('date', ''))
            return thread_messages
        except Exception as e:
            print(f"Error retrieving thread context for thread ID '{thread_id}': {e}")
            return []

    def get_sender_history(self, sender_header_value):
        """
        Get the history of emails from a specific sender.

        Args:
            sender_header_value: The value of the 'From' header (or X-Original-Sender/Reply-To for display,
                                 but actual 'From' for history lookup).

        Returns:
            A list of recent emails from the sender.
        """
        try:
            # Extract email address from sender string
            email_match = re.search(r'<([^>]+)>', sender_header_value)
            if email_match:
                email_address = email_match.group(1)
            else:
                # If no <>, try to extract email from a "Name <email>" format or just use the value
                parts = sender_header_value.split(' ')
                if '@' in parts[-1]:
                    email_address = parts[-1].strip('<>')
                else:
                    email_address = sender_header_value

            # Query for emails from this sender
            query = f'from:{email_address}'
            results = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=5  # Limit to recent emails
            ).execute()

            messages = results.get('messages', [])
            sender_history = []
            for message in messages:
                msg = self.service.users().messages().get(
                    userId='me',
                    id=message['id'],
                    format='metadata',
                    metadataHeaders=['Subject', 'Date']
                ).execute()

                headers = {}
                for header in msg['payload']['headers']:
                    headers[header['name']] = header['value']

                sender_history.append({
                    'id': message['id'],
                    'subject': headers.get('Subject', 'No Subject'),
                    'date': headers.get('Date'),
                    'snippet': msg.get('snippet', '')
                })

            return sender_history
        except Exception as e:
            print(f"Error retrieving sender history for '{sender_header_value}': {e}")
            return []

    def save_enhanced_context(self, msg_id, enhanced_context):
        """
        Save enhanced context to a file.

        Args:
            msg_id: The ID of the email.
            enhanced_context: The enhanced context dictionary.

        Returns:
            Path to the saved file.
        """
        try:
            # Create a safe filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            context_path = os.path.join(self.output_dir, f"context_{msg_id}_{timestamp}.json")

            with open(context_path, 'w', encoding='utf-8') as f:
                json.dump(enhanced_context, f, indent=2, default=str)

            return context_path
        except Exception as e:
            print(f"Error saving enhanced context for email ID '{msg_id}': {e}")
            return None


def process_emails():
    extractor = GmailContextExtractor()
    enhanced_context = extractor.extract_enhanced_context()
    # Do something with the extracted context (e.g. pass it to the LLM or store it)
    print("Extracted context:", enhanced_context)


if __name__ == "__main__":
    process_emails()