#!/usr/bin/env python3
"""
Test script for Email Summary with Supabase integration
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_supabase_connection():
    """Test the Supabase connection."""
    print("Testing Supabase connection...")
    
    url = os.environ.get("SUPABASE_URL")
    key = os.environ.get("SUPABASE_KEY")
    
    print(f"SUPABASE_URL: {'✅ Set' if url else '❌ Not set'}")
    print(f"SUPABASE_KEY: {'✅ Set' if key else '❌ Not set'}")
    
    if not url or not key:
        print("\n❌ Supabase credentials not found in environment variables.")
        print("Please set SUPABASE_URL and SUPABASE_KEY in your .env file")
        return False
    
    try:
        from supabase import create_client
        supabase = create_client(url, key)
        
        # Test connection with a simple query
        result = supabase.table('email_summary_reports').select('count').limit(1).execute()
        print("✅ Supabase connection successful!")
        return True
        
    except ImportError:
        print("❌ Supabase package not installed. Run: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        print("Make sure your database tables are set up (run supabase_schema.sql)")
        return False

def test_email_summary_import():
    """Test importing the email summary module."""
    print("\nTesting email_summary module import...")
    
    try:
        from email_summary import EmailSummaryProcessor, SupabaseService
        print("✅ Email summary module imported successfully!")
        
        # Test initialization without database
        processor = EmailSummaryProcessor(use_database=False)
        print("✅ EmailSummaryProcessor initialized without database")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to import email_summary: {e}")
        return False

def main():
    """Main test function."""
    print("="*60)
    print("EMAIL SUMMARY WITH SUPABASE - INTEGRATION TEST")
    print("="*60)
    
    # Test 1: Supabase connection
    supabase_ok = test_supabase_connection()
    
    # Test 2: Email summary import
    import_ok = test_email_summary_import()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Supabase Connection: {'✅ PASS' if supabase_ok else '❌ FAIL'}")
    print(f"Module Import: {'✅ PASS' if import_ok else '❌ FAIL'}")
    
    if supabase_ok and import_ok:
        print("\n🎉 All tests passed! You're ready to use email summary with Supabase.")
        print("\nNext steps:")
        print("1. Run the SQL schema in your Supabase dashboard (supabase_schema.sql)")
        print("2. Execute: python email_summary.py")
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    main()
