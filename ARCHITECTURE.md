# System Architecture - TeamsysV0.1 Email Processing System

## Overview

TeamsysV0.1 is a comprehensive, AI-powered email processing system designed to automatically extract, validate, and process purchase orders from business emails. The system integrates Gmail API, Google Gemini AI, and MYOB EXO API to create a complete order processing pipeline.

## System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Gmail Integration Layer                  │
├─────────────────────────────────────────────────────────────────┤
│  Gmail API │ OAuth2 Auth │ Email Fetching │ Label Management    │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    AI Processing Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  Google Gemini │ PDF Extraction │ Context Memory │ Validation   │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                   Business Logic Layer                         │
├─────────────────────────────────────────────────────────────────┤
│  Order Processing │ Data Models │ Workflow Management │ Rules   │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Integration Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  MYOB EXO API │ Order Validation │ Sales Order Creation         │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    Management Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  Web Dashboard │ Monitoring │ Analytics │ User Interface       │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Configuration Management (`config.py`)
- **Purpose**: Centralized configuration and environment variable management
- **Features**: 
  - Environment variable validation
  - API credential management
  - Logging configuration with color support
  - Configuration validation methods

### 2. Data Models (`models.py`)
- **Purpose**: Pydantic-based data validation and structure definition
- **Models**:
  - `EmailData`: Email content and metadata
  - `ExtractedOrder`: Structured order information
  - `CustomerDetails`: Customer and debtor information
  - `DeliveryAddress`: Shipping address structure
  - `OrderLine`: Individual order line items
  - `ProcessedOrder`: Complete processed order with files

### 3. Gmail Integration (`gmail_service.py`)
- **Purpose**: Gmail API operations and email management
- **Features**:
  - OAuth2 authentication with token management
  - Email fetching with label and query filtering
  - PDF attachment extraction and processing
  - Label management (create, update, apply)
  - Email status tracking and updates

### 4. AI Processing (`llm_service.py`)
- **Purpose**: Google Gemini AI integration for intelligent processing
- **Features**:
  - Markdown summary generation from emails/PDFs
  - Structured data extraction using AI tools
  - MYOB payload generation with business rules
  - Context memory integration with ChromaDB
  - Multi-format content processing

### 5. MYOB Integration (`myob_service.py`)
- **Purpose**: MYOB EXO API integration for order management
- **Features**:
  - Order validation before creation
  - Sales order creation with full validation
  - Error handling and retry logic
  - Business rule validation
  - Stock code and customer verification

### 6. Memory System (`memory_client.py`)
- **Purpose**: Persistent context storage using ChromaDB
- **Features**:
  - Vector-based memory storage
  - Context retrieval for AI processing
  - Historical order pattern recognition
  - Semantic search capabilities

## System Workflows

### 1. Email Processing Workflow
```
1. Gmail Service → Fetch emails from configured labels
2. PDF Extractor → Extract text from PDF attachments
3. LLM Service → Generate markdown summary
4. LLM Service → Extract structured order data
5. Data Models → Validate extracted information
6. LLM Service → Generate MYOB API payload
7. User Interface → Display for approval (optional)
8. MYOB Service → Validate and create order
9. Gmail Service → Update email status/labels
```

### 2. Dashboard Management Workflow
```
1. Web Dashboard → Display email management interface
2. Gmail Sync → Real-time email synchronization
3. Batch Processing → Multi-email processing
4. Status Tracking → Visual status indicators
5. Analytics → Processing statistics and trends
6. File Management → Download generated files
```

### 3. Comprehensive System Workflow
```
1. Enhanced Universal Agent → Periodic email processing
2. Continuous Polling Agent → Real-time monitoring
3. Dashboard Integration → Web-based management
4. System Health Monitoring → Performance tracking
5. Backup Management → Configuration backups
```

## Data Flow

### Input Sources
- **Gmail Labels**: Configured email labels for different suppliers
- **PDF Attachments**: Purchase orders, invoices, packing slips
- **Email Content**: Subject lines, body text, sender information

### Processing Stages
1. **Extraction**: Email and PDF content extraction
2. **Analysis**: AI-powered content analysis and understanding
3. **Validation**: Data structure and business rule validation
4. **Transformation**: Convert to MYOB-compatible format
5. **Integration**: Create orders in MYOB EXO system

### Output Destinations
- **MYOB EXO**: Sales orders created in business system
- **File System**: Markdown summaries and JSON payloads
- **Database**: Processing history and analytics
- **Gmail**: Updated email labels and status

## Security Model

### Authentication
- **Gmail API**: OAuth2 with refresh token management
- **MYOB API**: Basic authentication with API keys
- **Environment Variables**: Secure credential storage

### Data Protection
- **Credential Management**: Environment-based configuration
- **Token Security**: Secure token storage and rotation
- **API Security**: Request validation and error handling
- **Data Validation**: Input sanitization and validation

### Access Control
- **Gmail Scopes**: Limited to necessary permissions
- **MYOB Permissions**: Order creation and validation only
- **File System**: Controlled access to generated files
- **Web Dashboard**: Local access with optional authentication

## Deployment Architecture

### Development Environment
```
Local Machine
├── Python 3.8+ Runtime
├── Gmail API Credentials
├── MYOB EXO Access
├── Environment Configuration
└── Local File Storage
```

### Production Environment
```
Production Server
├── Containerized Deployment (Optional)
├── Secure Credential Management
├── Persistent Storage
├── Monitoring and Logging
├── Backup Systems
└── Web Dashboard Access
```

## Integration Points

### External APIs
- **Gmail API**: Email access and management
- **Google Gemini**: AI processing and analysis
- **MYOB EXO API**: Business system integration

### Internal Components
- **ChromaDB**: Vector memory storage
- **SQLite**: Dashboard data storage
- **File System**: Document and configuration storage

## Scalability Considerations

### Performance Optimization
- **Batch Processing**: Multiple email processing
- **Async Operations**: Non-blocking I/O operations
- **Caching**: Memory-based context caching
- **Rate Limiting**: API request throttling

### Resource Management
- **Memory Usage**: Efficient data processing
- **Storage**: Configurable retention policies
- **Network**: Optimized API calls
- **CPU**: Parallel processing capabilities

## Monitoring and Observability

### Logging
- **Structured Logging**: JSON-formatted logs
- **Color Coding**: Visual log level identification
- **Component Tracking**: Per-module logging
- **Error Tracking**: Detailed error information

### Metrics
- **Processing Statistics**: Success/failure rates
- **Performance Metrics**: Processing times
- **System Health**: Component status monitoring
- **Business Metrics**: Order processing analytics

### Alerting
- **Email Notifications**: Processing completion alerts
- **Error Notifications**: Failure notifications
- **System Alerts**: Health check failures
- **Dashboard Alerts**: Real-time status updates

## Future Architecture Considerations

### Planned Enhancements
- **Microservices**: Component separation
- **Container Deployment**: Docker/Kubernetes
- **Cloud Integration**: Cloud-native deployment
- **Advanced Analytics**: Machine learning insights
- **Multi-tenant Support**: Multiple organization support

### Technology Evolution
- **API Versioning**: Backward compatibility
- **Database Migration**: Schema evolution
- **Security Updates**: Continuous security improvements
- **Performance Optimization**: Ongoing optimization