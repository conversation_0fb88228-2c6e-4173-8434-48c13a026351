# Installation Guide - TeamsysV0.1

## Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 2GB free space for dependencies and data
- **Network**: Internet connection for API access

### Required Accounts & Access
- **Gmail Account**: With API access enabled
- **Google Cloud Project**: For Gmail API and Gemini AI access
- **MYOB EXO System**: With API access and credentials
- **Gemini API Key**: For AI processing capabilities

## Installation Steps

### 1. Clone the Repository
```bash
git clone <repository-url>
cd TeamsysV0.1
```

### 2. Set Up Python Environment
```bash
# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies
```bash
# Install core dependencies
pip install -r requirements.txt

# Or install comprehensive dependencies (includes all optional features)
pip install -r requirements_comprehensive.txt
```

### 4. Run Setup Script
```bash
python setup.py
```
This script will:
- Create the `.env` file from template
- Validate required dependencies
- Check for credential files
- Set up initial configuration

## Configuration Setup

### 1. Gmail API Setup

#### Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Gmail API
4. Create credentials (OAuth 2.0 Client ID)
5. Download the credentials file as `credentials.json`

#### Configure Gmail Access
1. Place `credentials.json` in the project root
2. Run the authentication flow:
```bash
python gmail_service.py
```
3. Follow the browser authentication process
4. Grant necessary permissions for Gmail access

### 2. Gemini AI Setup

#### Get API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for configuration

### 3. MYOB EXO Setup

#### Obtain API Credentials
1. Contact your MYOB administrator for:
   - EXO server IP address and port
   - API username and password
   - API key and EXO token
2. Ensure your user has permissions for:
   - Sales order creation
   - Customer and stock validation
   - API access

### 4. Environment Configuration

Edit the `.env` file with your credentials:

```env
# Gmail API Configuration
GMAIL_CREDENTIALS_FILE=credentials.json
GMAIL_TOKEN_FILE=token.pickle
GMAIL_LABELS_TO_PROCESS=Brady,RSEA,Woolworths,Brierley,Highgate,Sitecraft
GMAIL_UNREAD_ONLY=False
MAX_GMAIL_RESULTS=10

# Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.0-flash-exp

# MYOB EXO Configuration
EXO_IP=your_myob_server_ip
EXO_PORT=your_myob_port
USER=your_myob_username
PWD=your_myob_password
API_KEY=your_myob_api_key
EXO_TOK=your_myob_token

# Logging Configuration
LOG_LEVEL=INFO
LOG_RECIPIENT_EMAIL=<EMAIL>
```

## Verification

### 1. Test System Components
```bash
# Test all system components
python test_system.py

# Test specific components
python test_gmail_service.py
python test_llm_service.py
python test_myob_service.py
```

### 2. Run Demo Scripts
```bash
# Test LLM processing with sample data
python demo_llm.py

# Test MYOB integration
python demo_myob_poster.py

# Test dashboard with sample data
python demo_dashboard.py
```

### 3. Verify Configuration
```bash
# Check system status
python comprehensive_email_system.py --status

# Validate configuration
python config.py
```

## Troubleshooting

### Common Issues

#### Gmail Authentication Errors
```bash
# Clear existing tokens and re-authenticate
rm token.pickle
python gmail_service.py
```

#### Missing Dependencies
```bash
# Reinstall dependencies
pip install --upgrade -r requirements.txt
```

#### MYOB Connection Issues
- Verify server IP and port accessibility
- Check firewall settings
- Validate credentials with MYOB administrator
- Test network connectivity to MYOB server

#### Gemini API Errors
- Verify API key is correct and active
- Check quota limits in Google Cloud Console
- Ensure billing is enabled for the project

### Log Analysis
```bash
# Check system logs for errors
tail -f logs/system.log

# View specific component logs
grep "ERROR" logs/gmail_service.log
grep "ERROR" logs/llm_service.log
grep "ERROR" logs/myob_service.log
```

### Getting Help

1. **Check Documentation**: Review component-specific README files
2. **Review Logs**: Check log files for detailed error information
3. **Test Components**: Use demo scripts to isolate issues
4. **Validate Configuration**: Ensure all credentials are correct

## Next Steps

After successful installation:

1. **Configure Processing Rules**: Edit `processing_rules.yaml`
2. **Set Up Email Labels**: Configure Gmail labels for suppliers
3. **Test with Sample Data**: Process test emails before production use
4. **Configure Dashboard**: Customize dashboard settings
5. **Set Up Monitoring**: Configure system health checks

## Security Considerations

### Credential Security
- Never commit `.env` file to version control
- Use strong passwords for all accounts
- Regularly rotate API keys and tokens
- Limit API permissions to minimum required

### Network Security
- Use HTTPS for all API communications
- Configure firewall rules for MYOB access
- Monitor API usage for unusual activity
- Implement rate limiting where appropriate

### Data Protection
- Regularly backup configuration files
- Secure storage of processed emails and orders
- Implement data retention policies
- Monitor file system permissions

## Production Deployment

### Recommended Setup
- Use dedicated server or cloud instance
- Configure automated backups
- Set up monitoring and alerting
- Implement log rotation
- Configure system service for auto-start

### Performance Optimization
- Adjust batch processing sizes
- Configure appropriate polling intervals
- Monitor memory and CPU usage
- Optimize database queries and storage