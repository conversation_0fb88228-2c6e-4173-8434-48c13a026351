#!/usr/bin/env python3
"""
Demo script to showcase the Enhanced Email Dashboard functionality.
"""
import os
import sys
import time
import sqlite3
from datetime import datetime, timedelta

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_data():
    """Create demo data in the dashboard database for demonstration."""
    print("📊 Creating demo data for dashboard...")
    
    # First initialize the dashboard to create the database
    from email_dashboard import EmailDashboard
    dashboard = EmailDashboard()
    
    db_path = "email_dashboard.db"
    
    # Sample email data
    demo_emails = [
        {
            'id': 'demo_001',
            'subject': 'Purchase Order #PO12345 - Brady Safety Equipment',
            'sender': '<EMAIL>',
            'source_label': 'Brady',
            'status': 'processed',
            'attachment_count': 2,
            'timestamp': (datetime.now() - timedelta(hours=2)).isoformat()
        },
        {
            'id': 'demo_002', 
            'subject': 'URGENT: Safety Gear Order #RSA789',
            'sender': '<EMAIL>',
            'source_label': 'RSEA',
            'status': 'review',
            'attachment_count': 1,
            'timestamp': (datetime.now() - timedelta(hours=1)).isoformat()
        },
        {
            'id': 'demo_003',
            'subject': 'Equipment Request - Store #1247',
            'sender': '<EMAIL>',
            'source_label': 'Woolworths',
            'status': 'failed',
            'attachment_count': 3,
            'timestamp': (datetime.now() - timedelta(minutes=30)).isoformat(),
            'error_message': 'PDF extraction failed - corrupted attachment'
        },
        {
            'id': 'demo_004',
            'subject': 'Industrial Equipment Order #BR567',
            'sender': '<EMAIL>',
            'source_label': 'Brierley',
            'status': 'unprocessed',
            'attachment_count': 1,
            'timestamp': (datetime.now() - timedelta(minutes=15)).isoformat()
        },
        {
            'id': 'demo_005',
            'subject': 'Construction Supply Order #HG321',
            'sender': '<EMAIL>',
            'source_label': 'Highgate',
            'status': 'processed',
            'attachment_count': 2,
            'timestamp': (datetime.now() - timedelta(hours=4)).isoformat(),
            'markdown_file': 'markdown/demo_highgate_order.md',
            'myob_file': 'myob/demo_highgate_order.json'
        }
    ]
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Insert demo emails
    for email in demo_emails:
        cursor.execute('''
            INSERT OR REPLACE INTO emails 
            (id, subject, sender, timestamp, source_label, status, attachment_count, 
             error_message, markdown_file, myob_file, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            email['id'], email['subject'], email['sender'], email['timestamp'],
            email['source_label'], email['status'], email['attachment_count'],
            email.get('error_message'), email.get('markdown_file'), email.get('myob_file'),
            datetime.now().isoformat()
        ))
    
    conn.commit()
    conn.close()
    print(f"✅ Added {len(demo_emails)} demo emails to dashboard")

def show_demo_info():
    """Display demo information."""
    print("\n" + "="*80)
    print("📧 ENHANCED EMAIL DASHBOARD - DEMO MODE")
    print("="*80)
    print("🎯 Demo Features:")
    print("   • Sample emails from different suppliers")
    print("   • Various processing statuses (Processed, Review, Failed, Unprocessed)")
    print("   • Interactive filtering and search")
    print("   • Batch processing simulation")
    print("   • Statistics and analytics")
    print("\n📊 Demo Data Includes:")
    print("   • Brady Safety Equipment - Processed ✅")
    print("   • RSEA Safety Gear - Needs Review ⚠️")
    print("   • Woolworths Equipment - Failed ❌")
    print("   • Brierley Industrial - Unprocessed ⏳")
    print("   • Highgate Construction - Processed ✅")
    print("\n🌐 Dashboard Access:")
    print("   • URL: http://localhost:5000")
    print("   • Try filtering by status or supplier")
    print("   • Test search functionality")
    print("   • Explore email details and file downloads")
    print("="*80)

def main():
    """Run the dashboard demo."""
    try:
        # Create demo data
        create_demo_data()
        
        # Show demo information
        show_demo_info()
        
        # Import and start dashboard
        from email_dashboard import EmailDashboard
        
        print("\n🚀 Starting Enhanced Email Dashboard...")
        print("⚡ Demo mode with sample data")
        print("🔄 Dashboard will open at http://localhost:5000")
        
        dashboard = EmailDashboard()
        dashboard.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n⚠️  Dashboard stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting dashboard demo: {e}")
        print("💡 Make sure Flask is installed: pip install flask")
        sys.exit(1)

if __name__ == "__main__":
    main()