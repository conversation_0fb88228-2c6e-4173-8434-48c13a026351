"""
MYOB API service for creating sales orders.
"""
import json
import logging
import requests
from typing import Dict, Any, Optional

from utils.config import config

logger = logging.getLogger(__name__)

class MyobService:
    """Service for MYOB EXO API operations."""
    
    def __init__(self):
        self.base_url = config.MYOB_BASE_URL
        self.headers = config.MYOB_HEADERS
        logger.info(f"Initialized MYOB service with base URL: {self.base_url}")
    def validate_order(self, minimal_order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Validate order with MYOB using minimal required fields.
        Returns fully populated order data ready for creation.
        """
        logger.info("Validating order with MYOB API")

        # Create minimal validation payload
        validation_payload = {
            "debtorid": minimal_order_data["debtorid"],
            "status": minimal_order_data["status"],
            "lines": []
        }

        # Add minimal line data (just stockcode and quantity)
        for line in minimal_order_data.get("lines", []):
            validation_payload["lines"].append({
                "stockcode": line["stockcode"],
                "orderquantity": line.get("orderquantity", 1.0)
            })

        # Include any other critical fields if present
        optional_fields = ["customerordernumber", "deliveryaddress", "extrafields"]
        for field in optional_fields:
            if field in minimal_order_data:
                validation_payload[field] = minimal_order_data[field]

        logger.debug(f"Validation payload: {json.dumps(validation_payload, indent=2)}")

        try:
            response = requests.post(
                f"{self.base_url}/salesorder/validate",
                json=validation_payload,
                headers=self.headers,
                timeout=30
            )
            response.raise_for_status()

            validation_result = response.json()
            logger.info("Order validation successful")

            return validation_result # This return should be inside the try block

        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB validation HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"MYOB validation error: {e}")
            raise
    
    def create_order(self, order_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create order using validated payload from MYOB."""
        logger.info("Creating order in MYOB")
        
        # Use the validated payload as-is (MYOB returns exactly what we need to POST)
        logger.debug(f"Order payload: {json.dumps(order_data, indent=2)}")
        
        try:
            response = requests.post(
                f"{self.base_url}/salesorder/",
                json=order_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 201:
                logger.info("Order created successfully in MYOB")
                return response.json()
            else:
                logger.error(f"MYOB order creation HTTP error: {response.status_code} {response.reason}")
                logger.error(f"Response: {response.text}")
                response.raise_for_status()
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"MYOB order creation HTTP error: {e}")
            logger.error(f"Response: {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"MYOB order creation error: {e}")
            raise
    def validate_and_create_order(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate and create sales order in MYOB."""
        try:
            # First validate the order
            validation_result = self.validate_order(payload)
            
            if not validation_result or 'order' not in validation_result:
                logger.error("Validation response missing 'order' object")
                return None
            
            validated_payload = validation_result['order']
            
            # Then create the order with validated payload
            created_order = self.create_order(validated_payload)
            
            return created_order
        except Exception as e:
            logger.error(f"Failed to validate and create MYOB order: {e}")
            return None
    
    def create_sales_order(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create sales order in MYOB (alias for validate_and_create_order)."""
        return self.validate_and_create_order(payload)
