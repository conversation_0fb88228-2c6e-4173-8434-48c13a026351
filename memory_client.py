import chromadb

class PersistentMemoryClient:
    def __init__(self, persist_directory="./chroma_db"):
        """
        Initializes the persistent ChromaDB client.

        Args:
            persist_directory (str): The directory to store the ChromaDB data.
        """
        self.client = chromadb.PersistentClient(path=persist_directory)
        self.collection = self.client.get_or_create_collection(name="llm_memory")

    def add_memory(self, documents, metadatas=None, ids=None):
        """
        Adds documents to the memory collection.

        Args:
            documents (list): A list of documents (strings) to add.
            metadatas (list, optional): A list of metadata dictionaries for each document.
            ids (list, optional): A list of unique IDs for each document.
        """
        self.collection.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )

    def query_memory(self, query_texts, n_results=10, where=None, where_document=None):
        """
        Queries the memory collection.

        Args:
            query_texts (list): A list of query strings.
            n_results (int): The number of results to return.
            where (dict, optional): A dictionary for metadata filtering.
            where_document (dict, optional): A dictionary for document content filtering.

        Returns:
            dict: The query results.
        """
        results = self.collection.query(
            query_texts=query_texts,
            n_results=n_results,
            where=where,
            where_document=where_document
        )
        return results

    def delete_memory(self, ids=None, where=None):
        """
        Deletes documents from the memory collection.

        Args:
            ids (list, optional): A list of IDs to delete.
            where (dict, optional): A dictionary for metadata filtering.
        """
        self.collection.delete(
            ids=ids,
            where=where
        )

if __name__ == "__main__":
    # Example Usage
    memory_client = PersistentMemoryClient()

    # Add some memories
    memory_client.add_memory(
        documents=["The capital of France is Paris.", "The highest mountain is Mount Everest."],
        ids=["fact1", "fact2"]
    )

    # Query memories
    results = memory_client.query_memory(query_texts=["What is the capital of France?"])
    print("Query Results:", results)

    # Add more memories with metadata
    memory_client.add_memory(
        documents=["Apples are fruits.", "Carrots are vegetables."],
        metadatas=[{"type": "fruit"}, {"type": "vegetable"}],
        ids=["food1", "food2"]
    )

    # Query with metadata filter
    results_filtered = memory_client.query_memory(query_texts=["Tell me about fruits."], where={"type": "fruit"})
    print("Filtered Query Results:", results_filtered)

    # Delete a memory
    memory_client.delete_memory(ids=["fact1"])

    # Verify deletion
    results_after_delete = memory_client.query_memory(query_texts=["What is the capital of France?"])
    print("Results after deletion:", results_after_delete)
