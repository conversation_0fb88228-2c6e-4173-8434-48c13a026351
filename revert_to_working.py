#!/usr/bin/env python3
"""
Revert to a working structure by copying files back to root and fixing imports.
This ensures the system works while maintaining the organized structure.
"""
import os
import shutil

def copy_files_to_root():
    """Copy key files back to root for direct access."""
    
    files_to_copy = [
        ("utils/config.py", "config.py"),
        ("utils/models.py", "models.py"), 
        ("services/gmail_service.py", "gmail_service.py"),
        ("services/llm_service.py", "llm_service.py"),
        ("services/myob_service.py", "myob_service.py"),
        ("services/memory_client.py", "memory_client.py"),
        ("utils/pdf_extractor.py", "pdf_extractor.py"),
        ("orchestrators/main_processor.py", "main_processor.py"),
        ("orchestrators/email_dashboard.py", "email_dashboard.py"),
        ("orchestrators/comprehensive_email_system.py", "comprehensive_email_system.py"),
    ]
    
    print("📋 Copying key files to root for direct access...")
    
    for source, dest in files_to_copy:
        if os.path.exists(source):
            try:
                shutil.copy2(source, dest)
                print(f"  ✅ Copied {source} -> {dest}")
            except Exception as e:
                print(f"  ❌ Failed to copy {source}: {e}")
        else:
            print(f"  ⚠️  Source not found: {source}")

def fix_root_imports():
    """Fix imports in the copied root files to use direct imports."""
    
    files_to_fix = [
        "main_processor.py",
        "email_dashboard.py", 
        "comprehensive_email_system.py",
        "gmail_service.py",
        "llm_service.py"
    ]
    
    print("\n🔧 Fixing imports in root files...")
    
    import_fixes = {
        "from utils.config import config": "from config import config",
        "from utils.models import": "from models import",
        "from services.gmail_service import": "from gmail_service import",
        "from services.llm_service import": "from llm_service import", 
        "from services.myob_service import": "from myob_service import",
        "from services.memory_client import": "from memory_client import",
        "from utils.pdf_extractor import": "from pdf_extractor import",
        "from orchestrators.main_processor import": "from main_processor import",
        "from orchestrators.email_dashboard import": "from email_dashboard import",
        "from orchestrators.comprehensive_email_system import": "from comprehensive_email_system import",
        "from agents.enhanced_universal_agent import": "from enhanced_universal_agent import",
        "from agents.continuous_polling_agent import": "from continuous_polling_agent import",
    }
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"  🔧 Fixing {file_path}...")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Apply import fixes
            modified = False
            for old_import, new_import in import_fixes.items():
                if old_import in content:
                    content = content.replace(old_import, new_import)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"    ✅ Fixed imports in {file_path}")
            else:
                print(f"    ℹ️  No changes needed in {file_path}")

def create_working_launcher():
    """Create a simple launcher that uses root files."""
    
    launcher_content = '''#!/usr/bin/env python3
"""
Working launcher for TeamsysV0.1 using root-level imports.
"""
import sys
import os

def test_imports():
    """Test that all imports work."""
    print("🧪 Testing Working Imports...")
    print("=" * 40)
    
    try:
        import config
        print("✅ config.py imported")
        print(f"   Gmail model: {config.config.GEMINI_MODEL}")
    except Exception as e:
        print(f"❌ config.py failed: {e}")
        return False
    
    try:
        import models
        print("✅ models.py imported")
        
        # Test model creation
        customer = models.CustomerDetails(debtor_id=6207)
        print(f"   Created customer: {customer.debtor_id}")
    except Exception as e:
        print(f"❌ models.py failed: {e}")
        return False
    
    try:
        import gmail_service
        print("✅ gmail_service.py imported")
    except Exception as e:
        print(f"❌ gmail_service.py failed: {e}")
        return False
    
    try:
        import llm_service
        print("✅ llm_service.py imported")
    except Exception as e:
        print(f"❌ llm_service.py failed: {e}")
        return False
    
    try:
        import myob_service
        print("✅ myob_service.py imported")
    except Exception as e:
        print(f"❌ myob_service.py failed: {e}")
        return False
    
    print("\\n🎉 All imports working!")
    return True

def start_dashboard():
    """Start the email dashboard."""
    try:
        import email_dashboard
        print("🚀 Starting Email Dashboard...")
        dashboard = email_dashboard.EmailDashboard()
        dashboard.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Dashboard failed: {e}")
        import traceback
        traceback.print_exc()

def start_processor():
    """Start the main processor."""
    try:
        import main_processor
        print("🚀 Starting Main Processor...")
        processor = main_processor.EmailOrderProcessor()
        processor.run()
    except Exception as e:
        print(f"❌ Processor failed: {e}")
        import traceback
        traceback.print_exc()

def start_demo():
    """Start demo dashboard."""
    try:
        print("🚀 Starting Demo Dashboard...")
        import demo_dashboard
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Working TeamsysV0.1 Launcher')
    parser.add_argument('action', choices=['test', 'dashboard', 'processor', 'demo'], 
                       help='Action to perform')
    
    args = parser.parse_args()
    
    if args.action == 'test':
        success = test_imports()
        if success:
            print("\\n✅ System is working! You can now run:")
            print("   python working_launcher.py dashboard")
            print("   python working_launcher.py processor") 
            print("   python working_launcher.py demo")
    elif args.action == 'dashboard':
        start_dashboard()
    elif args.action == 'processor':
        start_processor()
    elif args.action == 'demo':
        start_demo()
'''
    
    with open('working_launcher.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ Created working_launcher.py")

def main():
    """Main function to create working structure."""
    print("🔧 Creating Working Structure for TeamsysV0.1")
    print("=" * 50)
    print("This will copy key files to root and fix imports")
    print("so the system works while keeping the organized structure.")
    print()
    
    # Step 1: Copy files to root
    copy_files_to_root()
    
    # Step 2: Fix imports in root files
    fix_root_imports()
    
    # Step 3: Create working launcher
    create_working_launcher()
    
    print("\\n" + "=" * 50)
    print("🎉 Working structure created!")
    print("\\n🧪 Test the system:")
    print("   python working_launcher.py test")
    print("\\n🚀 Start components:")
    print("   python working_launcher.py demo")
    print("   python working_launcher.py dashboard")
    print("   python working_launcher.py processor")
    print("\\n📁 Note: Organized structure in folders is preserved")
    print("   Root files are working copies for easy access")

if __name__ == "__main__":
    main()