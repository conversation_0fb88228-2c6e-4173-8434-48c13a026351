"""
LLM service for parsing emails and generating structured data.
"""
import json
import logging
import config

from typing import Optional, Dict, Any, List

from google.generativeai.generative_models import GenerativeModel
from google.generativeai.client import configure as configure_gemini

from utils.config import config
from utils.models import ExtractedOrder
from services.memory_client import PersistentMemoryClient

logger = logging.getLogger(__name__)

class LLMService:
    """Service for LLM operations using Gemini."""
    def __init__(self):
        configure_gemini(api_key=config.GEMINI_API_KEY)
        self.model = GenerativeModel(config.GEMINI_MODEL)
        self.memory_client = PersistentMemoryClient()
        logger.info(f"Initialized LLM service with model: {config.GEMINI_MODEL}")
        logger.info("Connected to Chroma database for memory retrieval")

    def _gemini_struct_to_dict(self, value):
        """Convert Gemini Struct/MapComposite to Python dict."""
        if hasattr(value, '__iter__') and not isinstance(value, (str, bytes)) and not hasattr(value, 'items'):
            return [self._gemini_struct_to_dict(item) for item in value]
        elif isinstance(value, dict):
            py_dict = {}
            for key, v_item in value.items():
                converted_value = self._gemini_struct_to_dict(v_item)
                # Convert debtor_id to integer if it's a float
                if key == "debtor_id" and isinstance(converted_value, float):
                    converted_value = int(converted_value)
                py_dict[key] = converted_value
            return py_dict
        return value

    def get_relevant_context(self, query: str, n_results: int = 5) -> List[str]:
        """Query the memory database for relevant context."""
        try:
            results = self.memory_client.query_memory(
                query_texts=[query],
                n_results=n_results
            )
            
            contexts = []
            documents_list = results.get("documents")
            if results and documents_list and isinstance(documents_list, list) and documents_list[0]:
                documents = documents_list[0]
                metadatas_list = results.get("metadatas", [[]])
                metadatas = metadatas_list[0] if metadatas_list and metadatas_list[0] is not None else []
                
                for i, doc in enumerate(documents):
                    meta = metadatas[i] if i < len(metadatas) else {}
                    context = f"Document: {doc}"
                    if meta:
                        context += f"\nMetadata: {meta}"
                    contexts.append(context)
            
            logger.info(f"Retrieved {len(contexts)} relevant contexts for query: {query[:50]}...")
            return contexts
            
        except Exception as e:
            logger.error(f"Error querying memory database: {e}")
            return []

    def store_order_in_memory(self, email_data, extracted_order: ExtractedOrder, order_id: str) -> None:
        """Store processed order data in memory for future reference."""
        try:
            # Create a comprehensive document for storage
            document = f"""
Order ID: {order_id}
Customer: {extracted_order.customer_details.debtor_id}
Purchase Order: {extracted_order.customer_details.customer_order_number}
Email Subject: {email_data.subject}
Email Sender: {email_data.sender}

Products:
{chr(10).join([f"- {line.stockcode}: {line.orderquantity}" for line in extracted_order.order_lines])}

Shipping: {extracted_order.X_SHIPVIA}

Delivery Address:
{extracted_order.delivery_address.dict() if extracted_order.delivery_address else "None"}
"""

            metadata = {
                "order_id": order_id,
                "debtor_id": str(extracted_order.customer_details.debtor_id),
                "po_number": extracted_order.customer_details.customer_order_number or "",
                "sender": email_data.sender,
                "subject": email_data.subject,
                "timestamp": email_data.timestamp if hasattr(email_data, 'timestamp') else "",
                "shipping": extracted_order.X_SHIPVIA or ""
            }

            self.memory_client.add_memory(
                documents=[document],
                metadatas=[metadata],
                ids=[f"order_{order_id}"]
            )
            
            logger.info(f"Stored order {order_id} in memory database")
            
        except Exception as e:
            logger.error(f"Error storing order in memory: {e}")

    def generate_markdown_summary(self, email_data, pdf_content: str = "") -> str:
        """Generate a structured markdown summary of the email and PDF content."""

        full_content = f"""
Email Subject: {email_data.subject}
Email Sender: {email_data.sender}
Email Body:
{email_data.body}

PDF Attachments Content:
{pdf_content}
"""

        # Get relevant context from memory
        memory_contexts = self.get_relevant_context(f"{email_data.subject} {email_data.sender}", n_results=2)
        context_text = ""
        if memory_contexts:
            context_text = f"\n\nSimilar previous orders for reference:\n" + "\n---\n".join(memory_contexts)

        prompt = f"""
Analyze the following email and PDF content to create a structured markdown summary of important order information.

Focus on extracting:
- Customer details (company name, contact info)
- Order/Purchase order numbers
- Delivery addresses
- Product details (codes, quantities, descriptions)
- Shipping information
- Any special instructions or notes

Present the information in a clear, structured markdown format with appropriate headers and bullet points.

{context_text}

Content to analyze:
{full_content}

Please provide a comprehensive markdown summary:
"""

        try:
            response = self.model.generate_content(prompt)
            markdown_summary = response.text if hasattr(response, 'text') else "Failed to generate summary"
            logger.info("Generated markdown summary successfully")
            return markdown_summary
        except Exception as e:
            logger.error(f"Error generating markdown summary: {e}")
            return f"Error generating summary: {str(e)}"

    def extract_order_data(self, content: str) -> Optional[Dict[str, Any]]:
        """Extract structured order data from content using LLM with memory context."""
        
        # Get relevant context from memory database
        memory_contexts = self.get_relevant_context(content[:500], n_results=3)
        context_text = ""
        if memory_contexts:
            context_text = "\nRelevant previous examples:\n" + "\n---\n".join(memory_contexts)

        tool_schema = {
            "name": "extract_sales_order_data",
            "description": "Extracts sales order details from text content into a structured JSON format.",
            "parameters": {
                "type_": "OBJECT",
                "properties": {
                    "customer_details": {
                        "type_": "OBJECT",
                        "properties": {
                            "debtor_id": {
                                "type_": "INTEGER",
                                "description": "The unique numerical ID of the customer based on company name."
                            },
                            "customer_order_number": {
                                "type_": "STRING",
                                "description": "The customer's full purchase order (PO) number. Examples: 'LVA4401196688', 'DTS4401193941', 'PO12345'."
                            }
                        },
                        "required": ["debtor_id"]
                    },
                    "order_status": {
                        "type_": "INTEGER",
                        "description": "Order status: 0=Not Processed, 3=Quotation. Default to 3.",
                    },
                    "delivery_address": {
                        "type_": "OBJECT",
                        "properties": {
                            "line1": {"type_": "STRING"},
                            "line2": {"type_": "STRING"},
                            "line3": {"type_": "STRING"},
                            "line4": {"type_": "STRING"},
                            "line5": {"type_": "STRING"},
                            "line6": {"type_": "STRING"}
                        }
                    },
                    "order_lines": {
                        "type_": "ARRAY",
                        "items": {
                            "type_": "OBJECT",
                            "properties": {
                                "stockcode": {
                                    "type_": "STRING",
                                    "description": "The product's stock code. IMPORTANT: If the input text mentions a SKU code starting with 'EQL' (often used by Woolworths or Endeavour Group), you MUST return 'TSSU-ORA' as the stockcode. For other stock codes, return them as found."
                                },
                                "orderquantity": {
                                    "type_": "NUMBER",
                                    "description": "Quantity ordered (numeric value)."
                                }
                            },
                            "required": ["stockcode", "orderquantity"]
                        }
                    },
                    "X_SHIPVIA": {
                        "type_": "STRING",
                        "description": "The shipping method. Examples: 'DELTA', 'CAPITAL', 'BEST WAY', 'RING WHEN READY', 'DIRECT FREIGHT'.      "
                    }
                },
                "required": ["customer_details", "order_lines"]
            }
        }

        tools_config = [{"function_declarations": [tool_schema]}]
        prompt = f"""
You are an intelligent assistant working for Team Systems, specialized in extracting sales order information from raw text.
Your task is to identify key details and structure them by calling the 'extract_sales_order_data' tool.

                    "Rules":
                        1. Account name will never be Team Systems we are the supplier and any other name is the customer/account name.
                        1. If a field is missing or unclear, leave it as an empty string or default value (e.g., quantity = 0).
                        2. Ensure the SKU field has no whitespace.
                        3. If the dispatch method is "pickup" or empty, set it to "Ring when ready".
                        4. If a string like "POFREIGHT" or "Freight" is found in the document but does not have a SKU, set the dispatch method to "BEST WAY" and include the unit value unless pickup or empty.
                        5. Customers will use different SKU's codes e.g "FPS09K-PL3GST" is "MONSTAR3" in our system.
                        8. "Gateway Packaging Pty Ltd" disptach method is "CUSTOMERS CARRIER"
                        9. "Sitecraft Pty Ltd" is "EMAIL WHEN READY"
                        10. "RSEA Pty Ltd" = "DIRECT FREIGHT EXPRESS"
                        11. "Safety Xpress" = "DELTA"
                        12. "Items" containing "Freight" in the SKU require the SKU to be "CUSTOMER_FREIGHT" with the unit value added to the JSON object. e.g
                        "sku":  "FREIGHTINVI",
                                "quantity": 1,
                                "description": "Inventory Freight (VIC)",
                                "unit_value": 20.00
                        14. If the SKU does not match the usual format, provide a description in the description field.
                        15. "Endeavour Group Limited" will always be disptach method "CAPITAL"
                        16. Any SKU that mentions"EQLB8012" or similar via Woolworths or Endeavour group will be "TSSU-ORA" in our system.
                        17. Any account with "Brady" in the name will usually drop ship to the customer.
                        18. Brady is the account name/customer name, Ship to will be the drop ship address, PO number will be the order number.
                        19. Depending Brady's customer location, the dispatch method will be "BEST WAY" until we can learn patterns.
                        20. "Brady" will always have "Team Systems" SKU as "Your material number:"
                        21. "Brady" will always refer to the customer order number as "PO Number"
                        22. Based on the following warehouse locations, provide Delta for Metro and Direct Freight Express for Regional.
                        23. Delivery in Metro Australia = "DELTA"
                        24. Delivery in Regional Australia = "DIRECT FREIGHT EXPRESS"
                        25. Delivery small items in Metro Australia = "DIRECT FREIGHT EXPRESS"
                        26. Account preffered carrier is "CUSTOMERS CARRIER"
                        27. The account name will not always match the delivery address due to drop shipping.

Key extraction rules:
1. Customer Details:
   - For 'WOOLWORTHS LIMITED', debtor_id is 10981
   - For 'ENDEAVOUR GROUP', debtor_id is 21570
   - For 'RSEA', debtor_id is 6207
   - For 'BRADY', debtor_id is 5760
   - For 'GATEWAY', debtor_id 13924
   - For 'BRIERLEY', debtor_id 11139,
   - For 'REFLEX EQUIP', debtor_id 11197,
   - For 'SITECRAFT', debtor_id 1365,
   - For 'BLACKWOODS', debtor_id 5228
   - Extract the COMPLETE PO number as provided

2. Order Status: Default to 0=Not Processed, 3=Quotation


3. Product Lines:
   - For Woolworths/Endeavour group the stockcode will be "TSSU-ORA" this is a safety step
   - For BRADY orders: Look for "Your material number" field for stockcode
   - For RSEA orders: Look for "Supplier Item Code" field for stockcode
   - Extract quantity as numeric value

4. Shipping:
    - Common shipping options: "CAPITAL", "BEST WAY", "RING WHEN READY", "DIRECT FREIGHT", "DELTA", "CUSTOMERS CARRIER"


{context_text}

Content to process:
{content}

Please extract the sales order details using the tool:
"""

        try:
            response = self.model.generate_content(prompt, tools=tools_config)

            if response.candidates and response.candidates[0].content and response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call') and part.function_call.name == "extract_sales_order_data":
                        gemini_args = part.function_call.args
                        parsed_data = self._gemini_struct_to_dict(gemini_args)

                        logger.info("Successfully extracted order data with LLM")
                        logger.debug(f"Extracted data: {json.dumps(parsed_data, indent=2)}")

                        if isinstance(parsed_data, dict):
                            return parsed_data
                        else:
                            logger.warning("Extracted data is not a dictionary as expected")
                            return None

            logger.warning("LLM did not return expected tool call response")
            return None

        except Exception as e:
            logger.error(f"Error extracting order data with LLM: {e}")
            return None    

    def generate_myob_payload(self, extracted_order: ExtractedOrder) -> Optional[Dict[str, Any]]:
        """Generate MYOB-compatible payload from extracted order data."""
        
        # Define the correct EXO API schema
        myob_schema = {
            "type_": "OBJECT",
            "properties": {
                "debtorid": {
                    "type_": "INTEGER",
                    "description": "Customer debtor ID"
                },
                "customerordernumber": {
                    "type_": "STRING", 
                    "description": "Customer's purchase order number"
                },
                "status": {
                    "type_": "INTEGER",
                    "description": "Order status (0=Not Processed, 3=Quotation)"
                },
                "deliveryaddress": {
                    "type_": "OBJECT",
                    "properties": {
                        "line1": {"type_": "STRING"},
                        "line2": {"type_": "STRING"},
                        "line3": {"type_": "STRING"},
                        "line4": {"type_": "STRING"},
                        "line5": {"type_": "STRING"},
                        "line6": {"type_": "STRING"}
                    }
                },
                "lines": {
                    "type_": "ARRAY",
                    "items": {
                        "type_": "OBJECT",
                        "properties": {
                            "stockcode": {
                                "type_": "STRING",
                                "description": "Product stock code"
                            },
                            "orderquantity": {
                                "type_": "NUMBER",
                                "description": "Quantity to order"
                            }
                        },
                        "required": ["stockcode", "orderquantity"]
                    }
                },
                "extrafields": {
                    "type_": "ARRAY",
                    "description": "Array of extra fields for additional data like shipping method",
                    "items": {
                        "type_": "OBJECT",
                        "properties": {
                            "key": {
                                "type_": "STRING",
                                "description": "Field key (e.g., 'X_SHIPVIA')"
                            },
                            "value": {
                                "type_": "STRING",
                                "description": "Field value (e.g., shipping method)"
                            }
                        },
                        "required": ["key", "value"]
                    }
                }
            },
            "required": ["debtorid", "lines"]
        }

        tool_schema = {
            "name": "generate_myob_sales_order",
            "description": "Converts extracted order data into MYOB EXO API compatible format",
            "parameters": myob_schema
        }

        tools_config = [{"function_declarations": [tool_schema]}]
        
        # Convert extracted order to JSON for the prompt
        order_json = json.dumps(extracted_order.model_dump(), indent=2, default=str)
        
        prompt = f"""
Convert the following extracted order data into a MYOB EXO API compatible sales order payload.

IMPORTANT REQUIREMENTS:
1. Use EXACT field names: "debtorid", "customerordernumber", "status", "deliveryaddress", "lines", "extrafields"
2. For extrafields, use "key" and "value" format (NOT fieldname/fieldvalue)
3. Put X_SHIPVIA shipping method in extrafields at the TOP LEVEL (not in line items)
4. Only include the minimal required fields for order creation
5. Set status to 3 for quotations

EXAMPLE CORRECT ORDER:
{
  "debtorid": 6207,
  "customerordernumber": "1874786",
  "status": 3,
  "lines": [
    {
      "stockcode": "HTS500S",
      "orderquantity": 1.0
    },
    {
      "stockcode": "CUSTOMER_FREIGHT",
      "orderquantity": 1.0
    }
  ],
  "deliveryaddress": {
    "line1": "SPOTLIGHT JOONDALUP",
    "line2": "3/66 EDDYSTONE AVE",
    "line3": "JOONDALUP",
    "line4": "WA",
    "line5": "6027",
    "line6": null
  },
  "extrafields": [
    {
      "key": "X_SHIPVIA",
      "value": "DIRECT FREIGHT EXPRESS"
    }
  ]
}

EXAMPLE extrafields format:
"extrafields": [
    {{
        "key": "X_SHIPVIA",
        "value": "CAPITAL"
    }}
]

Extracted order data:
{order_json}

Please generate the MYOB payload using the tool:
"""

        try:
            response = self.model.generate_content(prompt, tools=tools_config)
            
            if not response or not response.candidates:
                logger.error("No response from LLM for MYOB payload generation")
                return None
            
            candidate = response.candidates[0]
            
            if not candidate.content or not candidate.content.parts:
                logger.error("No content in LLM response for MYOB payload")
                return None
            
            # Look for function call
            for part in candidate.content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    if part.function_call.name == "generate_myob_sales_order":
                        try:
                            gemini_args = part.function_call.args
                            myob_payload = self._gemini_struct_to_dict(gemini_args)
                            
                            # Validate the payload structure
                            if isinstance(myob_payload, dict):
                                self._validate_myob_payload(myob_payload)
                                
                                logger.info("Generated MYOB payload successfully")
                                logger.debug(f"MYOB payload: {json.dumps(myob_payload, indent=2)}")
                                return myob_payload
                            else:
                                logger.warning("MYOB payload is not a dictionary as expected")
                                return None
                            
                        except Exception as e:
                            logger.error(f"Error parsing MYOB payload function call: {e}")
                            continue
            
            logger.warning("LLM did not return expected MYOB payload function call")
            return None
            
        except Exception as e:
            logger.error(f"Error generating MYOB payload: {e}")
            return None

    def generate_myob_payload_direct(self, extracted_order: ExtractedOrder) -> Dict[str, Any]:
        """Generate MYOB payload directly from extracted order (no LLM) - more reliable."""
        
        # Build the basic payload
        payload = {
            "debtorid": extracted_order.customer_details.debtor_id,
            "status": extracted_order.order_status or 3,  # Default to quotation
            "defaultlocationid": 1,  # Add defaultlocationid underneath status
            "lines": [],
        }
        
        # Add customer order number if present
        if extracted_order.customer_details.customer_order_number:
            payload["customerordernumber"] = extracted_order.customer_details.customer_order_number
        
        # Add order lines
        for line in extracted_order.order_lines:
            payload["lines"].append({
                "stockcode": line.stockcode,
                "orderquantity": float(line.orderquantity)
            })
        
        # Add delivery address if present
        if extracted_order.delivery_address:
            addr = extracted_order.delivery_address
            payload["deliveryaddress"] = {
                "line1": addr.line1 or "",
                "line2": addr.line2 or "",
                "line3": addr.line3 or "",
                "line4": addr.line4 or "",
                "line5": addr.line5 or "",
                "line6": addr.line6 or ""
            }
        
        # Add extrafields for shipping method
        extrafields = []
        if extracted_order.X_SHIPVIA:
            extrafields.append({
                "key": "X_SHIPVIA",
                "value": extracted_order.X_SHIPVIA
            })
        
        if extrafields:
            payload["extrafields"] = extrafields
        
        logger.info("Generated minimal MYOB payload successfully")
        logger.info(f"Final minimal MYOB payload: {json.dumps(payload, indent=2)}")
        
        return payload

    def _validate_myob_payload(self, payload: Dict[str, Any]) -> None:
        """Validate MYOB payload structure."""
        required_fields = ["debtorid", "lines"]
        
        for field in required_fields:
            if field not in payload:
                raise ValueError(f"Missing required field: {field}")
        
        if not isinstance(payload["lines"], list) or len(payload["lines"]) == 0:
            raise ValueError("Lines must be a non-empty array")
        
        for i, line in enumerate(payload["lines"]):
            if "stockcode" not in line or "orderquantity" not in line:
                raise ValueError(f"Line {i+1} missing required fields: stockcode, orderquantity")
        
        # Validate extrafields format if present
        if "extrafields" in payload:
            if not isinstance(payload["extrafields"], list):
                raise ValueError("extrafields must be an array")
            
            for i, field in enumerate(payload["extrafields"]):
                if not isinstance(field, dict):
                    raise ValueError(f"extrafields[{i}] must be an object")
                if "key" not in field or "value" not in field:
                    raise ValueError(f"extrafields[{i}] must have 'key' and 'value' properties")
        
        logger.debug("MYOB payload validation passed")