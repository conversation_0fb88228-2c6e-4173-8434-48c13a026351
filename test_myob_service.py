"""
Test module for posting sales orders to MYOB using MyobService.
Generates unique customer order numbers to avoid duplication errors.
"""
import random
import string
import logging
from services.myob_service import MyobService

logging.basicConfig(level=logging.INFO)

def generate_unique_order_number(prefix="TEST"):
    # Generates a unique order number for testing
    return f"{prefix}{random.randint(100000, 999999)}"

def create_test_order_payload():
    return {
        "debtorid": 6207,  # Use a valid debtor ID from your MYOB system
        "customerordernumber": generate_unique_order_number(),
        "status": 3,
        "deliveryaddress": {
            "line1": "8 Curtis Road",
            "line2": "Tenancy 2",
            "line3": "Munno Para",
            "line4": "SA",
            "line5": "5115"
        },
        "lines": [
            {
                "stockcode": "TS1B",  # Use a valid stock code from your MYOB system
                "orderquantity": 1.0
            }
        ],
        "extrafields": [
            {
                "key": "X_SHIPVIA",
                "value": "BEST WAY"
            }
        ]
    }

def create_test_order_payload(debtorid, stockcode, address, shipvia):
    return {
        "debtorid": debtorid,
        "customerordernumber": generate_unique_order_number(f"T{debtorid}"),
        "status": 3,
        "deliveryaddress": address,
        "lines": [
            {
                "stockcode": stockcode,
                "orderquantity": 1.0
            }
        ],
        "extrafields": [
            {
                "key": "X_SHIPVIA",
                "value": shipvia
            }
        ]
    }

def main():
    service = MyobService()
    payload = create_test_order_payload()
    print("Posting test order to MYOB with customerordernumber:", payload["customerordernumber"])
    try:
        # Step 1: Validate
        validation_result = service.validate_order(payload)
        if not validation_result or 'order' not in validation_result:
            print("Validation failed. See logs for details.")
            return
        validated_payload = validation_result['order']
        # Step 2: Force status to 3 (Quotation)
        validated_payload['status'] = 3
        # Step 3: Create order
        result = service.create_order(validated_payload)
        if result:
            print("Order created successfully:", result)
        else:
            print("Order creation failed. See logs for details.")
    except Exception as e:
        print("Exception occurred while creating order:", e)

def post_multiple_test_orders():
    service = MyobService()
    # Test data for each debtor
    test_cases = [
        # WOOLWORTHS LIMITED
        (10981, "TSSU-ORA", {"line1": "1 Woolworths Way", "line2": "Bella Vista", "line3": "NSW", "line4": "2153", "line5": "AU"}, "CAPITAL"),
        # ENDEAVOUR GROUP
        (21570, "TSSU-ORA", {"line1": "1 Endeavour Lane", "line2": "Melbourne", "line3": "VIC", "line4": "3000", "line5": "AU"}, "CAPITAL"),
    ]
    for idx, (debtorid, stockcode, address, shipvia) in enumerate(test_cases, 1):
        payload = create_test_order_payload(debtorid, stockcode, address, shipvia)
        print(f"Posting test order {idx}/15 to MYOB with debtorid={debtorid}, customerordernumber={payload['customerordernumber']}")
        try:
            validation_result = service.validate_order(payload)
            if not validation_result or 'order' not in validation_result:
                print(f"Validation failed for order {idx}. See logs for details.")
                continue
            validated_payload = validation_result['order']
            validated_payload['status'] = 3
            result = service.create_order(validated_payload)
            if result:
                print(f"Order {idx} created successfully: {result.get('id', result)}")
            else:
                print(f"Order {idx} creation failed. See logs for details.")
        except Exception as e:
            print(f"Exception occurred while creating order {idx}: {e}")

if __name__ == "__main__":
    post_multiple_test_orders()
