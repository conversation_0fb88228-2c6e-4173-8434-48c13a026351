#!/usr/bin/env python3
"""
Test script to demonstrate email labeling functionality.
"""
import logging
from services.gmail_service import GmailService

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_email_labeling():
    """Test the email labeling functionality."""
    print("🔧 Testing Email Labeling Functionality")
    print("=" * 50)
    
    try:
        # Initialize Gmail service
        print("📧 Initializing Gmail service...")
        gmail = GmailService()
        
        if not gmail.service:
            print("❌ Gmail service failed to initialize")
            return
        
        print("✅ Gmail service initialized successfully")
        
        # Test 1: Create processing labels
        print("\n📋 Creating processing labels...")
        labels = gmail.get_or_create_processing_labels()
        
        if labels:
            print(f"✅ Processing labels created/verified:")
            for key, label_id in labels.items():
                print(f"   • {key}: {label_id}")
        else:
            print("❌ Failed to create processing labels")
            return
        
        # Test 2: Get a recent email to test labeling
        print("\n📬 Finding a recent email to test labeling...")
        
        # Get Brady label emails (should exist based on main_processor.py)
        label_id = gmail.get_label_id('Brady')
        if label_id:
            try:
                response = gmail.service.users().messages().list(
                    userId='me',
                    labelIds=[label_id],
                    maxResults=1
                ).execute()
                
                messages = response.get('messages', [])
                if messages:
                    test_email_id = messages[0]['id']
                    print(f"✅ Found test email: {test_email_id}")
                    
                    # Get email details
                    msg = gmail.service.users().messages().get(
                        userId='me', 
                        id=test_email_id, 
                        format='metadata',
                        metadataHeaders=['Subject']
                    ).execute()
                    
                    headers = msg.get('payload', {}).get('headers', [])
                    subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
                    print(f"   Subject: {subject}")
                    
                    # Test 3: Check current processing status
                    print(f"\n🔍 Checking current processing status...")
                    current_status = gmail.has_processing_label(test_email_id)
                    if current_status:
                        print(f"   Current status: {current_status}")
                    else:
                        print("   No processing label found")
                    
                    # Test 4: Add a test label
                    print(f"\n🏷️  Adding 'Processed' label (green)...")
                    success = gmail.mark_email_processed(test_email_id)
                    if success:
                        print("✅ Label added successfully")
                        
                        # Verify the label was added
                        print("🔍 Verifying label was added...")
                        new_status = gmail.has_processing_label(test_email_id)
                        if new_status == 'Processed':
                            print("✅ Label verification successful")
                        else:
                            print(f"⚠️  Unexpected status: {new_status}")
                    else:
                        print("❌ Failed to add label")
                    
                    # Test 5: Test other label types
                    print(f"\n🏷️  Testing traffic light labels...")
                    
                    # Test failed label (red)
                    print("   Adding 'Failed' label (red)...")
                    gmail.mark_email_failed(test_email_id)
                    
                    # Test review label (amber)
                    print("   Adding 'Review' label (amber)...")
                    gmail.mark_email_review(test_email_id)
                    
                    # Check final status
                    final_status = gmail.has_processing_label(test_email_id)
                    print(f"   Final status: {final_status}")
                    
                else:
                    print("❌ No emails found in Brady label")
            except Exception as e:
                print(f"❌ Error testing with Brady emails: {e}")
        else:
            print("❌ Brady label not found")
        
        print(f"\n✅ Email labeling test completed!")
        print("\n📋 Summary:")
        print("   • Processing labels created successfully")
        print("   • Label adding/checking functionality works")
        print("   • You can now check your Gmail to see the new labels")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Email labeling test error: {e}")

if __name__ == "__main__":
    test_email_labeling()