"""
Enhanced Universal Email Agent - Monitors expanded label categories and extracts orders from all purchase orders.
"""
import logging
import os
import json
import yaml
import re
import binascii
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Set, Any
from dataclasses import dataclass
from pathlib import Path


# Add project root to Python path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.gmail_service import GmailService
from services.llm_service import LLMService
from utils.pdf_extractor import extract_text_from_pdf
from utils.models import EmailData, ExtractedOrder, ProcessedOrder
from utils.config import config

logger = logging.getLogger(__name__)

@dataclass
class ProcessingRule:
    """Configuration for email processing rules."""
    name: str
    label_patterns: List[str]
    include_patterns: List[str]
    exclude_patterns: List[str]
    priority: int
    enabled: bool

@dataclass
class UniversalProcessingConfig:
    """Configuration for the enhanced universal agent."""
    monitor_all_labels: bool = True
    custom_label_patterns: Optional[List[str]] = None
    excluded_labels: Optional[List[str]] = None
    include_attachments: Optional[List[str]] = None
    exclude_subjects: Optional[List[str]] = None
    time_window_hours: int = 168  # 7 days default
    max_results_per_label: int = 50
    enable_smart_filtering: bool = True
    auto_categorization: bool = True

class EnhancedUniversalAgent:
    """Enhanced agent that monitors expanded label categories and extracts orders from all purchase orders."""
    
    def __init__(self, config_file: str = "universal_agent_config.yaml"):
        self.gmail_service = GmailService()
        self.llm_service = LLMService()
        self.config_file = config_file
        self.config = self._load_config()
        self.processing_rules = self._load_processing_rules()
        self._ensure_directories()
        logger.info("Enhanced Universal Agent initialized")
    
    def _load_config(self) -> UniversalProcessingConfig:
        """Load configuration from YAML file."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = yaml.safe_load(f)
                    return UniversalProcessingConfig(**config_data)
            except Exception as e:
                logger.warning(f"Error loading config file: {e}, using defaults")
        
        # Create default config file
        default_config = UniversalProcessingConfig(
            custom_label_patterns=["*Purchase*", "*Order*", "*Invoice*", "*PO*"],
            excluded_labels=["Spam", "Trash", "Drafts"],
            include_attachments=[".pdf", ".xlsx", ".docx", ".png", ".jpg"],
            exclude_subjects=["Out of Office", "Vacation", "Auto-reply"]
        )
        self._save_config(default_config)
        return default_config
    
    def _save_config(self, config: UniversalProcessingConfig):
        """Save configuration to YAML file."""
        try:
            with open(self.config_file, 'w') as f:
                yaml.dump(config.__dict__, f, default_flow_style=False)
            logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def _load_processing_rules(self) -> List[ProcessingRule]:
        """Load processing rules from configuration."""
        rules_file = "processing_rules.yaml"
        if os.path.exists(rules_file):
            try:
                with open(rules_file, 'r') as f:
                    rules_data = yaml.safe_load(f)
                    return [ProcessingRule(**rule) for rule in rules_data.get('rules', [])]
            except Exception as e:
                logger.warning(f"Error loading processing rules: {e}")
        
        # Create default rules
        default_rules = [
            ProcessingRule(
                name="Purchase Orders",
                label_patterns=["*Purchase*", "*Order*", "*PO*"],
                include_patterns=["purchase order", "PO#", "order #"],
                exclude_patterns=["packing slip", "receipt", "confirmation"],
                priority=1,
                enabled=True
            ),
            ProcessingRule(
                name="Invoices",
                label_patterns=["*Invoice*", "*Bill*"],
                include_patterns=["invoice", "bill", "payment"],
                exclude_patterns=["quote", "estimate"],
                priority=2,
                enabled=True
            ),
            ProcessingRule(
                name="Packing Slips",
                label_patterns=["*Packing*", "*Ship*"],
                include_patterns=["packing slip", "shipping", "dispatch"],
                exclude_patterns=["quote"],
                priority=3,
                enabled=True
            )
        ]
        
        self._save_processing_rules(default_rules)
        return default_rules
    
    def _save_processing_rules(self, rules: List[ProcessingRule]):
        """Save processing rules to YAML file."""
        try:
            rules_data = {'rules': [rule.__dict__ for rule in rules]}
            with open("processing_rules.yaml", 'w') as f:
                yaml.dump(rules_data, f, default_flow_style=False)
            logger.info("Processing rules saved")
        except Exception as e:
            logger.error(f"Error saving processing rules: {e}")
    
    def _ensure_directories(self):
        """Ensure output directories exist."""
        directories = ["markdown", "myob", "logs", "config"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def discover_labels(self) -> List[str]:
        """Discover all available Gmail labels."""
        try:
            if not self.gmail_service.service:
                logger.error("Gmail service not initialized")
                return []
            
            results = self.gmail_service.service.users().labels().list(userId='me').execute()
            labels = results.get('labels', [])
            
            all_labels = [label['name'] for label in labels]
            logger.info(f"Discovered {len(all_labels)} total labels")
            
            # Filter based on configuration
            if self.config.monitor_all_labels:
                # Include all except excluded
                excluded_labels = self.config.excluded_labels or []
                excluded = set(excluded_labels)
                filtered_labels = [label for label in all_labels if label not in excluded]
            else:
                # Use custom patterns
                filtered_labels = self._filter_labels_by_patterns(all_labels)
            
            logger.info(f"Filtered to {len(filtered_labels)} labels for monitoring")
            return filtered_labels
            
        except Exception as e:
            logger.error(f"Error discovering labels: {e}")
            return []
    
    def _filter_labels_by_patterns(self, labels: List[str]) -> List[str]:
        """Filter labels based on configured patterns."""
        if not self.config.custom_label_patterns:
            return labels
        
        filtered = []
        for label in labels:
            for pattern in self.config.custom_label_patterns:
                if self._match_pattern(label, pattern):
                    filtered.append(label)
                    break
        
        return filtered
    
    def _match_pattern(self, text: str, pattern: str) -> bool:
        """Check if text matches pattern (supports wildcards)."""
        # Convert wildcard pattern to regex
        regex_pattern = pattern.replace('*', '.*').replace('?', '.')
        return bool(re.search(regex_pattern, text, re.IGNORECASE))
    
    def categorize_email(self, email: EmailData) -> Dict[str, Any]:
        """Use AI to categorize email and determine processing approach."""
        try:
            categorization_prompt = f"""
Analyze this email and categorize it for business processing:

Subject: {email.subject}
Sender: {email.sender}
Body: {email.body[:1000]}...
Attachments: {[att['filename'] for att in email.attachments]}

Provide a JSON response with:
1. category: (purchase_order, invoice, packing_slip, quote, general_business, spam, other)
2. priority: (1-5, where 1 is highest priority)
3. confidence: (0.0-1.0)
4. contains_order_data: (true/false)
5. processing_recommendation: (process_immediately, review_required, skip, flag_for_manual)
6. key_identifiers: [list of important numbers/codes found]
7. supplier_info: extracted supplier/vendor information
"""
            
            response = self.llm_service.model.generate_content(categorization_prompt)
            response_text = response.text if hasattr(response, 'text') else str(response)
            if not response_text.strip():
                logger.error(f"Empty response from LLM for email {email.id}")
                raise ValueError("Empty response from LLM")
            
            # --- FIX: Clean the LLM response before parsing ---
            cleaned_json_str = self._extract_json_from_string(response_text)
            if not cleaned_json_str:
                logger.error(f"Could not extract valid JSON from LLM response for email {email.id}. Original text: {response_text}")
                raise ValueError("No valid JSON found in LLM response")
            
            try:
                categorization = json.loads(cleaned_json_str)
            except json.JSONDecodeError as json_err:
                logger.error(f"Invalid JSON from LLM for email {email.id} even after cleaning. Original: {response_text}, Cleaned: {cleaned_json_str}")
                raise json_err

            logger.info(f"Email categorized as {categorization.get('category')} with priority {categorization.get('priority')}")
            return categorization
            
        except Exception as e:
            logger.error(f"Error categorizing email {email.id}: {e}")
            return {
                'category': 'other',
                'priority': 3,
                'confidence': 0.0,
                'contains_order_data': False,
                'processing_recommendation': 'review_required'
            }
    
    def smart_content_filter(self, email: EmailData) -> bool:
        """Intelligent filtering to determine if email should be processed."""
        # Check exclude patterns
        if self.config.exclude_subjects:
            for pattern in self.config.exclude_subjects:
                if self._match_pattern(email.subject, pattern):
                    logger.info(f"Email excluded by subject pattern: {pattern}")
                    return False
        
        # If smart filtering is enabled, use AI categorization
        if self.config.enable_smart_filtering:
            categorization = self.categorize_email(email)
            
            # Skip if confidence is too low or doesn't contain order data
            if categorization.get('confidence', 0) < 0.3:
                logger.info(f"Email skipped due to low confidence: {categorization.get('confidence')}")
                return False
            
            # Skip certain categories
            skip_categories = ['spam', 'general_business']
            if categorization.get('category') in skip_categories:
                logger.info(f"Email skipped due to category: {categorization.get('category')}")
                return False
        
        return True
    
    def enhanced_attachment_processing(self, email: EmailData) -> str:
        """Enhanced processing for multiple attachment types."""
        full_content = email.body
        attachment_content = ""
        
        for attachment in email.attachments:
            filename = attachment['filename'].lower()
            
            try:
                if filename.endswith('.pdf'):
                    pdf_text = extract_text_from_pdf(attachment['data'])
                    if pdf_text:
                        attachment_content += f"\n--- PDF: {attachment['filename']} ---\n{pdf_text}\n"
                
                elif filename.endswith(('.xlsx', '.xls')):
                    # Excel processing (placeholder - would need openpyxl)
                    attachment_content += f"\n--- Excel: {attachment['filename']} ---\n[Excel content would be processed here]\n"
                
                elif filename.endswith('.docx'):
                    # Word document processing (placeholder - would need python-docx)
                    attachment_content += f"\n--- Word: {attachment['filename']} ---\n[Word content would be processed here]\n"
                
                elif filename.endswith(('.png', '.jpg', '.jpeg')):
                    # OCR processing (placeholder - would need OCR library)
                    attachment_content += f"\n--- Image: {attachment['filename']} ---\n[OCR content would be processed here]\n"
                
            except Exception as e:
                logger.error(f"Error processing attachment {filename}: {e}")
                attachment_content += f"\n--- Error processing {attachment['filename']}: {e} ---\n"
        
        full_content += attachment_content
        return full_content
    
    def universal_order_extraction(self, email: EmailData) -> Optional[ExtractedOrder]:
        """Universal order extraction that works with any supplier format."""
        try:
            # Get enhanced content including all attachments
            full_content = self.enhanced_attachment_processing(email)
            
            # Use AI categorization to inform extraction
            categorization = self.categorize_email(email)
            
            # Enhanced extraction prompt with categorization context
            extraction_prompt = f"""
Extract order data from this email regardless of supplier format:

Email Category: {categorization.get('category', 'unknown')}
Key Identifiers: {categorization.get('key_identifiers', [])}
Supplier Info: {categorization.get('supplier_info', 'unknown')}

Content: {full_content}

Extract and return JSON with ExtractedOrder format, being flexible with:
- Customer/supplier identification (use any available ID numbers)
- Order numbers and references
- Line items with any available product codes/descriptions
- Quantities and pricing where available
- Delivery addresses and shipping info

Be adaptive - if standard fields don't apply, map the closest equivalent.
"""
            
            response = self.llm_service.model.generate_content(extraction_prompt)
            response_text = response.text if hasattr(response, 'text') else str(response)
            if not response_text.strip():
                logger.error(f"Empty extraction response from LLM for email {email.id}")
                raise ValueError("Empty extraction response from LLM")
            
            # --- FIX: Clean the LLM response before parsing ---
            cleaned_json_str = self._extract_json_from_string(response_text)
            if not cleaned_json_str:
                logger.error(f"Could not extract valid JSON from LLM extraction response for email {email.id}. Original text: {response_text}")
                raise ValueError("No valid JSON found in LLM extraction response")

            try:
                extracted_data = json.loads(cleaned_json_str)
            except json.JSONDecodeError as json_err:
                logger.error(f"Invalid extraction JSON from LLM for email {email.id} even after cleaning. Original: {response_text}, Cleaned: {cleaned_json_str}")
                raise json_err

            # Validate and create ExtractedOrder
            return ExtractedOrder(**extracted_data)
            
        except Exception as e:
            logger.error(f"Universal order extraction failed for {email.id}: {e}")
            return None
    
    def process_emails_from_all_labels(self) -> List[ProcessedOrder]:
        """Process emails from all discovered labels."""
        logger.info("Starting universal email processing")
        
        # Discover available labels
        labels_to_process = ['INBOX']
        
        if not labels_to_process:
            logger.warning("No labels found for processing")
            return []
        
        all_emails = []
        
        # Process each label
        for label_name in labels_to_process:
            try:
                label_emails = self._fetch_emails_from_label(label_name)
                all_emails.extend(label_emails)
                logger.info(f"Found {len(label_emails)} emails in label '{label_name}'")
            except Exception as e:
                logger.error(f"Error processing label {label_name}: {e}")
        
        logger.info(f"Total emails found: {len(all_emails)}")
        
        # Filter emails using smart filtering
        filtered_emails = []
        for email in all_emails:
            if self.smart_content_filter(email):
                filtered_emails.append(email)
        
        logger.info(f"Emails after filtering: {len(filtered_emails)}")
        
        # Process filtered emails
        processed_orders = []
        for email in filtered_emails:
            try:
                processed_order = self._process_single_email_universal(email)
                if processed_order:
                    processed_orders.append(processed_order)
            except Exception as e:
                logger.error(f"Error processing email {email.id}: {e}")
                # Mark as failed but continue
                try:
                    self.gmail_service.mark_email_failed(email.id)
                except:
                    pass
        
        logger.info(f"Successfully processed {len(processed_orders)} orders")
        return processed_orders
    
    def _fetch_emails_from_label(self, label_name: str) -> List[EmailData]:
        """Fetch emails from a specific label."""
        try:
            label_id = self.gmail_service.get_label_id(label_name)
            if not label_id:
                return []
            
            # Build query
            query_parts = []
            
            # Time window
            if self.config.time_window_hours:
                after_date = (datetime.now() - timedelta(hours=self.config.time_window_hours)).strftime('%Y/%m/%d')
                query_parts.append(f"after:{after_date}")
            
            # Attachments filter
            if self.config.include_attachments:
                query_parts.append("has:attachment")
            
            query = " ".join(query_parts) if query_parts else ""
            
            if not self.gmail_service.service:
                logger.error("Gmail service not available")
                return []
            
            response = self.gmail_service.service.users().messages().list(
                userId='me',
                labelIds=[label_id],
                maxResults=self.config.max_results_per_label,
                q=query
            ).execute()
            
            messages = response.get('messages', [])
            emails = []
            
            for msg_ref in messages:
                email_data = self.gmail_service._get_email_details(msg_ref['id'], label_name)
                if email_data:
                    emails.append(email_data)
            
            return emails
            
        except Exception as e:
            logger.error(f"Error fetching emails from label {label_name}: {e}")
            return []
    
    def _process_single_email_universal(self, email: EmailData) -> Optional[ProcessedOrder]:
        """Process a single email with universal extraction."""
        logger.info(f"Processing email: {email.subject}")
        
        try:
            # Extract order data using universal method
            extracted_order = self.universal_order_extraction(email)
            if not extracted_order:
                logger.warning(f"No order data extracted from {email.id}")
                self.gmail_service.mark_email_review(email.id)
                return None
            
            # Generate markdown summary
            markdown_summary = self.llm_service.generate_markdown_summary(email, "")
            
            # Generate MYOB payload
            myob_payload = self.llm_service.generate_myob_payload_direct(extracted_order)
            
            # Save files
            markdown_filepath = self._save_markdown_file(email.subject, markdown_summary)
            myob_filepath = self._save_myob_file(email.subject, myob_payload)
            
            # Mark as processed
            self.gmail_service.mark_email_processed(email.id)
            
            return ProcessedOrder(
                email_id=email.id,
                email_subject=email.subject,
                extracted_data=extracted_order,
                markdown_summary=markdown_summary,
                myob_payload=myob_payload,
                markdown_filepath=markdown_filepath,
                myob_filepath=myob_filepath
            )
            
        except Exception as e:
            logger.error(f"Error processing email {email.id}: {e}")
            self.gmail_service.mark_email_failed(email.id)
            return None
    
    def _save_markdown_file(self, email_subject: str, content: str) -> str:
        """Save markdown file with sanitized filename."""
        timestamp = datetime.now().strftime("%d-%m_%H%M")
        sanitized_subject = self._sanitize_filename(email_subject)
        filename = f"{timestamp}_{sanitized_subject}.md"
        filepath = os.path.join("markdown", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return filepath
        except Exception as e:
            logger.error(f"Error saving markdown file: {e}")
            return ""
    
    def _save_myob_file(self, email_subject: str, payload: dict) -> str:
        """Save MYOB JSON file with sanitized filename."""
        timestamp = datetime.now().strftime("%d-%m_%H%M")
        sanitized_subject = self._sanitize_filename(email_subject)
        filename = f"{timestamp}_{sanitized_subject}.json"
        filepath = os.path.join("myob", filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(payload, f, indent=2)
            return filepath
        except Exception as e:
            logger.error(f"Error saving MYOB file: {e}")
            return ""
    
    def _sanitize_filename(self, text: str) -> str:
        """Sanitize filename by removing invalid characters."""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            text = text.replace(char, '_')
        return text.strip()[:50]

    def _extract_json_from_string(self, text: str) -> Optional[str]:
        """
        Finds and extracts a JSON object from a string that might be wrapped in markdown.
        Returns the clean JSON string or None if it can't be found.
        """
        if not isinstance(text, str):
            return None

        try:
            # Common markdown format ```json ... ```
            match = re.search(r"```(json)?\s*(\{.*?\})\s*```", text, re.DOTALL)
            if match:
                return match.group(2)

            # Fallback for finding the first '{' and last '}'
            start_index = text.find('{')
            end_index = text.rfind('}')
            
            if start_index != -1 and end_index != -1 and end_index > start_index:
                return text[start_index : end_index + 1]
        
        except Exception as e:
            logger.error(f"Error while extracting JSON from text: {e}")
        
        return None # Return None if no valid JSON object is found
    
    def run_universal_processing(self):
        """Run the universal processing workflow."""
        logger.info("Starting Enhanced Universal Agent")
        try:
            processed_orders = self.process_emails_from_all_labels()
            
            if processed_orders:
                self._display_processing_results(processed_orders)
            else:
                print("No orders were processed in this run.")
            
            return processed_orders
            
        except Exception as e:
            logger.error(f"Universal processing failed: {e}")
            print(f"Processing failed: {e}")
            return []
    
    def _display_processing_results(self, processed_orders: List[ProcessedOrder]):
        """Display processing results."""
        print("\n" + "="*80)
        print("🚀 ENHANCED UNIVERSAL EMAIL PROCESSING COMPLETED")
        print("="*80)
        
        for i, order in enumerate(processed_orders, 1):
            print(f"\n--- ORDER {i} of {len(processed_orders)} ---")
            print(f"📧 Email: {order.email_subject}")
            print(f"📁 Files: {order.markdown_filepath}, {order.myob_filepath}")
            
            # Display extracted data summary
            if order.extracted_data:
                customer = order.extracted_data.customer_details
                print(f"👤 Customer: {customer.debtor_id}")
                print(f"🏷️  Order: {customer.customer_order_number or 'N/A'}")
                print(f"📦 Lines: {len(order.extracted_data.order_lines)}")
        
        print(f"\n✅ Total processed: {len(processed_orders)} orders")
        print("="*80)

def main():
    """Main function for enhanced universal agent."""
    agent = EnhancedUniversalAgent()
    return agent.run_universal_processing()

if __name__ == "__main__":
    main()