#!/usr/bin/env python3
"""
Comprehensive Email Processing System - Orchestrates both enhanced universal agent and continuous polling agent.
"""
import logging
import os
import json
import time
import threading
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
import sqlite3


# Add project root to Python path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.enhanced_universal_agent import EnhancedUniversalAgent
from agents.continuous_polling_agent import ContinuousPollingAgent
from orchestrators.email_dashboard import EmailDashboard
from services.gmail_service import GmailService
from utils.config import config

logger = logging.getLogger(__name__)

@dataclass
class SystemConfig:
    """Configuration for the comprehensive email system."""
    enable_universal_agent: bool = True
    enable_continuous_polling: bool = True
    enable_dashboard: bool = True
    dashboard_port: int = 5000
    universal_agent_schedule: str = "hourly"  # hourly, daily, manual
    system_health_check_interval: int = 300  # 5 minutes
    backup_interval_hours: int = 24
    log_level: str = "INFO"

class ComprehensiveEmailSystem:
    """Main orchestrator for the comprehensive email processing system."""
    
    def __init__(self, config_file: str = "system_config.json"):
        self.config = self._load_config(config_file)
        self.gmail_service = GmailService()
        
        # Initialize agents
        self.universal_agent = None
        self.polling_agent = None
        self.dashboard = None
        
        # System state
        self.running = False
        self.system_start_time = None
        self.last_health_check = None
        
        # Threading
        self.threads: List[threading.Thread] = []
        
        # Database for system-wide tracking
        self.system_db_path = "comprehensive_system.db"
        self._init_system_database()
        
        logger.info("Comprehensive Email System initialized")
    
    def _load_config(self, config_file: str) -> SystemConfig:
        """Load system configuration."""
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                    return SystemConfig(**config_data)
            except Exception as e:
                logger.warning(f"Error loading config: {e}, using defaults")
        
        # Create default config
        default_config = SystemConfig()
        self._save_config(config_file, default_config)
        return default_config
    
    def _save_config(self, config_file: str, config: SystemConfig):
        """Save system configuration."""
        try:
            with open(config_file, 'w') as f:
                json.dump(config.__dict__, f, indent=2)
            logger.info(f"System configuration saved to {config_file}")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def _init_system_database(self):
        """Initialize system-wide database."""
        try:
            conn = sqlite3.connect(self.system_db_path)
            cursor = conn.cursor()
            
            # System health table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_health (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    component TEXT NOT NULL,
                    status TEXT NOT NULL,
                    metrics TEXT,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # System events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    component TEXT,
                    description TEXT,
                    severity TEXT DEFAULT 'INFO',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    emails_processed_per_hour INTEGER DEFAULT 0,
                    average_processing_time REAL DEFAULT 0,
                    queue_size INTEGER DEFAULT 0,
                    error_rate REAL DEFAULT 0,
                    system_cpu_usage REAL DEFAULT 0,
                    system_memory_usage REAL DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("System database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing system database: {e}")
    
    def _log_system_event(self, event_type: str, component: str, description: str, severity: str = "INFO"):
        """Log system-wide events."""
        try:
            conn = sqlite3.connect(self.system_db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO system_events (timestamp, event_type, component, description, severity)
                VALUES (?, ?, ?, ?, ?)
            ''', (datetime.now().isoformat(), event_type, component, description, severity))
            conn.commit()
            conn.close()
            logger.info(f"System event logged: {event_type} - {description}")
        except Exception as e:
            logger.error(f"Error logging system event: {e}")
    
    def initialize_components(self):
        """Initialize all system components."""
        try:
            # Initialize Universal Agent
            if self.config.enable_universal_agent:
                logger.info("Initializing Enhanced Universal Agent...")
                self.universal_agent = EnhancedUniversalAgent()
                self._log_system_event("COMPONENT_INIT", "UniversalAgent", "Enhanced Universal Agent initialized")
            
            # Initialize Continuous Polling Agent
            if self.config.enable_continuous_polling:
                logger.info("Initializing Continuous Polling Agent...")
                self.polling_agent = ContinuousPollingAgent()
                self._log_system_event("COMPONENT_INIT", "PollingAgent", "Continuous Polling Agent initialized")
            
            # Initialize Dashboard
            if self.config.enable_dashboard:
                logger.info("Initializing Email Dashboard...")
                self.dashboard = EmailDashboard()
                self._log_system_event("COMPONENT_INIT", "Dashboard", "Email Dashboard initialized")
            
            logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            self._log_system_event("COMPONENT_INIT", "System", f"Component initialization failed: {e}", "ERROR")
            return False
    
    def start_universal_agent_scheduler(self):
        """Start the universal agent on a schedule."""
        def scheduler_worker():
            while self.running:
                try:
                    if self.config.universal_agent_schedule == "hourly":
                        interval = 3600  # 1 hour
                    elif self.config.universal_agent_schedule == "daily":
                        interval = 86400  # 24 hours
                    else:
                        # Manual mode - don't run automatically
                        time.sleep(60)
                        continue
                    
                    logger.info("Running scheduled universal agent processing...")
                    self._log_system_event("SCHEDULED_RUN", "UniversalAgent", "Starting scheduled processing")
                    
                    # Run universal agent if initialized
                    if self.universal_agent is not None:
                        processed_orders = self.universal_agent.run_universal_processing()
                        self._log_system_event(
                            "SCHEDULED_COMPLETE", 
                            "UniversalAgent", 
                            f"Scheduled processing completed: {len(processed_orders)} orders processed"
                        )
                    else:
                        logger.warning("Universal agent is not initialized. Skipping scheduled processing.")
                        self._log_system_event(
                            "SCHEDULED_SKIPPED",
                            "UniversalAgent",
                            "Scheduled processing skipped: Universal agent not initialized",
                            "WARNING"
                        )
                    
                    # Wait for next scheduled run
                    time.sleep(interval)
                    
                except Exception as e:
                    logger.error(f"Error in universal agent scheduler: {e}")
                    self._log_system_event("SCHEDULED_ERROR", "UniversalAgent", f"Scheduler error: {e}", "ERROR")
                    time.sleep(300)  # Wait 5 minutes before retry
        
        if self.universal_agent and self.config.universal_agent_schedule != "manual":
            scheduler_thread = threading.Thread(
                target=scheduler_worker,
                name="UniversalAgentScheduler",
                daemon=True
            )
            scheduler_thread.start()
            self.threads.append(scheduler_thread)
            logger.info(f"Universal agent scheduler started ({self.config.universal_agent_schedule})")
    
    def start_dashboard_server(self):
        """Start the dashboard server."""
        def dashboard_worker():
            try:
                logger.info(f"Starting dashboard server on port {self.config.dashboard_port}")
                if self.dashboard is not None:
                    self.dashboard.run(
                        host='0.0.0.0',
                        port=self.config.dashboard_port,
                        debug=False
                    )
                else:
                    logger.error("Dashboard instance is None. Cannot start dashboard server.")
                    self._log_system_event("DASHBOARD_ERROR", "Dashboard", "Dashboard instance is None. Cannot start server.", "ERROR")
            except Exception as e:
                logger.error(f"Dashboard server error: {e}")
                self._log_system_event("DASHBOARD_ERROR", "Dashboard", f"Server error: {e}", "ERROR")
        
        if self.dashboard:
            dashboard_thread = threading.Thread(
                target=dashboard_worker,
                name="DashboardServer",
                daemon=True
            )
            dashboard_thread.start()
            self.threads.append(dashboard_thread)
            logger.info("Dashboard server thread started")
    
    def start_health_monitor(self):
        """Start system health monitoring."""
        def health_monitor_worker():
            while self.running:
                try:
                    self.perform_health_check()
                    time.sleep(self.config.system_health_check_interval)
                except Exception as e:
                    logger.error(f"Health monitor error: {e}")
                    time.sleep(60)
        
        health_thread = threading.Thread(
            target=health_monitor_worker,
            name="HealthMonitor",
            daemon=True
        )
        health_thread.start()
        self.threads.append(health_thread)
        logger.info("Health monitor started")
    
    def perform_health_check(self):
        """Perform comprehensive system health check."""
        try:
            health_data = {
                'timestamp': datetime.now().isoformat(),
                'components': {}
            }
            
            # Check Gmail service
            try:
                if self.gmail_service.service:
                    health_data['components']['gmail'] = {'status': 'healthy', 'service_available': True}
                else:
                    health_data['components']['gmail'] = {'status': 'degraded', 'service_available': False}
            except Exception as e:
                health_data['components']['gmail'] = {'status': 'unhealthy', 'error': str(e)}
            
            # Check Universal Agent
            if self.universal_agent:
                try:
                    # Test if agent can discover labels
                    labels = self.universal_agent.discover_labels()
                    health_data['components']['universal_agent'] = {
                        'status': 'healthy',
                        'labels_discovered': len(labels)
                    }
                except Exception as e:
                    health_data['components']['universal_agent'] = {'status': 'unhealthy', 'error': str(e)}
            
            # Check Polling Agent
            if self.polling_agent:
                try:
                    stats = self.polling_agent.get_processing_statistics()
                    health_data['components']['polling_agent'] = {
                        'status': 'healthy' if self.polling_agent.running else 'stopped',
                        'queue_size': stats.get('current_queue_size', 0),
                        'processed_24h': stats.get('total_processed_24h', 0)
                    }
                except Exception as e:
                    health_data['components']['polling_agent'] = {'status': 'unhealthy', 'error': str(e)}
            
            # Check Dashboard
            if self.dashboard:
                try:
                    # Dashboard is healthy if the thread is alive
                    dashboard_thread = next((t for t in self.threads if t.name == "DashboardServer"), None)
                    health_data['components']['dashboard'] = {
                        'status': 'healthy' if dashboard_thread and dashboard_thread.is_alive() else 'stopped',
                        'port': self.config.dashboard_port
                    }
                except Exception as e:
                    health_data['components']['dashboard'] = {'status': 'unhealthy', 'error': str(e)}
            
            # Log health status
            self._log_health_status(health_data)
            self.last_health_check = datetime.now()
            
            logger.debug("Health check completed successfully")
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
    
    def _log_health_status(self, health_data: Dict[str, Any]):
        """Log health status to database."""
        try:
            conn = sqlite3.connect(self.system_db_path)
            cursor = conn.cursor()
            
            for component, status in health_data['components'].items():
                cursor.execute('''
                    INSERT INTO system_health (timestamp, component, status, metrics)
                    VALUES (?, ?, ?, ?)
                ''', (
                    health_data['timestamp'],
                    component,
                    status['status'],
                    json.dumps(status)
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging health status: {e}")
    
    def start_backup_scheduler(self):
        """Start automatic backup scheduler."""
        def backup_worker():
            while self.running:
                try:
                    self.create_system_backup()
                    time.sleep(self.config.backup_interval_hours * 3600)
                except Exception as e:
                    logger.error(f"Backup scheduler error: {e}")
                    time.sleep(3600)  # Wait 1 hour before retry
        
        backup_thread = threading.Thread(
            target=backup_worker,
            name="BackupScheduler",
            daemon=True
        )
        backup_thread.start()
        self.threads.append(backup_thread)
        logger.info("Backup scheduler started")
    
    def create_system_backup(self):
        """Create backup of system databases and configurations."""
        try:
            backup_dir = f"backups/{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(backup_dir, exist_ok=True)
            
            # Backup databases
            import shutil
            
            databases = [
                self.system_db_path,
                "continuous_polling.db",
                "email_dashboard.db"
            ]
            
            for db_file in databases:
                if os.path.exists(db_file):
                    shutil.copy2(db_file, backup_dir)
            
            # Backup configurations
            config_files = [
                "system_config.json",
                "polling_config.json",
                "universal_agent_config.yaml",
                "processing_rules.yaml"
            ]
            
            for config_file in config_files:
                if os.path.exists(config_file):
                    shutil.copy2(config_file, backup_dir)
            
            logger.info(f"System backup created: {backup_dir}")
            self._log_system_event("BACKUP_CREATED", "System", f"Backup created at {backup_dir}")
            
        except Exception as e:
            logger.error(f"Error creating system backup: {e}")
            self._log_system_event("BACKUP_ERROR", "System", f"Backup failed: {e}", "ERROR")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        try:
            status = {
                'system': {
                    'running': self.running,
                    'start_time': self.system_start_time.isoformat() if self.system_start_time else None,
                    'uptime_seconds': (datetime.now() - self.system_start_time).total_seconds() if self.system_start_time else 0,
                    'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None
                },
                'components': {
                    'universal_agent': {
                        'enabled': self.config.enable_universal_agent,
                        'available': self.universal_agent is not None,
                        'schedule': self.config.universal_agent_schedule
                    },
                    'polling_agent': {
                        'enabled': self.config.enable_continuous_polling,
                        'available': self.polling_agent is not None,
                        'running': self.polling_agent.running if self.polling_agent else False
                    },
                    'dashboard': {
                        'enabled': self.config.enable_dashboard,
                        'available': self.dashboard is not None,
                        'port': self.config.dashboard_port
                    }
                },
                'statistics': {}
            }
            
            # Add polling agent statistics
            if self.polling_agent:
                status['statistics']['polling'] = self.polling_agent.get_processing_statistics()
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    def run_manual_universal_processing(self):
        """Manually run universal agent processing."""
        if not self.universal_agent:
            raise RuntimeError("Universal agent not initialized")
        
        logger.info("Running manual universal agent processing...")
        self._log_system_event("MANUAL_RUN", "UniversalAgent", "Manual processing requested")
        
        processed_orders = self.universal_agent.run_universal_processing()
        
        self._log_system_event(
            "MANUAL_COMPLETE",
            "UniversalAgent", 
            f"Manual processing completed: {len(processed_orders)} orders processed"
        )
        
        return processed_orders
    
    def start(self):
        """Start the comprehensive email system."""
        if self.running:
            logger.warning("System is already running")
            return
        
        logger.info("Starting Comprehensive Email Processing System...")
        
        # Initialize components
        if not self.initialize_components():
            logger.error("Failed to initialize components, aborting startup")
            return
        
        self.running = True
        self.system_start_time = datetime.now()
        
        # Log system startup
        self._log_system_event("SYSTEM_START", "System", "Comprehensive email system started")
        
        # Start continuous polling agent
        if self.polling_agent and self.config.enable_continuous_polling:
            polling_thread = threading.Thread(
                target=self.polling_agent.start,
                name="PollingAgent",
                daemon=True
            )
            polling_thread.start()
            self.threads.append(polling_thread)
            logger.info("Continuous polling agent started")
        
        # Start universal agent scheduler
        if self.universal_agent and self.config.enable_universal_agent:
            self.start_universal_agent_scheduler()
        
        # Start dashboard server
        if self.dashboard and self.config.enable_dashboard:
            self.start_dashboard_server()
        
        # Start health monitoring
        self.start_health_monitor()
        
        # Start backup scheduler
        self.start_backup_scheduler()
        
        logger.info("All system components started successfully")
        
        # Display system information
        self.display_startup_info()
    
    def stop(self):
        """Stop the comprehensive email system gracefully."""
        if not self.running:
            return
        
        logger.info("Stopping Comprehensive Email Processing System...")
        self.running = False
        
        # Stop polling agent
        if self.polling_agent:
            self.polling_agent.stop()
        
        # Log system shutdown
        self._log_system_event("SYSTEM_STOP", "System", "Comprehensive email system stopped")
        
        logger.info("System stopped successfully")
    
    def display_startup_info(self):
        """Display system startup information."""
        status = self.get_system_status()
        
        print("\n" + "="*80)
        print("🚀 COMPREHENSIVE EMAIL PROCESSING SYSTEM")
        print("="*80)
        if self.system_start_time:
            print(f"⏰ Started: {self.system_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("⏰ Started: Not started yet")
        print(f"🔧 Configuration:")
        print(f"   • Universal Agent: {'✅ Enabled' if self.config.enable_universal_agent else '❌ Disabled'}")
        print(f"   • Continuous Polling: {'✅ Enabled' if self.config.enable_continuous_polling else '❌ Disabled'}")
        print(f"   • Dashboard: {'✅ Enabled' if self.config.enable_dashboard else '❌ Disabled'}")
        
        if self.config.enable_dashboard:
            print(f"🌐 Dashboard URL: http://localhost:{self.config.dashboard_port}")
        
        print("\nActive Components:")
        components = status['components']
        for component, info in components.items():
            if info['enabled'] and info['available']:
                print(f"   • {component.replace('_', ' ').title()}: ✅ Running")
        
        print("\n📊 Commands:")
        print("   • python comprehensive_email_system.py --status   (show status)")
        print("   • python comprehensive_email_system.py --manual   (run manual processing)")
        print("   • python comprehensive_email_system.py --stop     (stop system)")
        print("="*80 + "\n")

def main():
    """Main function for comprehensive email system."""
    parser = argparse.ArgumentParser(description='Comprehensive Email Processing System')
    parser.add_argument('--config', default='system_config.json', help='System configuration file')
    parser.add_argument('--start', action='store_true', help='Start the system (default)')
    parser.add_argument('--stop', action='store_true', help='Stop the system')
    parser.add_argument('--status', action='store_true', help='Show system status')
    parser.add_argument('--manual', action='store_true', help='Run manual universal processing')
    parser.add_argument('--backup', action='store_true', help='Create system backup')
    
    args = parser.parse_args()
    
    system = ComprehensiveEmailSystem(args.config)
    
    try:
        if args.status:
            status = system.get_system_status()
            print(json.dumps(status, indent=2))
            return
        
        if args.manual:
            if not system.initialize_components():
                print("❌ Failed to initialize components")
                return
            
            processed_orders = system.run_manual_universal_processing()
            print(f"✅ Manual processing completed: {len(processed_orders)} orders processed")
            return
        
        if args.backup:
            system.create_system_backup()
            print("✅ System backup created")
            return
        
        if args.stop:
            system.stop()
            print("✅ System stopped")
            return
        
        # Default: start the system
        system.start()
        
        # Keep main thread alive
        try:
            while system.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⚠️  Shutdown requested by user")
            system.stop()
    
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}")
        system.stop()

if __name__ == "__main__":
    main()