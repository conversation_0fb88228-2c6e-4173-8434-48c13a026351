#!/usr/bin/env python3
"""
Test script to verify the email order processor setup and configuration.
Run this before using the main application to ensure everything is configured correctly.
"""

import os
import sys
from pathlib import Path

def test_environment_file():
    """Test if .env file exists and contains required variables"""
    print("🔍 Testing environment configuration...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found. Copy .env.example to .env and configure it.")
        return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = [
        'GEMINI_API_KEY',
        'EXO_IP',
        'EXO_PORT', 
        'USER',
        'PWD',
        'API_KEY',
        'EXO_TOK'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment configuration looks good!")
    return True

def test_credentials_file():
    """Test if Gmail credentials file exists"""
    print("🔍 Testing Gmail credentials...")
    
    creds_file = os.getenv('GMAIL_CREDENTIALS_FILE', 'credentials.json')
    if not os.path.exists(creds_file):
        print(f"❌ Gmail credentials file '{creds_file}' not found.")
        print("   Download OAuth 2.0 credentials from Google Cloud Console.")
        return False
    
    print("✅ Gmail credentials file found!")
    return True

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("🔍 Testing Python dependencies...")
    
    required_packages = [
        'dotenv',
        'google.auth',
        'googleapiclient',
        'bs4',
        'fitz',
        'google.generativeai',
        'pydantic',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing Python packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed!")
    return True

def test_gemini_connection():
    """Test connection to Gemini API"""
    print("🔍 Testing Gemini API connection...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        import google.generativeai as genai
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ GEMINI_API_KEY not found in environment")
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel(os.getenv('GEMINI_MODEL', 'gemini-1.5-flash-latest'))
        
        # Test with a simple prompt
        response = model.generate_content("Say 'Hello' if you can hear me.")
        
        if response and response.text:
            print("✅ Gemini API connection successful!")
            return True
        else:
            print("❌ Gemini API responded but with empty content")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API connection failed: {e}")
        return False

def test_myob_connection():
    """Test connection to MYOB API"""
    print("🔍 Testing MYOB API connection...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        import requests
        import base64
        
        # Build MYOB connection details
        exa_ip = os.getenv('EXO_IP')
        exa_port = os.getenv('EXO_PORT')
        user = os.getenv('USER')
        pwd = os.getenv('PWD')
        api_key = os.getenv('API_KEY')
        exa_tok = os.getenv('EXO_TOK')
        
        base_url = f"http://{exa_ip}:{exa_port}"
        auth_string = f"{user}:{pwd}"
        base64_auth = base64.b64encode(auth_string.encode()).decode()
        
        headers = {
            "Authorization": f"Basic {base64_auth}",
            "Accept": "application/json",
            "x-myobapi-key": api_key,
            "x-myobapi-exoToken": exa_tok
        }
        
        # Test with a simple API call (assuming this endpoint exists)
        response = requests.get(f"{base_url}/", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ MYOB API connection successful!")
            return True
        else:
            print(f"⚠️  MYOB API responded with status {response.status_code}")
            print("   This might still work - check MYOB server status")
            return True  # Don't fail the test for this
            
    except Exception as e:
        print(f"❌ MYOB API connection failed: {e}")
        print("   Check your MYOB server configuration and network connectivity")
        return False

def test_gmail_labels():
    """Test Gmail label configuration"""
    print("🔍 Testing Gmail label configuration...")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    labels = os.getenv('GMAIL_LABELS_TO_PROCESS', '').split(',')
    labels = [label.strip() for label in labels if label.strip()]
    
    if not labels:
        print("❌ No Gmail labels configured in GMAIL_LABELS_TO_PROCESS")
        return False
    
    print(f"✅ Gmail labels configured: {', '.join(labels)}")
    return True

def main():
    """Run all tests"""
    print("🚀 Email Order Processor - Configuration Test")
    print("=" * 50)
    
    tests = [
        test_environment_file,
        test_credentials_file,
        test_dependencies,
        test_gmail_labels,
        test_gemini_connection,
        test_myob_connection
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Your setup looks good.")
        print("You can now run: python email_order_processor.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues before running the main application.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
