import requests
import json
import os
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def fetch_exo_discovery(ip_address="***********:", port=8888):
    """
    Fetches the EXO discovery endpoint and saves the response as a JSON file.
    
    Args:
        ip_address (str): The IP address of the EXO server
        port (int): The port number where the EXO API is running
    
    Returns:
        str: Path to the saved JSON file or None if the request fails
    """
    url = f"http://{ip_address}:{port}/discovery"
    logger.info(f"Fetching EXO discovery data from: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()  # Raise an exception for 4XX/5XX responses
        
        # Parse the response as JSON
        discovery_data = response.json()
        
        # Save to a JSON file
        output_file = os.path.join(os.path.dirname(__file__), "exo_discovery_data.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(discovery_data, f, indent=4)
        
        logger.info(f"Discovery data saved to: {output_file}")
        return output_file
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching discovery data: {e}")
        return None
    except json.JSONDecodeError:
        logger.error("Failed to parse response as JSON")
        # Save the raw response for inspection
        output_file = os.path.join(os.path.dirname(__file__), "exo_discovery_raw.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(response.text)
        logger.info(f"Raw response saved to: {output_file}")
        return None

if __name__ == "__main__":
    # Use the IP address and port provided
    output_file = fetch_exo_discovery(ip_address="***********",port=8888)
    
    if output_file:
        print(f"Success! Discovery data saved to: {output_file}")
    else:
        print("Failed to retrieve EXO discovery data. Check the logs for details.")