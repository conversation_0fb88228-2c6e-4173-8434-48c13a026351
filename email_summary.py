"""
Email Summary Processor - reads through inbox emails for the last 72 hours
and creates a summary of events, using X-Original-Sender as the actual from field.
"""
import logging
import json
import email.utils
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from collections import defaultdict
import re
import os

from gmail_service import GmailService
from config import config
from memory_client import PersistentMemoryClient # Import PersistentMemoryClient

# Supabase imports
try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    create_client = None
    Client = None
    print("Supabase not installed. Run 'pip install supabase' to enable database features.")

logger = logging.getLogger(__name__)

class SupabaseService:
    """Handles Supabase database operations for email data."""

    def __init__(self):
        """Initialize Supabase client."""
        if not SUPABASE_AVAILABLE:
            raise ImportError("Supabase is not available. Install with 'pip install supabase'")

        self.url = os.environ.get("SUPABASE_URL")
        self.key = os.environ.get("SUPABASE_KEY")

        if not self.url or not self.key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY environment variables must be set")

        self.supabase = create_client(self.url, self.key)
        logger.info("Supabase client initialized")

    def create_email_summary_table(self):
        """Create the email_summaries table if it doesn't exist."""
        try:
            # This would typically be done via SQL migration
            # For now, we'll assume the table exists or create it manually
            logger.info("Email summary table check/creation completed")
        except Exception as e:
            logger.error(f"Error creating email summary table: {e}")
            raise

    def insert_email_events(self, events: List['EmailEvent'], report_id: Optional[str] = None) -> bool:
        """Insert email events into the database."""
        try:
            # Convert EmailEvent dataclasses to dictionaries for insertion
            event_data = []
            for event in events:
                event_dict = {
                    'email_id': event.id,
                    'subject': event.subject,
                    'sender': event.sender,
                    'original_sender': event.original_sender,
                    'from_field': event.from_field,
                    'timestamp': event.timestamp.isoformat(),
                    'snippet': event.snippet,
                    'has_attachments': event.has_attachments,
                    'attachment_count': event.attachment_count,
                    'labels': event.labels,
                    'is_unread': event.is_unread,
                    'thread_id': event.thread_id,
                    'report_id': report_id,
                    'created_at': datetime.now().isoformat()
                }
                event_data.append(event_dict)

            # Insert into Supabase
            result = self.supabase.table('email_events').insert(event_data).execute()

            if result.data:
                logger.info(f"Successfully inserted {len(event_data)} email events into database")
                return True
            else:
                logger.error("Failed to insert email events - no data returned")
                return False

        except Exception as e:
            logger.error(f"Error inserting email events: {e}")
            return False

    def insert_summary_report(self, report: 'EmailSummaryReport') -> Optional[str]:
        """Insert summary report metadata into the database."""
        try:
            report_data = {
                'report_generated': report.report_generated.isoformat(),
                'period_start': report.period_start.isoformat(),
                'period_end': report.period_end.isoformat(),
                'total_emails': report.total_emails,
                'unread_emails': report.unread_emails,
                'emails_with_attachments': report.emails_with_attachments,
                'sender_summary': report.sender_summary,
                'subject_patterns': report.subject_patterns,
                'hourly_distribution': report.hourly_distribution,
                'daily_distribution': report.daily_distribution
            }

            result = self.supabase.table('email_summary_reports').insert(report_data).execute()

            if result.data and len(result.data) > 0:
                report_id = result.data[0]['id']
                logger.info(f"Successfully inserted summary report with ID: {report_id}")
                return report_id
            else:
                logger.error("Failed to insert summary report")
                return None

        except Exception as e:
            logger.error(f"Error inserting summary report: {e}")
            return None

    def get_recent_reports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent summary reports from the database."""
        try:
            result = self.supabase.table('email_summary_reports')\
                .select('*')\
                .order('report_generated', desc=True)\
                .limit(limit)\
                .execute()

            return result.data if result.data else []

        except Exception as e:
            logger.error(f"Error fetching recent reports: {e}")
            return []

@dataclass
class EmailEvent:
    """Represents a single email event."""
    id: str
    subject: str
    sender: str  # This will be X-Original-Sender if available, otherwise From
    original_sender: Optional[str]  # X-Original-Sender field
    from_field: str  # Standard From field
    timestamp: datetime
    snippet: str
    has_attachments: bool
    attachment_count: int
    labels: List[str]
    is_unread: bool
    thread_id: str
    full_body: Optional[str] = None # Add full_body field

@dataclass
class EmailSummaryReport:
    """Complete email summary report."""
    report_generated: datetime
    period_start: datetime
    period_end: datetime
    total_emails: int
    unread_emails: int
    emails_with_attachments: int
    events: List[EmailEvent]
    sender_summary: Dict[str, int]  # sender -> count
    subject_patterns: Dict[str, int]  # pattern -> count
    hourly_distribution: Dict[int, int]  # hour -> count
    daily_distribution: Dict[str, int]  # date -> count

class EmailSummaryProcessor:
    """Processes inbox emails to create summary reports."""

    def __init__(self, use_database: bool = True):
        """Initialize the email summary processor."""
        logger.info("Initializing Email Summary Processor")
        self.gmail_service = GmailService()

        # Initialize Supabase service if available and requested
        self.supabase_service = None
        if use_database and SUPABASE_AVAILABLE:
            try:
                self.supabase_service = SupabaseService()
                logger.info("Supabase service initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Supabase service: {e}")
                logger.warning("Continuing without database features")
        elif use_database and not SUPABASE_AVAILABLE:
            logger.warning("Database features requested but Supabase not available")

        # Initialize PersistentMemoryClient
        try:
            self.memory_client = PersistentMemoryClient()
            logger.info("PersistentMemoryClient initialized")
        except Exception as e:
            logger.error(f"Failed to initialize PersistentMemoryClient: {e}")
            self.memory_client = None


        logger.info("Email Summary Processor initialized")

    def generate_72_hour_summary(self, save_to_database: bool = True) -> EmailSummaryReport:
        """Generate a summary of emails from the last 24 hours."""
        logger.info("Generating 24-hour email summary")

        # Calculate time range``
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=72)

        logger.info(f"Analyzing emails from {start_time} to {end_time}")

        # Fetch emails from the period
        events = self._fetch_emails_in_period(start_time, end_time)

        # Generate summary statistics
        report = self._create_summary_report(events, start_time, end_time)

        # Save to database if enabled
        if save_to_database and self.supabase_service:
            try:
                # Insert summary report first to get report_id
                report_id = self.supabase_service.insert_summary_report(report)

                if report_id:
                    # Insert individual email events with report_id
                    success = self.supabase_service.insert_email_events(events, report_id)
                    if success:
                        logger.info(f"Successfully saved email summary to database with ID: {report_id}")
                    else:
                        logger.warning("Failed to save email events to database")
                else:
                    logger.warning("Failed to save summary report to database")

            except Exception as e:
                logger.error(f"Error saving to database: {e}")
                logger.warning("Continuing without database save")

        logger.info(f"Generated summary for {len(events)} emails")
        return report

    def _fetch_emails_in_period(self, start_time: datetime, end_time: datetime) -> List[EmailEvent]:
        """Fetch all emails within the specified time period."""
        # Convert to Gmail search format
        start_str = start_time.strftime("%Y/%m/%d")
        end_str = end_time.strftime("%Y/%m/%d")

        # Gmail query for the time period
        query = f"in:inbox after:{start_str} before:{end_str}"

        logger.info(f"Fetching emails with query: {query}")

        try:
            # Get list of message IDs
            results = self.gmail_service.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=500  # Adjust as needed
            ).execute()

            messages = results.get('messages', [])
            logger.info(f"Found {len(messages)} messages in period")

            events = []
            for msg_ref in messages:
                try:
                    # Fetch full message to get body for embedding
                    msg = self.gmail_service.service.users().messages().get(
                        userId='me',
                        id=msg_ref['id'],
                        format='full' # Request full format to get body
                    ).execute()

                    event = self._process_single_message(msg_ref['id'], msg) # Pass full message to process
                    if event:
                        # Filter by actual timestamp (Gmail search is sometimes imprecise)
                        if start_time <= event.timestamp <= end_time:
                            events.append(event)
                            # Chunk and embed the email content
                            if self.memory_client:
                                self._chunk_and_embed_email(event)

                except Exception as e:
                    logger.error(f"Error processing message {msg_ref['id']}: {e}")
                    continue

            logger.info(f"Processed {len(events)} valid events")
            return events

        except Exception as e:
            logger.error(f"Error fetching emails: {e}")
            return []

    def _process_single_message(self, message_id: str, msg: Dict[str, Any]) -> Optional[EmailEvent]:
        """Process a single message to extract event information and full body."""
        try:
            # Extract headers
            headers = msg['payload'].get('headers', [])
            header_dict = {h['name'].lower(): h['value'] for h in headers}

            # Get sender information - prioritize X-Original-Sender
            original_sender = header_dict.get('x-original-sender')
            from_field = header_dict.get('from', 'Unknown Sender')
            sender = original_sender if original_sender else from_field

            # Extract other fields
            subject = header_dict.get('subject', 'No Subject')
            date_str = header_dict.get('date', '')

            # Parse timestamp
            try:
                # Gmail date format parsing
                timestamp = self._parse_gmail_date(date_str)
            except:
                logger.warning(f"Could not parse date '{date_str}' for message {message_id}")
                timestamp = datetime.now()

            # Get message metadata
            snippet = msg.get('snippet', '')
            thread_id = msg.get('threadId', '')
            label_ids = msg.get('labelIds', [])

            # Extract full body
            full_body = self._get_email_body(msg['payload'])

            # Check for attachments
            has_attachments, attachment_count = self._check_attachments(msg['payload'])

            # Check if unread
            is_unread = 'UNREAD' in label_ids

            # Convert label IDs to names (basic mapping)
            labels = self._convert_label_ids_to_names(label_ids)

            return EmailEvent(
                id=message_id,
                subject=subject,
                sender=sender,
                original_sender=original_sender,
                from_field=from_field,
                timestamp=timestamp,
                snippet=snippet,
                has_attachments=has_attachments,
                attachment_count=attachment_count,
                labels=labels,
                is_unread=is_unread,
                thread_id=thread_id,
                full_body=full_body # Include full body
            )

        except Exception as e:
            logger.error(f"Error processing message {message_id}: {e}")
            return None

    def _get_email_body(self, payload: Dict[str, Any]) -> Optional[str]:
        """Extract the plain text or HTML body from the email payload."""
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    return self._decode_body_data(part['body'].get('data', ''))
                if part['mimeType'] == 'text/html':
                    # Optionally process HTML to text if needed
                    return self._decode_body_data(part['body'].get('data', ''))
                if 'parts' in part:
                    # Recurse into nested parts
                    nested_body = self._get_email_body(part)
                    if nested_body:
                        return nested_body
        elif payload['mimeType'] == 'text/plain':
            return self._decode_body_data(payload['body'].get('data', ''))
        elif payload['mimeType'] == 'text/html':
             return self._decode_body_data(payload['body'].get('data', ''))

        return None

    def _decode_body_data(self, data: str) -> str:
        """Decode base64 email body data."""
        import base64
        try:
            return base64.urlsafe_b64decode(data).decode('utf-8')
        except Exception as e:
            logger.error(f"Error decoding email body data: {e}")
            return ""


    def _chunk_and_embed_email(self, event: EmailEvent, chunk_size: int = 500, chunk_overlap: int = 100):
        """Chunks email body and embeds into ChromaDB."""
        if not self.memory_client or not event.full_body:
            return

        text = event.full_body
        text_len = len(text)
        chunks = []
        start = 0

        while start < text_len:
            end = start + chunk_size
            chunk = text[start:min(end, text_len)]
            chunks.append(chunk)
            start += chunk_size - chunk_overlap

        logger.info(f"Chunked email {event.id} into {len(chunks)} chunks.")

        documents = chunks
        ids = [f"{event.id}_chunk_{i}" for i in range(len(chunks))]
        metadatas = [{"email_id": event.id, "subject": event.subject, "sender": event.sender, "timestamp": event.timestamp.isoformat(), "chunk_index": i} for i in range(len(chunks))]

        try:
            self.memory_client.add_memory(documents=documents, ids=ids, metadatas=metadatas)
            logger.info(f"Embedded {len(chunks)} chunks for email {event.id} into ChromaDB.")
        except Exception as e:
            logger.error(f"Error embedding chunks for email {event.id}: {e}")


    def _parse_gmail_date(self, date_str: str) -> datetime:
        """Parse Gmail date string to datetime object."""
        # Common Gmail date formats
        formats = [
            "%a, %d %b %Y %H:%M:%S %z",
            "%d %b %Y %H:%M:%S %z",
            "%a, %d %b %Y %H:%M:%S %Z",
            "%d %b %Y %H:%M:%S %Z"
        ]

        for fmt in formats:
            try:
                # Remove timezone info for simpler parsing
                clean_date = re.sub(r'\s*\([^)]*\)$', '', date_str)
                return datetime.strptime(clean_date, fmt).replace(tzinfo=None)
            except ValueError:
                continue

        # Fallback: try to extract basic date components
        try:
            import email.utils
            parsed = email.utils.parsedate_tz(date_str)
            if parsed:
                return datetime(*parsed[:6])
        except:
            pass

        raise ValueError(f"Could not parse date: {date_str}")

    def _check_attachments(self, payload: Dict[str, Any]) -> tuple[bool, int]:
        """Check if message has attachments and count them."""
        attachment_count = 0

        def count_attachments_recursive(parts):
            nonlocal attachment_count
            if isinstance(parts, list):
                for part in parts:
                    count_attachments_recursive(part)
            elif isinstance(parts, dict):
                filename = parts.get('filename', '')
                if filename and parts.get('body', {}).get('attachmentId'):
                    attachment_count += 1

                if 'parts' in parts:
                    count_attachments_recursive(parts['parts'])

        if 'parts' in payload:
            count_attachments_recursive(payload['parts'])

        return attachment_count > 0, attachment_count

    def _convert_label_ids_to_names(self, label_ids: List[str]) -> List[str]:
        """Convert Gmail label IDs to readable names."""
        # Basic system label mapping
        system_labels = {
            'INBOX': 'Inbox',
            'SENT': 'Sent',
            'DRAFT': 'Drafts',
            'SPAM': 'Spam',
            'TRASH': 'Trash',
            'UNREAD': 'Unread',
            'STARRED': 'Starred',
            'IMPORTANT': 'Important'
        }

        labels = []
        for label_id in label_ids:
            if label_id in system_labels:
                labels.append(system_labels[label_id])
            else:
                # For custom labels, you might want to fetch the actual name
                labels.append(label_id)

        return labels

    def _create_summary_report(self, events: List[EmailEvent], start_time: datetime, end_time: datetime) -> EmailSummaryReport:
        """Create a comprehensive summary report from events."""
        logger.info("Creating summary report")

        # Basic counts
        total_emails = len(events)
        unread_emails = sum(1 for e in events if e.is_unread)
        emails_with_attachments = sum(1 for e in events if e.has_attachments)

        # Sender analysis
        sender_summary = defaultdict(int)
        for event in events:
            sender_summary[event.sender] += 1

        # Subject pattern analysis
        subject_patterns = defaultdict(int)
        for event in events:
            # Extract common patterns (Purchase Order, Invoice, etc.)
            subject_lower = event.subject.lower()
            if 'purchase order' in subject_lower or 'po ' in subject_lower:
                subject_patterns['Purchase Orders'] += 1
            elif 'invoice' in subject_lower:
                subject_patterns['Invoices'] += 1
            elif 'quote' in subject_lower or 'quotation' in subject_lower:
                subject_patterns['Quotes'] += 1
            elif 'delivery' in subject_lower or 'shipment' in subject_lower:
                subject_patterns['Delivery/Shipment'] += 1
            elif 'order' in subject_lower:
                subject_patterns['Orders (General)'] += 1
            else:
                subject_patterns['Other'] += 1

        # Temporal analysis
        hourly_distribution = defaultdict(int)
        daily_distribution = defaultdict(int)

        for event in events:
            hour = event.timestamp.hour
            date_str = event.timestamp.strftime('%Y-%m-%d')

            hourly_distribution[hour] += 1
            daily_distribution[date_str] += 1

        return EmailSummaryReport(
            report_generated=datetime.now(),
            period_start=start_time,
            period_end=end_time,
            total_emails=total_emails,
            unread_emails=unread_emails,
            emails_with_attachments=emails_with_attachments,
            events=events,
            sender_summary=dict(sender_summary),
            subject_patterns=dict(subject_patterns),
            hourly_distribution=dict(hourly_distribution),
            daily_distribution=dict(daily_distribution)
        )

    def save_summary_report(self, report: EmailSummaryReport, format: str = 'json') -> str:
        """Save the summary report to file."""
        timestamp = report.report_generated.strftime('%Y%m%d_%H%M%S')

        if format.lower() == 'json':
            filename = f"reports/email_summary_{timestamp}.json"
            return self._save_json_report(report, filename)
        elif format.lower() == 'markdown':
            filename = f"reports/email_summary_{timestamp}.md"
            return self._save_markdown_report(report, filename)
        else:
            raise ValueError(f"Unsupported format: {format}")

    def _save_json_report(self, report: EmailSummaryReport, filename: str) -> str:
        """Save report as JSON."""
        # Convert to serializable format
        report_dict = {
            'report_generated': report.report_generated.isoformat(),
            'period_start': report.period_start.isoformat(),
            'period_end': report.period_end.isoformat(),
            'total_emails': report.total_emails,
            'unread_emails': report.unread_emails,
            'emails_with_attachments': report.emails_with_attachments,
            'sender_summary': report.sender_summary,
            'subject_patterns': report.subject_patterns,
            'hourly_distribution': report.hourly_distribution,
            'daily_distribution': report.daily_distribution,
            'events': [
                {
                    'id': event.id,
                    'subject': event.subject,
                    'sender': event.sender,
                    'original_sender': event.original_sender,
                    'from_field': event.from_field,
                    'timestamp': event.timestamp.isoformat(),
                    'snippet': event.snippet,
                    'has_attachments': event.has_attachments,
                    'attachment_count': event.attachment_count,
                    'labels': event.labels,
                    'is_unread': event.is_unread,
                    'thread_id': event.thread_id,
                    'full_body': event.full_body # Include full body in JSON
                }
                for event in report.events
            ]
        }

        filepath = filename # Use filename directly as it includes the reports directory
        os.makedirs("reports", exist_ok=True)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved JSON report to: {filepath}")
        return filepath

    def _save_markdown_report(self, report: EmailSummaryReport, filename: str) -> str:
        """Save report as Markdown."""
        content = self._generate_markdown_content(report)

        filepath = filename # Use filename directly as it includes the reports directory
        os.makedirs("reports", exist_ok=True)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"Saved Markdown report to: {filepath}")
        return filepath

    def _generate_markdown_content(self, report: EmailSummaryReport) -> str:
        """Generate Markdown content for the report."""
        content = f"""# Email Summary Report

**Report Generated:** {report.report_generated.strftime('%Y-%m-%d %H:%M:%S')}
**Period:** {report.period_start.strftime('%Y-%m-%d %H:%M')} to {report.period_end.strftime('%Y-%m-%d %H:%M')} (72 hours)

## Summary Statistics

- **Total Emails:** {report.total_emails}
- **Unread Emails:** {report.unread_emails}
- **Emails with Attachments:** {report.emails_with_attachments}

## Top Senders
"""

        # Top senders (sorted by count)
        sorted_senders = sorted(report.sender_summary.items(), key=lambda x: x[1], reverse=True)
        for sender, count in sorted_senders[:10]:  # Top 10
            content += f"- **{sender}:** {count} emails\n"

        content += "\n## Subject Patterns\n"
        for pattern, count in sorted(report.subject_patterns.items(), key=lambda x: x[1], reverse=True):
            content += f"- **{pattern}:** {count} emails\n"

        content += "\n## Daily Distribution\n"
        for date, count in sorted(report.daily_distribution.items()):
            content += f"- **{date}:** {count} emails\n"

        content += "\n## Hourly Distribution\n"
        max_count = max(report.hourly_distribution.values()) if report.hourly_distribution else 1
        for hour in range(24):
            count = report.hourly_distribution.get(hour, 0)
            if count > 0:
                bar = "█" * max(1, int(count * 20 / max_count))
                content += f"  {hour:02d}:00 │{bar:<20}│ {count}\n"


        content += f"\n## Recent Events (Last 20)\n"
        recent_events = sorted(report.events, key=lambda x: x.timestamp, reverse=True)[:20]

        for event in recent_events:
            status = "🔵 Unread" if event.is_unread else "✅ Read"
            attachments = f" 📎({event.attachment_count})" if event.has_attachments else ""
            original_note = f" (Original: {event.original_sender})" if event.original_sender != event.from_field else ""

            content += f"""
### {event.timestamp.strftime('%Y-%m-%d %H:%M')} - {status}{attachments}
**From:** {event.sender}{original_note}
**Subject:** {event.subject}
**Snippet:** {event.snippet[:100]}...
"""

        return content

    def print_summary_report(self, report: EmailSummaryReport):
        """Print a summary report to console."""
        print("\n" + "="*80)
        print("EMAIL SUMMARY REPORT - LAST 72 HOURS")
        print("="*80)
        print(f"Report Generated: {report.report_generated.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Period: {report.period_start.strftime('%Y-%m-%d %H:%M')} to {report.period_end.strftime('%Y-%m-%d %H:%M')}")
        print(f"Total Emails: {report.total_emails}")
        print(f"Unread Emails: {report.unread_emails}")
        print(f"Emails with Attachments: {report.emails_with_attachments}")

        print("\n📊 TOP SENDERS:")
        sorted_senders = sorted(report.sender_summary.items(), key=lambda x: x[1], reverse=True)
        for sender, count in sorted_senders[:10]:
            print(f"  • {sender}: {count} emails")

        print("\n📋 SUBJECT PATTERNS:")
        for pattern, count in sorted(report.subject_patterns.items(), key=lambda x: x[1], reverse=True):
            print(f"  • {pattern}: {count} emails")

        print("\n📅 DAILY BREAKDOWN:")
        for date, count in sorted(report.daily_distribution.items()):
            print(f"  • {date}: {count} emails")

        print("\n🕐 HOURLY ACTIVITY:")
        max_count = max(report.hourly_distribution.values()) if report.hourly_distribution else 1
        for hour in range(24):
            count = report.hourly_distribution.get(hour, 0)
            if count > 0:
                bar = "█" * max(1, int(count * 20 / max_count))
                print(f"  {hour:02d}:00 │{bar:<20}│ {count}")


    def get_recent_database_reports(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent summary reports from the database."""
        if not self.supabase_service:
            logger.warning("Database service not available")
            return []

        return self.supabase_service.get_recent_reports(limit)

    def print_database_reports(self, limit: int = 5):
        """Print recent database reports summary."""
        reports = self.get_recent_database_reports(limit)

        if not reports:
            print("No database reports found or database not available")
            return

        print("\n" + "="*80)
        print(f"RECENT DATABASE REPORTS (Last {len(reports)})")
        print("="*80)

        for i, report in enumerate(reports, 1):
            print(f"\n{i}. Report ID: {report.get('id', 'Unknown')}")
            print(f"   Generated: {report.get('report_generated', 'Unknown')}")
            print(f"   Period: {report.get('period_start', 'Unknown')} to {report.get('period_end', 'Unknown')}")
            print(f"   Total Emails: {report.get('total_emails', 0)}")
            print(f"   Unread: {report.get('unread_emails', 0)}")
            print(f"   With Attachments: {report.get('emails_with_attachments', 0)}")

def main():
    """Main function to run email summary processor."""

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        # Initialize processor with database support
        processor = EmailSummaryProcessor(use_database=True)

        # Show recent database reports if available
        if processor.supabase_service:
            print("\n📊 Recent Database Reports:")
            processor.print_database_reports(3)

        # Generate 72-hour summary (will save to database if available)
        report = processor.generate_72_hour_summary(save_to_database=True)

        # Print to console
        processor.print_summary_report(report)

        # Save file reports
        json_file = processor.save_summary_report(report, 'json')
        markdown_file = processor.save_summary_report(report, 'markdown')

        print(f"\n📄 Reports saved:")
        print(f"  JSON: {json_file}")
        print(f"  Markdown: {markdown_file}")

        # Show database status
        if processor.supabase_service:
            print(f"  Database: ✅ Saved to Supabase")
        else:
            print(f"  Database: ❌ Not available (install supabase package)")

    except Exception as e:
        logger.error(f"Error in email summary processor: {e}")
        print(f"Error: {e}")

        # If it's a Supabase error, suggest fallback
        if "supabase" in str(e).lower():
            print("\n💡 Tip: You can still run without database features by setting use_database=False")
            print("Or install Supabase with: pip install supabase")


if __name__ == "__main__":
    main()
