-- Supabase Schema for Email Summary System
-- Run this SQL in your Supabase SQL Editor to create the necessary tables

-- Table for storing email summary reports
CREATE TABLE IF NOT EXISTS email_summary_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    report_generated TIMESTAMPTZ NOT NULL,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    total_emails INTEGER NOT NULL DEFAULT 0,
    unread_emails INTEGER NOT NULL DEFAULT 0,
    emails_with_attachments INTEGER NOT NULL DEFAULT 0,
    sender_summary JSONB DEFAULT '{}',
    subject_patterns JSONB DEFAULT '{}',
    hourly_distribution JSONB DEFAULT '{}',
    daily_distribution JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table for storing individual email events
CREATE TABLE IF NOT EXISTS email_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email_id VARCHAR NOT NULL,
    subject TEXT,
    sender TEXT,
    original_sender TEXT,
    from_field TEXT,
    timestamp TIMESTAMPTZ,
    snippet TEXT,
    has_attachments BOOLEAN DEFAULT FALSE,
    attachment_count INTEGER DEFAULT 0,
    labels JSONB DEFAULT '[]',
    is_unread BOOLEAN DEFAULT FALSE,
    thread_id VARCHAR,
    report_id UUID REFERENCES email_summary_reports(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_events_email_id ON email_events(email_id);
CREATE INDEX IF NOT EXISTS idx_email_events_sender ON email_events(sender);
CREATE INDEX IF NOT EXISTS idx_email_events_timestamp ON email_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_email_events_report_id ON email_events(report_id);
CREATE INDEX IF NOT EXISTS idx_email_events_is_unread ON email_events(is_unread);
CREATE INDEX IF NOT EXISTS idx_email_summary_reports_generated ON email_summary_reports(report_generated);

-- Row Level Security (RLS) policies
ALTER TABLE email_summary_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_events ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read and write their own data
CREATE POLICY "Users can view email summary reports" ON email_summary_reports
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert email summary reports" ON email_summary_reports
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can view email events" ON email_events
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can insert email events" ON email_events
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update the updated_at column
CREATE TRIGGER update_email_summary_reports_updated_at 
    BEFORE UPDATE ON email_summary_reports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_events_updated_at 
    BEFORE UPDATE ON email_events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for easier querying
CREATE OR REPLACE VIEW recent_email_summaries AS
SELECT 
    r.id,
    r.report_generated,
    r.period_start,
    r.period_end,
    r.total_emails,
    r.unread_emails,
    r.emails_with_attachments,
    COUNT(e.id) as stored_events_count
FROM email_summary_reports r
LEFT JOIN email_events e ON r.id = e.report_id
GROUP BY r.id, r.report_generated, r.period_start, r.period_end, 
         r.total_emails, r.unread_emails, r.emails_with_attachments
ORDER BY r.report_generated DESC;

-- View for email statistics by sender
CREATE OR REPLACE VIEW sender_statistics AS
SELECT 
    sender,
    COUNT(*) as total_emails,
    COUNT(*) FILTER (WHERE is_unread = true) as unread_emails,
    COUNT(*) FILTER (WHERE has_attachments = true) as emails_with_attachments,
    MIN(timestamp) as first_email,
    MAX(timestamp) as latest_email
FROM email_events 
WHERE sender IS NOT NULL
GROUP BY sender
ORDER BY total_emails DESC;

-- View for daily email trends
CREATE OR REPLACE VIEW daily_email_trends AS
SELECT 
    DATE(timestamp) as email_date,
    COUNT(*) as total_emails,
    COUNT(*) FILTER (WHERE is_unread = true) as unread_emails,
    COUNT(*) FILTER (WHERE has_attachments = true) as emails_with_attachments,
    COUNT(DISTINCT sender) as unique_senders
FROM email_events 
WHERE timestamp IS NOT NULL
GROUP BY DATE(timestamp)
ORDER BY email_date DESC;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT USAGE ON SCHEMA public TO authenticated;
-- GRANT ALL ON email_summary_reports TO authenticated;
-- GRANT ALL ON email_events TO authenticated;
-- GRANT SELECT ON recent_email_summaries TO authenticated;
-- GRANT SELECT ON sender_statistics TO authenticated;
-- GRANT SELECT ON daily_email_trends TO authenticated;

-- Insert a sample comment for verification
INSERT INTO email_summary_reports (
    report_generated, 
    period_start, 
    period_end, 
    total_emails, 
    unread_emails, 
    emails_with_attachments,
    sender_summary,
    subject_patterns,
    hourly_distribution,
    daily_distribution
) VALUES (
    NOW(),
    NOW() - INTERVAL '72 hours',
    NOW(),
    0,
    0,
    0,
    '{"system": 0}',
    '{"initialization": 1}',
    '{}',
    '{}'
) ON CONFLICT DO NOTHING;

-- Show confirmation message
SELECT 'Email Summary Database Schema Created Successfully! 🎉' as message;
