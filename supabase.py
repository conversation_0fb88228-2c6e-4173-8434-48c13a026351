#!/usr/bin/env python3
"""
Supabase Database Setup Script

This script helps set up the Supabase database connection for the AI Email Manager.
It checks for existing credentials and tests the connection.
"""

import os
import sys
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Ensure the project root is in the Python path
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

# Import project modules
from supabase import create_client, Client
from config import config
from email_summary import EmailSummaryProcessor 

# Default Supabase URL from your provided JS snippet
DEFAULT_SUPABASE_URL = "https://ubvchwzcipqequgmlkyi.supabase.co"

async def setup_supabase():
    """
    Set up the Supabase connection and test it.
    """
    print("\n===== Supabase Database Setup =====")
    
    # Load environment variables from .env file
    load_dotenv()
    
    # Check for existing Supabase credentials
    supabase_url = os.environ.get('SUPABASE_URL', DEFAULT_SUPABASE_URL)
    supabase_key = os.environ.get('SUPABASE_KEY')
    
    if supabase_url and supabase_key:
        print(f"Found existing Supabase credentials:")
        print(f"URL: {supabase_url}")
        print(f"API Key: {'*' * 10}{supabase_key[-5:] if supabase_key else ''}")
        
        use_existing = input("\nUse these credentials? (y/n) [y]: ").strip().lower() or 'y'
        
        if use_existing != 'y':
            supabase_url = input(f"\nEnter your Supabase URL [{DEFAULT_SUPABASE_URL}]: ").strip() or DEFAULT_SUPABASE_URL
            supabase_key = input("Enter your Supabase API Key: ").strip()
            
            if not supabase_url or not supabase_key:
                print("Error: Supabase URL and API Key are required.")
                return False
            
            # Update .env file with new credentials
            update_env_file(supabase_url, supabase_key)
    else:
        print("No Supabase API key found in .env file.")
        print(f"Default Supabase URL: {DEFAULT_SUPABASE_URL}")
        
        supabase_url = input(f"\nEnter your Supabase URL [{DEFAULT_SUPABASE_URL}]: ").strip() or DEFAULT_SUPABASE_URL
        supabase_key = input("Enter your Supabase API Key: ").strip()
        
        if not supabase_url or not supabase_key:
            print("Error: Supabase URL and API Key are required.")
            return False
        
        # Create/update .env file with credentials
        update_env_file(supabase_url, supabase_key)
    
    # Test the connection
    print("\nTesting Supabase connection...")
    try:
        client = create_client(supabase_url, supabase_key)
        print("Connection established successfully!")
        
        # Initialize database schema
        print("\nSetting up database schema...")
        result = await client.setup_database()
        
        if result:
            print("Database schema setup successful!")
            print("\nYour Supabase database is now ready to use with AI Email Manager.")
            print("\nIMPORTANT: To complete database setup, please run the SQL setup script:")
            print(f"1. Log into your Supabase dashboard")
            print(f"2. Go to SQL Editor")
            print(f"3. Open and run the file at {os.path.join(project_root, 'db/supabase_schema.sql')}")
            print("\nThis will create all necessary tables and functions for the AI Email Manager.")
            return True
        else:
            print("Database schema check failed. Check logs for details.")
            return False
        
    except Exception as e:
        print(f"Error connecting to Supabase: {str(e)}")
        print("\nPlease check your credentials and try again.")
        return False

def update_env_file(url, key):
    """Update the .env file with Supabase credentials."""
    env_path = os.path.join(project_root, ".env")
    
    # Check if .env exists and read existing content
    env_content = {}
    if os.path.exists(env_path):
        with open(env_path, 'r') as f:
            for line in f:
                if line.strip() and not line.strip().startswith('#'):
                    if '=' in line:
                        var_name, var_value = line.strip().split('=', 1)
                        env_content[var_name] = var_value
    
    # Update with Supabase credentials
    env_content['SUPABASE_URL'] = url
    env_content['SUPABASE_KEY'] = key
    
    # Write back to .env file
    with open(env_path, 'w') as f:
        for key, value in env_content.items():
            f.write(f"{key}={value}\n")
    
    print(f"Credentials saved to {env_path}")
    
    # Update environment variables for current session
    os.environ['SUPABASE_URL'] = url
    os.environ['SUPABASE_KEY'] = key

if __name__ == "__main__":
    result = asyncio.run(setup_supabase())
    sys.exit(0 if result else 1)
