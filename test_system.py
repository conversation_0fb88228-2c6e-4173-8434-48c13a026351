"""
Test script to demonstrate the new modular email order processor.
"""
import logging
from utils.config import config
from orchestrators.main_processor import EmailOrderProcessor

# Set up logging for testing
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_configuration():
    """Test configuration loading."""
    print("🔧 Testing Configuration...")
    try:
        config.validate_config()
        print("✅ Configuration validation passed")
        print(f"   - Gmail credentials: {config.GMAIL_CREDENTIALS_FILE}")
        print(f"   - Labels to process: {config.GMAIL_LABELS_TO_PROCESS}")
        print(f"   - MYOB server: {config.MYOB_BASE_URL}")
        print(f"   - Gemini model: {config.GEMINI_MODEL}")
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False
    return True

def test_services_initialization():
    """Test individual service initialization."""
    print("\\n🚀 Testing Service Initialization...")
    
    try:
        from services.gmail_service import GmailService
        from services.llm_service import LLMService
        from services.myob_service import MyobService
        
        print("✅ All service modules imported successfully")
        
        # Test LLM service (doesn't require authentication)
        try:
            llm_service = LLMService()
            print("✅ LLM Service initialized")
        except Exception as e:
            print(f"❌ LLM Service failed: {e}")
            return False
        
        # Test MYOB service (doesn't require network connection to initialize)
        try:
            myob_service = MyobService()
            print("✅ MYOB Service initialized")
        except Exception as e:
            print(f"❌ MYOB Service failed: {e}")
            return False
        
        print("⚠️  Gmail Service requires authentication - skipping in test")
        
    except Exception as e:
        print(f"❌ Service initialization failed: {e}")
        return False
    
    return True

def test_data_models():
    """Test Pydantic data models."""
    print("\\n📊 Testing Data Models...")
    
    try:
        from utils.models import ExtractedOrder, CustomerDetails, OrderLine
        
        # Create a sample order
        sample_order = ExtractedOrder(
            customer_details=CustomerDetails(
                debtor_id=10981,
                customer_order_number="LVA4401196688"
            ),
            order_status=0,
            order_lines=[
                OrderLine(stockcode="TSSU-ORA", orderquantity=5.0),
                OrderLine(stockcode="ITEM002", orderquantity=2.0)
            ],
            X_SHIPVIA="CAPITAL"
        )
        
        print("✅ Data models work correctly")
        print(f"   - Customer: Debtor ID {sample_order.customer_details.debtor_id}")
        print(f"   - Order lines: {len(sample_order.order_lines)}")
        print(f"   - Ship via: {sample_order.X_SHIPVIA}")
        
    except Exception as e:
        print(f"❌ Data model test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("EMAIL ORDER PROCESSOR - SYSTEM TEST")
    print("=" * 60)
    
    # Run tests
    tests = [
        test_configuration,
        test_services_initialization,
        test_data_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        print("\\n📝 Next steps:")
        print("   1. Ensure your .env file is configured")
        print("   2. Place credentials.json in the project directory")
        print("   3. Run: python main_processor.py")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        print("\\n🔧 Check:")
        print("   1. .env file exists and is properly configured")
        print("   2. All required dependencies are installed")
        print("   3. API credentials are valid")

if __name__ == "__main__":
    main()
