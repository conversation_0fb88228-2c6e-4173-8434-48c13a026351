"""
Runtime configuration and command-line argument support for email processing.
Provides flexible ways to specify labels and processing options at runtime.
"""

import os
import sys
import argparse
import logging
from typing import List, Optional, Dict, Any

logger = logging.getLogger(__name__)


class RuntimeConfig:
    """Runtime configuration manager for email processing."""
    
    def __init__(self):
        """Initialize runtime configuration."""
        self.args = None
        self._parse_arguments()
    
    def _parse_arguments(self):
        """Parse command-line arguments."""
        parser = argparse.ArgumentParser(
            description="Email Order Processor with Dynamic Label Selection",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  python main_processor.py                           # Interactive label selection
  python main_processor.py --labels Brady,RSEA      # Process specific labels
  python main_processor.py --labels all             # Process all available labels
  python main_processor.py --non-interactive        # Use environment/default labels
  python main_processor.py --brady-only             # Brady-only mode (backward compatibility)
  python main_processor.py --max-results 50         # Override email limit to 50 for all labels
  python main_processor.py --show-labels            # Show available labels and exit
  python main_processor.py --validate-config        # Validate configuration and exit

Environment Variables:
  MAX_GMAIL_RESULTS_OVERRIDE=50                     # Global email limit override
  MAX_GMAIL_RESULTS_BRADY=200                       # Brady-specific email limit
  RUNTIME_LABELS_TO_PROCESS=Brady,RSEA              # Labels to process
            """
        )
        
        # Label selection options
        label_group = parser.add_mutually_exclusive_group()
        label_group.add_argument(
            '--labels', '-l',
            type=str,
            help='Comma-separated list of labels to process, or "all" for all labels'
        )
        label_group.add_argument(
            '--brady-only',
            action='store_true',
            help='Process only Brady label (backward compatibility)'
        )
        
        # Processing mode options
        parser.add_argument(
            '--non-interactive', '-n',
            action='store_true',
            help='Run in non-interactive mode (use environment variables or defaults)'
        )
        
        # Information options
        info_group = parser.add_mutually_exclusive_group()
        info_group.add_argument(
            '--show-labels',
            action='store_true',
            help='Show available labels and exit'
        )
        info_group.add_argument(
            '--validate-config',
            action='store_true',
            help='Validate configuration and exit'
        )
        
        # Processing options
        parser.add_argument(
            '--max-results',
            type=int,
            metavar='N',
            help='Override maximum results per label (1-1000, default varies by label)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually processing'
        )
        
        # Logging options
        parser.add_argument(
            '--verbose', '-v',
            action='store_true',
            help='Enable verbose logging'
        )
        parser.add_argument(
            '--quiet', '-q',
            action='store_true',
            help='Suppress non-error output'
        )
        
        self.args = parser.parse_args()
    
    def get_processing_labels(self) -> Optional[List[str]]:
        """
        Get labels to process from command-line arguments.
        
        Returns:
            List of label names, or None if not specified via command line.
        """
        if self.args.brady_only:
            return ['Brady']
        
        if self.args.labels:
            if self.args.labels.lower() == 'all':
                return 'all'  # Special marker for all labels
            else:
                return [label.strip() for label in self.args.labels.split(',') if label.strip()]
        
        return None
    
    def is_interactive_mode(self) -> bool:
        """Check if interactive mode should be used."""
        return not self.args.non_interactive
    
    def is_dry_run(self) -> bool:
        """Check if this is a dry run."""
        return self.args.dry_run
    
    def should_show_labels(self) -> bool:
        """Check if we should show labels and exit."""
        return self.args.show_labels
    
    def should_validate_config(self) -> bool:
        """Check if we should validate config and exit."""
        return self.args.validate_config
    
    def get_max_results_override(self) -> Optional[int]:
        """Get max results override from command line."""
        return self.args.max_results
    
    def is_verbose(self) -> bool:
        """Check if verbose logging is enabled."""
        return self.args.verbose
    
    def is_quiet(self) -> bool:
        """Check if quiet mode is enabled."""
        return self.args.quiet
    
    def get_runtime_labels_from_env(self) -> List[str]:
        """Get labels from environment variable override."""
        runtime_labels = os.getenv("RUNTIME_LABELS_TO_PROCESS")
        if runtime_labels:
            labels = [label.strip() for label in runtime_labels.split(",") if label.strip()]
            logger.info(f"Using runtime labels from environment: {labels}")
            return labels
        return []
    
    def get_effective_labels(self) -> Optional[List[str]]:
        """
        Get the effective labels to process, considering all sources.
        
        Priority order:
        1. Command-line arguments
        2. Environment variables
        3. None (will trigger interactive selection or defaults)
        
        Returns:
            List of label names, 'all' for all labels, or None for interactive/default.
        """
        # First check command-line arguments
        cmd_labels = self.get_processing_labels()
        if cmd_labels is not None:
            return cmd_labels
        
        # Then check environment variables
        env_labels = self.get_runtime_labels_from_env()
        if env_labels:
            return env_labels
        
        # No explicit labels specified
        return None
    
    def configure_logging(self):
        """Configure logging based on runtime options."""
        if self.is_verbose():
            logging.getLogger().setLevel(logging.DEBUG)
            logger.info("Verbose logging enabled")
        elif self.is_quiet():
            logging.getLogger().setLevel(logging.ERROR)
    
    def print_configuration_summary(self):
        """Print a summary of the current configuration."""
        if self.is_quiet():
            return

        print("\n🔧 Configuration Summary:")
        print("=" * 40)

        effective_labels = self.get_effective_labels()
        if effective_labels == 'all':
            print("📧 Labels: All available labels")
        elif effective_labels:
            print(f"📧 Labels: {', '.join(effective_labels)}")
        else:
            mode = "Interactive" if self.is_interactive_mode() else "Default (Brady)"
            print(f"📧 Labels: {mode} selection")

        print(f"🔄 Mode: {'Non-interactive' if not self.is_interactive_mode() else 'Interactive'}")
        print(f"🧪 Dry Run: {'Yes' if self.is_dry_run() else 'No'}")

        # Show email limit configuration
        max_override = self.get_max_results_override()
        if max_override:
            print(f"📊 Global Email Limit Override: {max_override} emails (command-line)")

        # Check for environment variable overrides
        import os
        env_override = os.getenv("MAX_GMAIL_RESULTS_OVERRIDE")
        if env_override and not max_override:
            print(f"📊 Global Email Limit Override: {env_override} emails (environment)")

        # Show per-label environment overrides
        per_label_overrides = []
        for env_var in os.environ:
            if env_var.startswith("MAX_GMAIL_RESULTS_") and env_var != "MAX_GMAIL_RESULTS_OVERRIDE":
                label_name = env_var.replace("MAX_GMAIL_RESULTS_", "").lower()
                per_label_overrides.append(f"{label_name}={os.environ[env_var]}")

        if per_label_overrides:
            print(f"📊 Per-Label Limit Overrides: {', '.join(per_label_overrides)}")

        print()


def get_runtime_config() -> RuntimeConfig:
    """Get the runtime configuration instance."""
    return RuntimeConfig()


def show_available_labels():
    """Show available labels and exit."""
    try:
        from gmail_service import GmailService
        from label_selector import LabelSelector
        
        print("\n📧 Available Gmail Labels:")
        print("=" * 50)
        
        gmail_service = GmailService()
        selector = LabelSelector(gmail_service)
        
        # Show processing-ready labels
        processing_labels = gmail_service.get_available_processing_labels()
        if processing_labels:
            print("\n🔄 Labels with PDF attachments (processing-ready):")
            for i, label in enumerate(processing_labels, 1):
                print(f"  {i:2d}. {label}")
        
        # Show all user labels
        all_labels = gmail_service.get_user_labels()
        other_labels = [label for label in all_labels if label not in processing_labels]
        if other_labels:
            print("\n📁 Other user labels:")
            for i, label in enumerate(other_labels, 1):
                print(f"  {i:2d}. {label}")
        
        if not all_labels:
            print("❌ No user-created labels found.")
        
        print(f"\nTotal: {len(processing_labels)} processing-ready, {len(other_labels)} other labels")
        
    except Exception as e:
        print(f"❌ Error retrieving labels: {e}")
        sys.exit(1)


def validate_configuration():
    """Validate configuration and exit."""
    try:
        from config import config
        
        print("\n🔍 Validating Configuration:")
        print("=" * 40)
        
        # Validate config
        config.validate_config()
        print("✅ Configuration is valid")
        
        # Test Gmail connection
        from gmail_service import GmailService
        gmail_service = GmailService()
        labels = gmail_service.get_user_labels()
        print(f"✅ Gmail connection successful ({len(labels)} user labels found)")
        
        # Test query builder
        from query_builder import GmailQueryBuilder
        builder = GmailQueryBuilder()
        query, max_results = builder.build_label_query('Brady')
        print(f"✅ Query builder working (Brady query: {query})")
        
        print("\n🎉 All systems ready!")
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Test the runtime config
    config = get_runtime_config()
    config.print_configuration_summary()
    
    if config.should_show_labels():
        show_available_labels()
    elif config.should_validate_config():
        validate_configuration()
    else:
        print("Runtime configuration loaded successfully.")
