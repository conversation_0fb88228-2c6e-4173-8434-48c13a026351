# Comprehensive Email Processing System

A sophisticated, AI-powered email processing system with two specialized agents for automated business email handling, order extraction, and intelligent routing.

## 🚀 System Overview

This comprehensive system consists of two main AI agents:

1. **Enhanced Universal Agent** - Monitors expanded label categories and extracts orders from all purchase orders
2. **Continuous Polling Agent** - Real-time Gmail monitoring with intelligent prioritization and processing

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Gmail Integration Layer                   │
├─────────────────────────────────────────────────────────────┤
│  Gmail API │ Push Notifications │ Real-time Queue │ Labels  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Comprehensive Email System                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────┐    ┌─────────────────────────────┐ │
│  │ Enhanced Universal  │    │ Continuous Polling Agent    │ │
│  │ Agent              │    │                             │ │
│  │ • Label Discovery   │    │ • Real-time Monitoring     │ │
│  │ • Smart Filtering   │    │ • Priority Queue           │ │
│  │ • Universal Extract │    │ • Error Handling           │ │
│  │ • Multi-format     │    │ • Retry Logic              │ │
│  └─────────────────────┘    └─────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │               Email Dashboard                           │ │
│  │ • Web Interface • Statistics • Manual Processing       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Data Management                          │
├─────────────────────────────────────────────────────────────┤
│  SQLite DBs │ Audit Logs │ Status Tracking │ Health Monitor │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Output Systems                           │
├─────────────────────────────────────────────────────────────┤
│  MYOB Integration │ Markdown Files │ JSON Payloads │ Alerts │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Key Features

### Enhanced Universal Agent
- **Dynamic Label Discovery**: Automatically discovers and monitors all Gmail labels
- **Smart Content Filtering**: AI-powered categorization and confidence scoring
- **Universal Order Extraction**: Works with any supplier format, not limited to specific customers
- **Multi-format Support**: PDF, Excel, Word, and image processing capabilities
- **Configurable Rules**: YAML-based processing rules and patterns
- **Intelligent Routing**: Content-based email classification and routing

### Continuous Polling Agent
- **Real-time Monitoring**: Continuous Gmail API polling with intelligent intervals
- **Priority-based Processing**: 5-level priority system (Critical, High, Medium, Low, Bulk)
- **Concurrent Processing**: Multi-threaded worker pool for parallel email processing
- **Comprehensive Error Handling**: Retry logic with exponential backoff
- **Rate Limit Management**: API quota management and optimization
- **Audit Logging**: Complete processing history and performance metrics

### Dashboard & Management
- **Web-based Dashboard**: Real-time email management interface
- **Processing Statistics**: Performance metrics and analytics
- **Manual Processing**: On-demand email processing capabilities
- **Health Monitoring**: System component health checks
- **Backup & Recovery**: Automated system backups

## 📦 Installation

1. **Install Dependencies**:
```bash
pip install -r requirements_comprehensive.txt
```

2. **Setup Gmail API Credentials**:
   - Create a project in Google Cloud Console
   - Enable Gmail API
   - Download `credentials.json`
   - Place in project root

3. **Configure Environment**:
```bash
# Copy example configuration
cp .env.example .env

# Edit configuration
GEMINI_API_KEY=your_gemini_api_key
GMAIL_CREDENTIALS_FILE=credentials.json
MYOB_API_SETTINGS=your_myob_settings
```

4. **Initialize System**:
```bash
# First run will create default configurations
python comprehensive_email_system.py --status
```

## 🚀 Quick Start

### Start the Complete System
```bash
# Start all components (default)
python comprehensive_email_system.py

# Or specify configuration
python comprehensive_email_system.py --config system_config.json
```

### Individual Components

**Enhanced Universal Agent Only**:
```bash
python enhanced_universal_agent.py
```

**Continuous Polling Agent Only**:
```bash
python continuous_polling_agent.py
```

**Dashboard Only**:
```bash
python email_dashboard.py
```

### Manual Operations
```bash
# Run manual processing
python comprehensive_email_system.py --manual

# Check system status
python comprehensive_email_system.py --status

# Create system backup
python comprehensive_email_system.py --backup
```

## ⚙️ Configuration

### System Configuration (`system_config.json`)
```json
{
  "enable_universal_agent": true,
  "enable_continuous_polling": true,
  "enable_dashboard": true,
  "dashboard_port": 5000,
  "universal_agent_schedule": "hourly",
  "system_health_check_interval": 300,
  "backup_interval_hours": 24,
  "log_level": "INFO"
}
```

### Universal Agent Configuration (`universal_agent_config.yaml`)
```yaml
monitor_all_labels: true
custom_label_patterns:
  - "*Purchase*"
  - "*Order*"
  - "*Invoice*"
excluded_labels:
  - "Spam"
  - "Trash"
  - "Drafts"
time_window_hours: 168
enable_smart_filtering: true
```

### Polling Agent Configuration (`polling_config.json`)
```json
{
  "polling_interval_seconds": 30,
  "max_workers": 4,
  "max_retry_attempts": 3,
  "rate_limit_delay": 1,
  "batch_size": 10,
  "high_priority_threshold": 0.8,
  "audit_retention_days": 30
}
```

### Processing Rules (`processing_rules.yaml`)
```yaml
rules:
  - name: "Purchase Orders"
    label_patterns: ["*Purchase*", "*Order*", "*PO*"]
    include_patterns: ["purchase order", "PO#", "order #"]
    exclude_patterns: ["packing slip", "receipt"]
    priority: 1
    enabled: true
```

## 🔧 Advanced Features

### Smart Email Categorization
The system uses AI to categorize emails into:
- `purchase_order` - Business purchase orders
- `invoice` - Payment invoices
- `packing_slip` - Shipping notifications
- `quote` - Price quotations
- `general_business` - Other business emails
- `spam` - Unwanted emails

### Priority Processing
- **Critical (1)**: High-confidence purchase orders, urgent business emails
- **High (2)**: Standard purchase orders, important invoices
- **Medium (3)**: General business correspondence
- **Low (4)**: Notifications, receipts
- **Bulk (5)**: Marketing, newsletters

### Error Handling & Recovery
- **Automatic Retry**: Failed emails automatically retry with exponential backoff
- **Circuit Breaker**: Prevents cascade failures during API outages
- **Graceful Degradation**: System continues operating with reduced functionality
- **Health Monitoring**: Continuous component health assessment

### Audit & Compliance
- **Complete Audit Trail**: Every processing attempt logged
- **Performance Metrics**: Processing times, success rates, queue statistics
- **Data Retention**: Configurable retention policies
- **Backup & Recovery**: Automated system state backups

## 📊 Dashboard Features

Access the web dashboard at `http://localhost:5000` for:

- **Real-time Email List**: Live view of processed emails
- **Processing Statistics**: System performance metrics
- **Manual Processing**: Process individual emails or batches
- **Configuration Management**: Update processing rules
- **System Health**: Component status monitoring
- **File Downloads**: Access generated markdown and MYOB files

## 🔄 Processing Workflow

1. **Email Discovery**: System discovers emails from all configured labels
2. **AI Categorization**: Each email analyzed and categorized with confidence score
3. **Smart Filtering**: Low-confidence and excluded emails filtered out
4. **Priority Assignment**: Emails assigned priority based on category and content
5. **Queue Processing**: Emails processed by priority in worker threads
6. **Data Extraction**: AI extracts structured business data
7. **File Generation**: Creates markdown summaries and MYOB JSON payloads
8. **Status Updates**: Gmail labels updated with processing status
9. **Audit Logging**: Complete processing history recorded

## 🛠️ Monitoring & Maintenance

### System Health Checks
- Gmail API connectivity
- Database integrity
- Component availability
- Processing queue status
- Error rates and patterns

### Performance Monitoring
```bash
# View system statistics
python continuous_polling_agent.py --stats

# Check processing status
python comprehensive_email_system.py --status

# Monitor logs
tail -f logs/system.log
```

### Maintenance Operations
```bash
# Create manual backup
python comprehensive_email_system.py --backup

# Clean old audit data (automatic)
# Configurable retention in polling_config.json

# Update processing rules
# Edit processing_rules.yaml and restart system
```

## 🚨 Troubleshooting

### Common Issues

**Gmail API Errors**:
- Check credentials.json file
- Verify Gmail API is enabled
- Ensure proper OAuth scopes

**Processing Failures**:
- Check Gemini API key
- Verify network connectivity
- Review error logs in dashboard

**Performance Issues**:
- Adjust worker count in polling config
- Increase polling interval
- Check system resources

### Log Locations
- System logs: `comprehensive_system.db`
- Polling logs: `continuous_polling.db`
- Dashboard logs: `email_dashboard.db`
- Processing files: `markdown/` and `myob/` directories

## 🔧 Customization

### Adding New Email Types
1. Update `processing_rules.yaml` with new patterns
2. Modify categorization logic in `enhanced_universal_agent.py`
3. Add extraction rules for new data formats

### Custom Integrations
The system provides hooks for:
- Custom data extraction methods
- Additional output formats
- Third-party API integrations
- Custom notification systems

### Scaling Considerations
- Increase worker threads for higher throughput
- Implement Redis for distributed queuing
- Add load balancing for multiple instances
- Consider database optimization for large volumes

## 📚 API Reference

### System Control
```python
from comprehensive_email_system import ComprehensiveEmailSystem

system = ComprehensiveEmailSystem()
system.start()  # Start all components
system.stop()   # Graceful shutdown
status = system.get_system_status()  # Get current status
```

### Manual Processing
```python
from enhanced_universal_agent import EnhancedUniversalAgent

agent = EnhancedUniversalAgent()
processed_orders = agent.run_universal_processing()
```

### Dashboard Integration
```python
from email_dashboard import EmailDashboard

dashboard = EmailDashboard()
dashboard.run(port=5000)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Review system logs
- Check dashboard health monitoring
- Submit issues with detailed error logs
