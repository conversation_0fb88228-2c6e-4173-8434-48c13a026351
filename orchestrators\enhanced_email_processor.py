#!/usr/bin/env python3
"""
Enhanced Email Processor with Dashboard Integration.
"""
import logging
import sys
import os
from datetime import datetime
from typing import List

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from orchestrators.main_processor import EmailOrderProcessor, send_completion_email
from orchestrators.email_dashboard import EmailDashboard
from services.gmail_service import GmailService

logger = logging.getLogger(__name__)

class EnhancedEmailProcessor:
    """Enhanced email processor with dashboard integration."""
    
    def __init__(self):
        self.processor = EmailOrderProcessor()
        self.dashboard = EmailDashboard()
        self.gmail_service = GmailService()
        
    def process_all_emails(self, update_dashboard=True):
        """Process all emails and optionally update dashboard."""
        print(f"\n{'='*80}")
        print("📧 ENHANCED EMAIL PROCESSING WITH DASHBOARD INTEGRATION")
        print(f"{'='*80}")
        
        try:
            # Run the standard processing workflow
            print("🔄 Processing emails using existing workflow...")
            processed_orders = self.processor.run_extraction_workflow()
            
            if update_dashboard:
                print("📊 Updating dashboard database...")
                self._update_dashboard_from_processing(processed_orders)
            
            # Send completion email with enhanced information
            self._send_enhanced_completion_email(processed_orders)
            
            print(f"\n✅ Processing completed successfully!")
            print(f"📧 Processed {len(processed_orders)} emails")
            print(f"📊 Dashboard updated with latest status")
            print(f"🌐 View dashboard at: http://localhost:5000")
            
            return processed_orders
            
        except Exception as e:
            logger.error(f"Enhanced processing failed: {e}")
            print(f"❌ Processing failed: {e}")
            return []
    
    def _update_dashboard_from_processing(self, processed_orders):
        """Update dashboard database with processing results."""
        try:
            # Sync latest emails first
            self.dashboard._sync_emails_from_gmail()
            
            # Update status for processed emails
            for order in processed_orders:
                self.dashboard._update_email_status(
                    email_id=order.email_id,
                    status='processed',
                    markdown_file=getattr(order, 'markdown_filepath', None),
                    myob_file=getattr(order, 'myob_filepath', None)
                )
            
            logger.info(f"Updated dashboard for {len(processed_orders)} processed emails")
            
        except Exception as e:
            logger.error(f"Failed to update dashboard: {e}")
    
    def _send_enhanced_completion_email(self, processed_orders):
        """Send enhanced completion email with dashboard link."""
        try:
            current_time = datetime.now().strftime("%B %d, %Y at %I:%M %p")
            
            # Get dashboard stats
            stats = self.dashboard._get_processing_stats()
            
            enhanced_body = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Enhanced Email Processing Complete</title>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        background-color: #f5f5f5;
                        margin: 0;
                        padding: 20px;
                    }}
                    .container {{
                        max-width: 800px;
                        margin: 0 auto;
                        background-color: white;
                        border-radius: 10px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        overflow: hidden;
                    }}
                    .header {{
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }}
                    .dashboard-link {{
                        background-color: rgba(255, 255, 255, 0.2);
                        color: white;
                        padding: 15px 25px;
                        text-decoration: none;
                        border-radius: 25px;
                        display: inline-block;
                        margin-top: 15px;
                        font-weight: bold;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        transition: all 0.3s ease;
                    }}
                    .dashboard-link:hover {{
                        background-color: rgba(255, 255, 255, 0.3);
                        border-color: rgba(255, 255, 255, 0.5);
                    }}
                    .stats-grid {{
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                        gap: 20px;
                        padding: 30px;
                        background-color: #f8f9fa;
                    }}
                    .stat-card {{
                        background: white;
                        padding: 20px;
                        border-radius: 8px;
                        text-align: center;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        border-left: 4px solid;
                    }}
                    .stat-card.total {{ border-color: #6c757d; }}
                    .stat-card.processed {{ border-color: #28a745; }}
                    .stat-card.review {{ border-color: #fd7e14; }}
                    .stat-card.failed {{ border-color: #dc3545; }}
                    .stat-card.unprocessed {{ border-color: #007bff; }}
                    .stat-number {{
                        font-size: 2em;
                        font-weight: bold;
                        color: #333;
                        display: block;
                    }}
                    .stat-label {{
                        color: #666;
                        font-size: 0.9em;
                        margin-top: 5px;
                    }}
                    .content {{
                        padding: 30px;
                    }}
                    .feature-list {{
                        list-style: none;
                        padding: 0;
                    }}
                    .feature-list li {{
                        padding: 8px 0;
                        border-bottom: 1px solid #eee;
                    }}
                    .feature-list li:before {{
                        content: "✓";
                        color: #28a745;
                        font-weight: bold;
                        margin-right: 10px;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🚀 Enhanced Email Processing Complete</h1>
                        <p>TeamsysV0.1 with Dashboard Integration</p>
                        <p>Completed on {current_time}</p>
                        <a href="http://localhost:5000" class="dashboard-link">
                            📊 Open Email Dashboard
                        </a>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card total">
                            <span class="stat-number">{stats['overall']['total']}</span>
                            <div class="stat-label">Total Emails</div>
                        </div>
                        <div class="stat-card processed">
                            <span class="stat-number">{stats['overall']['processed']}</span>
                            <div class="stat-label">Processed</div>
                        </div>
                        <div class="stat-card review">
                            <span class="stat-number">{stats['overall']['review']}</span>
                            <div class="stat-label">Review Needed</div>
                        </div>
                        <div class="stat-card failed">
                            <span class="stat-number">{stats['overall']['failed']}</span>
                            <div class="stat-label">Failed</div>
                        </div>
                        <div class="stat-card unprocessed">
                            <span class="stat-number">{stats['overall']['unprocessed']}</span>
                            <div class="stat-label">Unprocessed</div>
                        </div>
                    </div>
                    
                    <div class="content">
                        <h3>📈 This Processing Run</h3>
                        <p><strong>{len(processed_orders)} emails</strong> were processed in this run.</p>
                        
                        <h3>🌟 Dashboard Features Available</h3>
                        <ul class="feature-list">
                            <li>Real-time email sync from Gmail</li>
                            <li>Advanced filtering and search capabilities</li>
                            <li>Traffic light status system (🟢 🟡 🔴)</li>
                            <li>Batch email processing</li>
                            <li>Processing statistics and analytics</li>
                            <li>File download for markdown and MYOB outputs</li>
                            <li>Manual status updates and email management</li>
                        </ul>
                        
                        <h3>🔗 Quick Actions</h3>
                        <p>
                            <a href="http://localhost:5000" style="color: #667eea; text-decoration: none;">
                                📊 <strong>Open Dashboard</strong>
                            </a> - Access the full email management interface
                        </p>
                        
                        <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin-top: 20px;">
                            <p><strong>💡 Pro Tip:</strong> Use the dashboard to:</p>
                            <ul style="margin: 10px 0;">
                                <li>Monitor email processing status in real-time</li>
                                <li>Process individual emails or batches</li>
                                <li>Download generated files directly</li>
                                <li>View detailed email content and attachments</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            
            send_completion_email(
                subject=f"🚀 Enhanced Email Processing Complete - {len(processed_orders)} Emails Processed",
                body=enhanced_body
            )
            
        except Exception as e:
            logger.error(f"Failed to send enhanced completion email: {e}")

def main():
    """Main function for enhanced processing."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced Email Processor with Dashboard')
    parser.add_argument('--no-dashboard-update', action='store_true', 
                        help='Skip dashboard database update')
    parser.add_argument('--start-dashboard', action='store_true',
                        help='Start dashboard server after processing')
    
    args = parser.parse_args()
    
    try:
        processor = EnhancedEmailProcessor()
        
        # Process emails
        processed_orders = processor.process_all_emails(
            update_dashboard=not args.no_dashboard_update
        )
        
        # Optionally start dashboard
        if args.start_dashboard:
            print(f"\n🌐 Starting dashboard server...")
            processor.dashboard.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print(f"\n⚠️  Processing interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()