# TeamsysV0.1 Modernization TODO

## 1. Package Updates
- [x] Update all packages in .venv using PowerShell command:
  ```powershell
  pip list --outdated --format=json | ConvertFrom-Json | ForEach-Object { pip install --upgrade $_.name }
  ```

## 2. Google Gen AI SDK Migration (2025)
- [x] Install latest Google Gen AI SDK (GA May 2025)
- [x] Update imports to use new google-genai SDK
- [x] Replace current Gemini implementation with OpenAI-compatible version
- [x] Update to use gemini-2.5-flash model

## 3. File Refactoring for OpenAI-Compatible Gemini
- [x] **main_processor.py** - Update LLM service integration
- [x] **config.py** - Update configuration for new SDK
- [x] **llm_service.py** - Complete refactor to OpenAI-compatible Gemini
- [ ] **gmail_service.py** - Update any LLM integrations
- [ ] **models.py** - Update data models if needed
- [ ] **memory_client.py** - Update memory integration

## 4. Bug Fixes
- [x] Fix ProcessedOrder validation errors:
  - extracted_data should be valid dictionary or ExtractedOrder instance
  - myob_payload should be valid dictionary
- [x] Handle None values in LLM responses properly
- [x] Add validation for extracted data before creating ProcessedOrder

## 5. Business Logic Updates
- [x] Add BRADY email filtering: Skip emails with subject "Packing List for Purchase Order" 
- [x] Ensure SKU mapping: EQLB8012 → TSSU-ORA
- [x] Validate customer mapping and debtor IDs

## 6. Testing & Validation
- [x] Test package updates don't break existing functionality
- [x] Verify new Google Gen AI SDK integration
- [ ] Test order processing with various email formats (ready for testing)
- [ ] Validate MYOB payload generation (ready for testing)

## Priority Order
1. Package updates (foundation)
2. SDK migration (core functionality)
3. Bug fixes (stability)
4. Business logic updates (features)
5. Testing (validation)