#!/usr/bin/env python3
"""
Simple launcher for the Email Dashboard.
"""
import sys
import os
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    print("\n" + "="*80)
    print("📧 TEAMSYS EMAIL DASHBOARD")
    print("="*80)
    print("🚀 Starting enhanced email management interface...")
    print("📊 Features:")
    print("   • Real-time email sync from Gmail")
    print("   • Traffic light status system (🟢 🟡 🔴)")
    print("   • Advanced filtering and search")
    print("   • Batch email processing")
    print("   • Processing statistics and analytics")
    print("   • File download capabilities")
    print("="*80)
    
    try:
        from email_dashboard import EmailDashboard
        dashboard = EmailDashboard()
        dashboard.run(host='0.0.0.0', port=8050, debug=False)
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()