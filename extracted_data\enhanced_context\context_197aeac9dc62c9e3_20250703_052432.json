{"email": {"id": "197aeac9dc62c9e3", "headers": {"Delivered-To": "<EMAIL>", "Received": "from RSEAMELAP13 (*************) by ML1PEPF0000F178.mail.protection.outlook.com (*************) with Microsoft SMTP Server id 15.20.8880.14 via Frontend Transport; Thu, 26 Jun 2025 23:57:04 +0000", "X-Forwarded-Encrypted": "i=3; AJvYcCXy887j4Z1qseVHnTyfkL6lfX5ms/VaGEZG4QOJ/j8wo+brFzkq0QvztpGN3ZnUp0gllZme1g==@teamsystems.net.au", "X-Google-Smtp-Source": "AGHT+IHYAE5E0gfiaTZpc/j7OOyANAy5OpwHUEGprpMWgV81W5VNkpisdnyFUNVro8ow6EUhmPI/", "X-Received": "by 2002:a05:6902:2701:b0:e82:3c9d:e654 with SMTP id 3f1490d57ef6-e87a7b662c8mr1826686276.23.1750982234897;        <PERSON><PERSON>, 26 Jun 2025 16:57:14 -0700 (PDT)", "ARC-Seal": "i=1; a=rsa-sha256; s=arcselector10001; d=microsoft.com; cv=none; b=FZM3qP+LaLAYW09uIAyFPFSPRJR8vCaVP/xWdhs9hnQ5Kea4H2DzfxmwMgfBQfL2K2F7OJbojYHwu1a1QLRtnchWGEMzM7ItW9j7oY5O1oVg95tPNmL9KMNdCLy2pfm38mGaA4yJgs6dQ5fgu/WYhfIjOSBvlF9mjM26b31ciG+03z3NZtuXpSt/YmOHGtTpixQOFceRbgK0Fs+QQS5+3JsLZsXV+WrWBHTJuOvK9FR/qx6EK49B2PUIYjhpTUzUp1UNIpA09rRnJzusqDxp5/s5VK1YBCVvYlBI/7FYD4tHH2GwnEfZr/IUH7jVpOvZ/+/Gmu5wxOuc7LCbVezayw==", "ARC-Message-Signature": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com; s=arcselector10001; h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-AntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Exchange-AntiSpam-MessageData-1; bh=A/CSnZpIJc8n9bB+lxauDxIeH9lfMu2aqzhj4nDpfdk=; b=FJRacht7BDx2wsepI4aLwlk7DnsucdssB5j9TogCRfE0R0k5Ub3JY7NqjcMfuHFjRAqLlu6vOqFBtrNzXQzd2yHrGKFbIM6wl6y4auYjT9T662zrqBRuc+o9l3VIB5NLW70nXvpOt3sXMFwEMsYt6gfekYUMU3WNFkYTj281LF2qXCVBcm6ZL0aOLiV3po7/a6gg8lNzpgTM9CRVpnX1lHym2X39y2+A3OyYjkv/B9TwtWWeo1DT63QGLUGiIQWpQhkug50nx9eibdAp0IXkc1hAQvSI6dwWiLeBYNNT56uhwsZY+P5tSPGY4lVzB1LoUOzI9quUuJA0ZiFJIPOiBg==", "ARC-Authentication-Results": "i=1; mx.microsoft.com 1; spf=softfail (sender ip is *************) smtp.rcpttodomain=teamsystems.net.au smtp.mailfrom=rsea.com.au; dmarc=fail (p=none sp=none pct=100) action=none header.from=rsea.com.au; dkim=none (message not signed); arc=none (0)", "Return-Path": "<<EMAIL>>", "Received-SPF": "SoftFail (protection.outlook.com: domain of transitioning rsea.com.au discourages use of ************* as permitted sender)", "Authentication-Results": "mx.google.com;       dkim=pass header.i=@teamsystems-net-au.20230601.gappssmtp.com header.s=20230601 header.b=WAxKW6Z0;       arc=pass (i=3 spf=pass spfdomain=rsea.com.au dkim=pass dkdomain=rsea.com.au dmarc=pass fromdomain=rsea.com.au);       spf=pass (google.com: <NAME_EMAIL> designates 2607:f8b0:4864:20::b46 as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=fail (p=NONE sp=NONE dis=NONE arc=pass) header.from=rsea.com.au;       dara=fail header.i=@teamsystems.net.au", "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=teamsystems-net-au.20230601.gappssmtp.com; s=20230601; t=1750982237; x=1751587037; darn=teamsystems.net.au;        h=list-unsubscribe:list-archive:list-help:list-post:list-id         :mailing-list:precedence:x-original-authentication-results         :x-original-sender:message-id:subject:date:to:from:mime-version:from         :to:cc:subject:date:message-id:reply-to;        bh=A/CSnZpIJc8n9bB+lxauDxIeH9lfMu2aqzhj4nDpfdk=;        b=WAxKW6Z0DfLclqslOrZnCv0PFww3J2o+KRi9KS3fCGobL9oO2WVRjLvfRVSUKJcUzV         ph3za50FRNEQOU6RtFuCeByV06Uk95Z5aPdBl0qqo8ksOowWNaJxcDTKWnXc3MizMFYX         LYqsLzcGKjcMx90vBznzXxbqMt84cgXx1ifC2+VLLd5Lsz5gCQ0LP/f0ca6BE+KkZiej         caySMsVb9EBm2k3A52PGEdpEeKS7j/tzKWJ3NaIiHJMv7TOOtkRiHq7mAd248Dlh7oTS         xWZUyhtxl0kpGn0Vn/aAQBPyz7DwKlqsY5hqAfNcOQqaj52ZHmO6fgqkytxieB3ugXyT         SxHw==", "X-Google-DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20230601; t=1750982237; x=1751587037;        h=list-unsubscribe:list-archive:list-help:list-post         :x-spam-checked-in-group:list-id:mailing-list:precedence         :x-original-authentication-results:x-original-sender:message-id         :subject:date:to:from:mime-version:x-beenthere:x-gm-message-state         :from:to:cc:subject:date:message-id:reply-to;        bh=A/CSnZpIJc8n9bB+lxauDxIeH9lfMu2aqzhj4nDpfdk=;        b=DR60t0ZzdiWaF0BudhX14OrGHKooepdLesGhM56U6yEqgR+MFTPb/TwzO8mEf3rC3j         GdYFUp2uvufuLHN3Yjo1PS0Jo6kCsJhlG7D1DKlZKIQ1ajxkzr8QsbDRfuTzCaco2Czl         4wY+W1u/lW13BZMUsyj0JQdEZOxJrUcUiUI+3ql1kmGnp2MFs5VMIsM74NFeAT37LjnD         /MtI8BUu8wKAmwk6VPKqpvi9jnc7n/sIMVUfnEzG11N/KO1MUSwpMvwtXu7lPRfzkKer         o1ih84GSq+eQH6tBjj/Uq4DuT2HnP3xJ8NafBFkBJmp+t8GV8wiBmYk37V4Ob+OUU3vL         4J8w==", "X-Gm-Message-State": "AOJu0YxYQx2w12d1yXx4Iv08hiE63kTUo41iYPBZgrv8xA9ac4BlCyIa 4BClQb0D2vOTes+MXerTtuPn5OQoXmNz/1vUSbmw/5kzKbj64DKZh0LNyLAwKPpzbugmTg==", "X-BeenThere": "<EMAIL>; h=AZMbMZd8npQWtQ4L0zKmy+B76TprYC8GozIb4nu8nzy0IJdW9Q==", "X-MS-Exchange-Authentication-Results": "spf=softfail (sender IP is *************) smtp.mailfrom=rsea.com.au; dkim=none (message not signed) header.d=none;dmarc=fail action=none header.from=rsea.com.au;", "MIME-Version": "1.0", "From": "<EMAIL>", "To": "<EMAIL>", "Date": "27 Jun 2025 09:57:04 +1000", "Subject": "PURCHASE ORDER - 1880782 for TEASYS from RSEA Safety Bunbury", "Content-Type": "multipart/mixed; boundary=--boundary_197_7ae93629-be10-495b-9c90-a5ad0864a1af", "Message-ID": "<<EMAIL>>", "X-EOPAttributedMessage": "0", "X-MS-PublicTrafficType": "Email", "X-MS-TrafficTypeDiagnostic": "ML1PEPF0000F178:EE_|SYBPR01MB5568:EE_", "X-MS-Office365-Filtering-Correlation-Id": "42b6cb1c-4571-4e04-c9f5-08ddb50d26e7", "X-MS-Exchange-SenderADCheck": "1", "X-MS-Exchange-AntiSpam-Relay": "0", "X-Microsoft-Antispam": "BCL:0;ARA:13230040|82310400026|36860700013|376014|1800799024|4053099003;", "X-Microsoft-Antispam-Message-Info": "cacNUGiF4BkK7grUGEfShbevOBp7QEDqsnVHDsRct8dRbudDo6iDBJ8zZ5idqdGLBOGf9EEV3soMZHh8rITDfOoLaaEHUtLVimCER8B+ws3dWodQnCVbSgsK9GSrjs7j1hQ5H/kCJ+XH/N3ZInOjBseYCvTEYf4WRDMXcTF7plgdtjiSxeGh6Jx5Sd60XV5hgCMYkHoMzR/7NF3qEZyXRlIRipCIDfP7TQPOQxsydUoi90ti8zR7wvrdlxfQbcnh60D1d8oTgcckO04tiTmkdzvtA2IezCkE+BJNNa2vGcdQyizWuoWrYSoam2+U33x82FhtwKIbBTNgZxW/tsFMUwX9tc+sXRTfXFUOGYeblW/uOo+3SfYmchGa09d00Fyl+CX8hLhkToZSCdy6ZN1JWDehRBc8iZ/Pc9WCIVbNYrQZqTrvbcXDlxOghpvyby1V5lQYIrWVcT1HWBMfPOraJ5m5tmfFycmcz0aZdfT2voCZ7qzqm1lVXF8XT5IvyeVs8lCi+oDpvaA74G3TJY4VDMjUCbgXpyxnMX/wLN05Q/scSlDn+rrT4AH5mdSucv59b0U8HeskUPi0pvLqk9AvC2uCsn6glV7TlPuFNM8FVBRk/PkvMqPlCP5lokC/2Ya9+yZTLMkgBS6WpUYP/8GPouRkP2T4NI6UW0nzwrZCAFfSlZGm7EBSaiIgF0IX1SlLasfbofNFnaItxlak/BodExHtMYSJR+bFYBn6x+YtHOclZ0prVac0cERMGU24xAH6sFrYcdyShNqjc4sVHH7vg9iIRgkrLwLAugQMfulr2p55Cdi7EOebraBrG+1da7L6fbenP5+EdYFOzT/uBZJNt+qTXm/cIELkPm2cpjde2lXxaDi/GiENs+tTq1Yw8GzJjPbSlH3LPn7Bz5xzv8COSlBUcitQMofwqzBqd6ARVntWPF8OAlRYfdF7nqUf/ONNAVlrYEhygS9IrxMjc+CIh+ji4mAKdl0vfkLL37VX3CMM3tcG1o/6e7q2kdrjozKpU12OkktD6se5SSyAXUy/xxVbF+te+db6kc9/0/2YDDh/VGNyLMS/ecVTyCaw5V+Ogysyv275xpBpNMcebZd6Sf5/C0UIxN2pF2ZA2uat62AT16rCwSVM5+9a2G8YJVc1GDVHOHc6Zr7rKBPtqgA9XURjPyIKaJkZdDp6IPPh/we0pTp1NPWtmA/WOOEFLKH4BajaAapXIl8OmczhbLCKhpTXnbYNb9SM9meq4z8MWG1pwb3oMT0dQCXjUd9sRBcHLVSJrD7WpHRVvndz4ayIhAyf9O+AjjpJDz9IhA6PvQCJ0cu3m/Do98v7TmQ71lB/CXBewKcMZ2KPZWvKKCG00rfAR3FpdfA9Ru/D1P7o2CGOxeSzMfZnuY0dnvvgNlNk4ViweWoc4C/jLLNdbyn1hN7jJfROc/c8aaUcy7LAHXJf12+A778UEE+nX6ItP+F4ui3nB56YPp8t8HDUI7dD5g==", "X-Forefront-Antispam-Report": "CIP:*************;CTRY:AU;LANG:en;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:RSEAMELAP13;PTR:InfoDomainNonexistent;CAT:NONE;SFS:(13230040)(82310400026)(36860700013)(376014)(1800799024)(4053099003);DIR:OUT;SFP:1102;", "X-OriginatorOrg": "rsea.com.au", "X-MS-Exchange-CrossTenant-OriginalArrivalTime": "26 Jun 2025 23:57:04.8287 (UTC)", "X-MS-Exchange-CrossTenant-Network-Message-Id": "42b6cb1c-4571-4e04-c9f5-08ddb50d26e7", "X-MS-Exchange-CrossTenant-Id": "e89d8ffd-f15f-4b37-8c28-6be445065af7", "X-MS-Exchange-CrossTenant-OriginalAttributedTenantConnectingIp": "TenantId=e89d8ffd-f15f-4b37-8c28-6be445065af7;Ip=[*************];Helo=[RSEAMELAP13]", "X-MS-Exchange-CrossTenant-AuthSource": "ML1PEPF0000F178.ausprd01.prod.outlook.com", "X-MS-Exchange-CrossTenant-AuthAs": "Anonymous", "X-MS-Exchange-CrossTenant-FromEntityHeader": "HybridOnPrem", "X-MS-Exchange-Transport-CrossTenantHeadersStamped": "SYBPR01MB5568", "X-Original-Sender": "<EMAIL>", "X-Original-Authentication-Results": "mx.google.com;       dkim=pass header.i=@rsea.com.au header.s=selector2 header.b=wcphJ8aX;       arc=pass (i=1);       spf=pass (google.com: <NAME_EMAIL> designates 2a01:111:f403:c40d::3 as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=rsea.com.au", "Precedence": "list", "Mailing-list": "list <EMAIL>; contact <EMAIL>", "List-ID": "<melbourne.teamsystems.net.au>", "X-Spam-Checked-In-Group": "<EMAIL>", "X-Google-Group-Id": "114953712296", "List-Post": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/post>, <mailto:<EMAIL>>", "List-Help": "<https://support.google.com/a/teamsystems.net.au/bin/topic.py?topic=25838>, <mailto:<EMAIL>>", "List-Archive": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/>", "List-Unsubscribe": "<mailto:<EMAIL>>, <https://groups.google.com/a/teamsystems.net.au/group/melbourne/subscribe>"}, "sender_display": "<EMAIL>", "sender_for_history": "<EMAIL>", "body": "", "attachments": [{"id": "ANGjdJ-zlnq89MzkXYvwfs5O4etQ_Nam37CIwBhpoP-DcmdvA5pI92YRSLK8dN_8QK7DJKhiqI2ujZXxcPw89Z2Zs7nUr71C7VOS6-kC6q8ETsBazV5eLFzFJT5N7J38-dfH4ZqmBmjT0OJamhNvFHsbxiJe5uapEiGUpANql8AV7UzfvrmFZ-YJe4KX7NDUAZtHf0Pl09ytXCdq37c5twyPxqKaV3RV21w_zgkDHFnNxbJLkms6Av_oLEv0k9KeVZoCSzGGMWsVnHrsfm-TVTaJBHpLDKBKNbeHYDVlxIZEELXyIpqyoEuIUI2B7s_ET0TapTo7mV_sgoIg0qE2eZJmDnR8El1THPtmtIW1ojEbb2O4CaCrl9OFseNbrDBW8mkQoxp2Wp5cqH2WUOfY", "filename": "PURCHASE ORDER-1880782-TEASYS.pdf", "mimeType": "application/octet-stream", "size": 173904, "local_path": "extracted_data/enhanced_context\\attachments\\197aeac9dc62c9e3_PURCHASE_ORDER-1880782-TEASYS.pdf"}], "metadata": {"date": "27 Jun 2025 09:57:04 +1000", "threadId": "197aeac9dc62c9e3", "labelIds": ["Label_5035052667921440910", "IMPORTANT", "CATEGORY_FORUMS", "Label_14", "INBOX"], "snippet": "Please find attached PURCHASE ORDER - 1880782 for TEASYS from RSEA Safety Bunbury ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DETAILS BELOW: PURCHASE", "internalDate": "1750982224000"}}, "thread_context": [{"id": "197aeac9dc62c9e3", "headers": {"Delivered-To": "<EMAIL>", "Received": "from RSEAMELAP13 (*************) by ML1PEPF0000F178.mail.protection.outlook.com (*************) with Microsoft SMTP Server id 15.20.8880.14 via Frontend Transport; Thu, 26 Jun 2025 23:57:04 +0000", "X-Forwarded-Encrypted": "i=3; AJvYcCXy887j4Z1qseVHnTyfkL6lfX5ms/VaGEZG4QOJ/j8wo+brFzkq0QvztpGN3ZnUp0gllZme1g==@teamsystems.net.au", "X-Google-Smtp-Source": "AGHT+IHYAE5E0gfiaTZpc/j7OOyANAy5OpwHUEGprpMWgV81W5VNkpisdnyFUNVro8ow6EUhmPI/", "X-Received": "by 2002:a05:6902:2701:b0:e82:3c9d:e654 with SMTP id 3f1490d57ef6-e87a7b662c8mr1826686276.23.1750982234897;        <PERSON><PERSON>, 26 Jun 2025 16:57:14 -0700 (PDT)", "ARC-Seal": "i=1; a=rsa-sha256; s=arcselector10001; d=microsoft.com; cv=none; b=FZM3qP+LaLAYW09uIAyFPFSPRJR8vCaVP/xWdhs9hnQ5Kea4H2DzfxmwMgfBQfL2K2F7OJbojYHwu1a1QLRtnchWGEMzM7ItW9j7oY5O1oVg95tPNmL9KMNdCLy2pfm38mGaA4yJgs6dQ5fgu/WYhfIjOSBvlF9mjM26b31ciG+03z3NZtuXpSt/YmOHGtTpixQOFceRbgK0Fs+QQS5+3JsLZsXV+WrWBHTJuOvK9FR/qx6EK49B2PUIYjhpTUzUp1UNIpA09rRnJzusqDxp5/s5VK1YBCVvYlBI/7FYD4tHH2GwnEfZr/IUH7jVpOvZ/+/Gmu5wxOuc7LCbVezayw==", "ARC-Message-Signature": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=microsoft.com; s=arcselector10001; h=From:Date:Subject:Message-ID:Content-Type:MIME-Version:X-MS-Exchange-AntiSpam-MessageData-ChunkCount:X-MS-Exchange-AntiSpam-MessageData-0:X-MS-Exchange-AntiSpam-MessageData-1; bh=A/CSnZpIJc8n9bB+lxauDxIeH9lfMu2aqzhj4nDpfdk=; b=FJRacht7BDx2wsepI4aLwlk7DnsucdssB5j9TogCRfE0R0k5Ub3JY7NqjcMfuHFjRAqLlu6vOqFBtrNzXQzd2yHrGKFbIM6wl6y4auYjT9T662zrqBRuc+o9l3VIB5NLW70nXvpOt3sXMFwEMsYt6gfekYUMU3WNFkYTj281LF2qXCVBcm6ZL0aOLiV3po7/a6gg8lNzpgTM9CRVpnX1lHym2X39y2+A3OyYjkv/B9TwtWWeo1DT63QGLUGiIQWpQhkug50nx9eibdAp0IXkc1hAQvSI6dwWiLeBYNNT56uhwsZY+P5tSPGY4lVzB1LoUOzI9quUuJA0ZiFJIPOiBg==", "ARC-Authentication-Results": "i=1; mx.microsoft.com 1; spf=softfail (sender ip is *************) smtp.rcpttodomain=teamsystems.net.au smtp.mailfrom=rsea.com.au; dmarc=fail (p=none sp=none pct=100) action=none header.from=rsea.com.au; dkim=none (message not signed); arc=none (0)", "Return-Path": "<<EMAIL>>", "Received-SPF": "SoftFail (protection.outlook.com: domain of transitioning rsea.com.au discourages use of ************* as permitted sender)", "Authentication-Results": "mx.google.com;       dkim=pass header.i=@teamsystems-net-au.20230601.gappssmtp.com header.s=20230601 header.b=WAxKW6Z0;       arc=pass (i=3 spf=pass spfdomain=rsea.com.au dkim=pass dkdomain=rsea.com.au dmarc=pass fromdomain=rsea.com.au);       spf=pass (google.com: <NAME_EMAIL> designates 2607:f8b0:4864:20::b46 as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=fail (p=NONE sp=NONE dis=NONE arc=pass) header.from=rsea.com.au;       dara=fail header.i=@teamsystems.net.au", "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=teamsystems-net-au.20230601.gappssmtp.com; s=20230601; t=1750982237; x=1751587037; darn=teamsystems.net.au;        h=list-unsubscribe:list-archive:list-help:list-post:list-id         :mailing-list:precedence:x-original-authentication-results         :x-original-sender:message-id:subject:date:to:from:mime-version:from         :to:cc:subject:date:message-id:reply-to;        bh=A/CSnZpIJc8n9bB+lxauDxIeH9lfMu2aqzhj4nDpfdk=;        b=WAxKW6Z0DfLclqslOrZnCv0PFww3J2o+KRi9KS3fCGobL9oO2WVRjLvfRVSUKJcUzV         ph3za50FRNEQOU6RtFuCeByV06Uk95Z5aPdBl0qqo8ksOowWNaJxcDTKWnXc3MizMFYX         LYqsLzcGKjcMx90vBznzXxbqMt84cgXx1ifC2+VLLd5Lsz5gCQ0LP/f0ca6BE+KkZiej         caySMsVb9EBm2k3A52PGEdpEeKS7j/tzKWJ3NaIiHJMv7TOOtkRiHq7mAd248Dlh7oTS         xWZUyhtxl0kpGn0Vn/aAQBPyz7DwKlqsY5hqAfNcOQqaj52ZHmO6fgqkytxieB3ugXyT         SxHw==", "X-Google-DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20230601; t=1750982237; x=1751587037;        h=list-unsubscribe:list-archive:list-help:list-post         :x-spam-checked-in-group:list-id:mailing-list:precedence         :x-original-authentication-results:x-original-sender:message-id         :subject:date:to:from:mime-version:x-beenthere:x-gm-message-state         :from:to:cc:subject:date:message-id:reply-to;        bh=A/CSnZpIJc8n9bB+lxauDxIeH9lfMu2aqzhj4nDpfdk=;        b=DR60t0ZzdiWaF0BudhX14OrGHKooepdLesGhM56U6yEqgR+MFTPb/TwzO8mEf3rC3j         GdYFUp2uvufuLHN3Yjo1PS0Jo6kCsJhlG7D1DKlZKIQ1ajxkzr8QsbDRfuTzCaco2Czl         4wY+W1u/lW13BZMUsyj0JQdEZOxJrUcUiUI+3ql1kmGnp2MFs5VMIsM74NFeAT37LjnD         /MtI8BUu8wKAmwk6VPKqpvi9jnc7n/sIMVUfnEzG11N/KO1MUSwpMvwtXu7lPRfzkKer         o1ih84GSq+eQH6tBjj/Uq4DuT2HnP3xJ8NafBFkBJmp+t8GV8wiBmYk37V4Ob+OUU3vL         4J8w==", "X-Gm-Message-State": "AOJu0YxYQx2w12d1yXx4Iv08hiE63kTUo41iYPBZgrv8xA9ac4BlCyIa 4BClQb0D2vOTes+MXerTtuPn5OQoXmNz/1vUSbmw/5kzKbj64DKZh0LNyLAwKPpzbugmTg==", "X-BeenThere": "<EMAIL>; h=AZMbMZd8npQWtQ4L0zKmy+B76TprYC8GozIb4nu8nzy0IJdW9Q==", "X-MS-Exchange-Authentication-Results": "spf=softfail (sender IP is *************) smtp.mailfrom=rsea.com.au; dkim=none (message not signed) header.d=none;dmarc=fail action=none header.from=rsea.com.au;", "MIME-Version": "1.0", "From": "<EMAIL>", "To": "<EMAIL>", "Date": "27 Jun 2025 09:57:04 +1000", "Subject": "PURCHASE ORDER - 1880782 for TEASYS from RSEA Safety Bunbury", "Content-Type": "multipart/mixed; boundary=--boundary_197_7ae93629-be10-495b-9c90-a5ad0864a1af", "Message-ID": "<<EMAIL>>", "X-EOPAttributedMessage": "0", "X-MS-PublicTrafficType": "Email", "X-MS-TrafficTypeDiagnostic": "ML1PEPF0000F178:EE_|SYBPR01MB5568:EE_", "X-MS-Office365-Filtering-Correlation-Id": "42b6cb1c-4571-4e04-c9f5-08ddb50d26e7", "X-MS-Exchange-SenderADCheck": "1", "X-MS-Exchange-AntiSpam-Relay": "0", "X-Microsoft-Antispam": "BCL:0;ARA:13230040|82310400026|36860700013|376014|1800799024|4053099003;", "X-Microsoft-Antispam-Message-Info": "cacNUGiF4BkK7grUGEfShbevOBp7QEDqsnVHDsRct8dRbudDo6iDBJ8zZ5idqdGLBOGf9EEV3soMZHh8rITDfOoLaaEHUtLVimCER8B+ws3dWodQnCVbSgsK9GSrjs7j1hQ5H/kCJ+XH/N3ZInOjBseYCvTEYf4WRDMXcTF7plgdtjiSxeGh6Jx5Sd60XV5hgCMYkHoMzR/7NF3qEZyXRlIRipCIDfP7TQPOQxsydUoi90ti8zR7wvrdlxfQbcnh60D1d8oTgcckO04tiTmkdzvtA2IezCkE+BJNNa2vGcdQyizWuoWrYSoam2+U33x82FhtwKIbBTNgZxW/tsFMUwX9tc+sXRTfXFUOGYeblW/uOo+3SfYmchGa09d00Fyl+CX8hLhkToZSCdy6ZN1JWDehRBc8iZ/Pc9WCIVbNYrQZqTrvbcXDlxOghpvyby1V5lQYIrWVcT1HWBMfPOraJ5m5tmfFycmcz0aZdfT2voCZ7qzqm1lVXF8XT5IvyeVs8lCi+oDpvaA74G3TJY4VDMjUCbgXpyxnMX/wLN05Q/scSlDn+rrT4AH5mdSucv59b0U8HeskUPi0pvLqk9AvC2uCsn6glV7TlPuFNM8FVBRk/PkvMqPlCP5lokC/2Ya9+yZTLMkgBS6WpUYP/8GPouRkP2T4NI6UW0nzwrZCAFfSlZGm7EBSaiIgF0IX1SlLasfbofNFnaItxlak/BodExHtMYSJR+bFYBn6x+YtHOclZ0prVac0cERMGU24xAH6sFrYcdyShNqjc4sVHH7vg9iIRgkrLwLAugQMfulr2p55Cdi7EOebraBrG+1da7L6fbenP5+EdYFOzT/uBZJNt+qTXm/cIELkPm2cpjde2lXxaDi/GiENs+tTq1Yw8GzJjPbSlH3LPn7Bz5xzv8COSlBUcitQMofwqzBqd6ARVntWPF8OAlRYfdF7nqUf/ONNAVlrYEhygS9IrxMjc+CIh+ji4mAKdl0vfkLL37VX3CMM3tcG1o/6e7q2kdrjozKpU12OkktD6se5SSyAXUy/xxVbF+te+db6kc9/0/2YDDh/VGNyLMS/ecVTyCaw5V+Ogysyv275xpBpNMcebZd6Sf5/C0UIxN2pF2ZA2uat62AT16rCwSVM5+9a2G8YJVc1GDVHOHc6Zr7rKBPtqgA9XURjPyIKaJkZdDp6IPPh/we0pTp1NPWtmA/WOOEFLKH4BajaAapXIl8OmczhbLCKhpTXnbYNb9SM9meq4z8MWG1pwb3oMT0dQCXjUd9sRBcHLVSJrD7WpHRVvndz4ayIhAyf9O+AjjpJDz9IhA6PvQCJ0cu3m/Do98v7TmQ71lB/CXBewKcMZ2KPZWvKKCG00rfAR3FpdfA9Ru/D1P7o2CGOxeSzMfZnuY0dnvvgNlNk4ViweWoc4C/jLLNdbyn1hN7jJfROc/c8aaUcy7LAHXJf12+A778UEE+nX6ItP+F4ui3nB56YPp8t8HDUI7dD5g==", "X-Forefront-Antispam-Report": "CIP:*************;CTRY:AU;LANG:en;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:RSEAMELAP13;PTR:InfoDomainNonexistent;CAT:NONE;SFS:(13230040)(82310400026)(36860700013)(376014)(1800799024)(4053099003);DIR:OUT;SFP:1102;", "X-OriginatorOrg": "rsea.com.au", "X-MS-Exchange-CrossTenant-OriginalArrivalTime": "26 Jun 2025 23:57:04.8287 (UTC)", "X-MS-Exchange-CrossTenant-Network-Message-Id": "42b6cb1c-4571-4e04-c9f5-08ddb50d26e7", "X-MS-Exchange-CrossTenant-Id": "e89d8ffd-f15f-4b37-8c28-6be445065af7", "X-MS-Exchange-CrossTenant-OriginalAttributedTenantConnectingIp": "TenantId=e89d8ffd-f15f-4b37-8c28-6be445065af7;Ip=[*************];Helo=[RSEAMELAP13]", "X-MS-Exchange-CrossTenant-AuthSource": "ML1PEPF0000F178.ausprd01.prod.outlook.com", "X-MS-Exchange-CrossTenant-AuthAs": "Anonymous", "X-MS-Exchange-CrossTenant-FromEntityHeader": "HybridOnPrem", "X-MS-Exchange-Transport-CrossTenantHeadersStamped": "SYBPR01MB5568", "X-Original-Sender": "<EMAIL>", "X-Original-Authentication-Results": "mx.google.com;       dkim=pass header.i=@rsea.com.au header.s=selector2 header.b=wcphJ8aX;       arc=pass (i=1);       spf=pass (google.com: <NAME_EMAIL> designates 2a01:111:f403:c40d::3 as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=rsea.com.au", "Precedence": "list", "Mailing-list": "list <EMAIL>; contact <EMAIL>", "List-ID": "<melbourne.teamsystems.net.au>", "X-Spam-Checked-In-Group": "<EMAIL>", "X-Google-Group-Id": "114953712296", "List-Post": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/post>, <mailto:<EMAIL>>", "List-Help": "<https://support.google.com/a/teamsystems.net.au/bin/topic.py?topic=25838>, <mailto:<EMAIL>>", "List-Archive": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/>", "List-Unsubscribe": "<mailto:<EMAIL>>, <https://groups.google.com/a/teamsystems.net.au/group/melbourne/subscribe>"}, "snippet": "Please find attached PURCHASE ORDER - 1880782 for TEASYS from RSEA Safety Bunbury ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DETAILS BELOW: PURCHASE", "date": "27 Jun 2025 09:57:04 +1000"}], "sender_history": [{"id": "197aeac9dc62c9e3", "subject": "PURCHASE ORDER - 1880782 for TEASYS from RSEA Safety Bunbury", "date": "27 Jun 2025 09:57:04 +1000", "snippet": "Please find attached PURCHASE ORDER - 1880782 for TEASYS from RSEA Safety Bunbury ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DETAILS BELOW: PURCHASE"}, {"id": "197ae76578040b3f", "subject": "PURCHASE ORDER - 1880680 for TEASYS from RSEA Safety Kotara", "date": "27 Jun 2025 08:57:49 +1000", "snippet": "Please find attached PURCHASE ORDER - 1880680 for TEASYS from RSEA Safety Kotara ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DETAILS BELOW: PURCHASE"}, {"id": "197a43f9524460b9", "subject": "PURCHASE ORDER - 1880327 for TEASYS from RSEA SAFETY LAVERTON", "date": "25 Jun 2025 09:21:51 +1000", "snippet": "Please find attached PURCHASE ORDER - 1880327 for TEASYS from RSEA SAFETY LAVERTON ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DETAILS BELOW: PURCHASE"}, {"id": "197a0bdda8d6c681", "subject": "PURCHASE ORDER - 1880296 for TEASYS from RSEA SAFETY LAVERTON", "date": "24 Jun 2025 17:01:15 +1000", "snippet": "Please find attached PURCHASE ORDER - 1880296 for TEASYS from RSEA SAFETY LAVERTON ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DETAILS BELOW: PURCHASE"}, {"id": "1975dc70f44498d1", "subject": "PURCHASE ORDER - 1876931 for TEASYS from RSEA SAFETY WELSHPOOL", "date": "11 Jun 2025 16:56:43 +1000", "snippet": "Please find attached PURCHASE ORDER - 1876931 for TEASYS from RSEA SAFETY WELSHPOOL ***PLEASE DO NOT REPLY TO THIS EMAIL ADDRESS AS IT IS NOT MONITORED*** PLEASE SEE OUR CONTACT DE<PERSON>ILS BELOW: PURCHASE"}], "label_context": "RSEA", "extraction_time": "2025-07-03T05:24:32.845966"}