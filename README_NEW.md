# Automated Email Order Processing Pipeline

A clean, modular Python application that automatically processes email orders with PDF attachments and creates sales orders in MYOB EXO via API integration.

## Features

- **Gmail Integration**: Automatically fetches emails from specified labels with PDF attachments
- **LLM-Powered Parsing**: Uses Google Gemini to intelligently extract order information from emails and PDFs
- **Structured Data Processing**: Creates markdown summaries and structured JSON payloads
- **MYOB EXO Integration**: Validates and posts sales orders to MYOB EXO via API
- **User Approval Workflow**: Interactive approval process for order posting
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

## Workflow

1. **Initialize Gmail Service**: Authenticates with Gmail API and connects to your account
2. **Initialize LLM Agent**: Sets up Google Gemini for intelligent text processing
3. **Parse Emails**: Fetches emails from specified labels containing PDF orders
4. **Create Markdown Summary**: Generates structured markdown of important order information
5. **Extract JSON Data**: Uses LLM to create structured JSON payload for MYOB
6. **User Approval**: Presents orders for manual approval before posting
7. **Post to MYOB**: Creates sales orders in MYOB EXO via API

## Quick Start

1. **<PERSON>lone and Setup**:
   ```bash
   cd your_project_directory
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys and configuration
   ```

3. **Setup Gmail API**:
   - Download OAuth 2.0 credentials from Google Cloud Console
   - Save as `credentials.json` in the project directory

4. **Run the Application**:
   ```bash
   python email_order_processor.py
   ```

## Configuration

The application is configured using environment variables in a `.env` file:

### Gmail Settings
- `GMAIL_CREDENTIALS_FILE`: Path to Gmail OAuth 2.0 credentials file
- `GMAIL_TOKEN_FILE`: Path where Gmail token will be stored
- `GMAIL_LABELS_TO_PROCESS`: Comma-separated list of Gmail labels to monitor
- `GMAIL_UNREAD_ONLY`: Process only unread emails (True/False)
- `MAX_GMAIL_RESULTS`: Maximum number of emails to fetch per run

### Gemini LLM Settings
- `GEMINI_API_KEY`: Your Google Gemini API key
- `GEMINI_MODEL`: Gemini model to use (default: gemini-1.5-flash-latest)

### MYOB API Settings
- `EXO_IP`: MYOB EXO server IP address
- `EXO_PORT`: MYOB EXO API port
- `USER`: MYOB API username
- `PWD`: MYOB API password
- `API_KEY`: MYOB API key
- `EXO_TOK`: MYOB EXO token

### Logging
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Project Structure

```
email_order_processor.py    # Main application file
requirements.txt           # Python dependencies
.env.example              # Environment configuration template
.env                      # Your actual configuration (create from .env.example)
credentials.json          # Gmail OAuth credentials (download from Google Cloud)
token.pickle             # Gmail token (auto-generated)
README.md                # This file
```

## Key Classes and Components

### EmailOrderProcessor
Main orchestration class that coordinates the entire workflow.

### GmailService
- Handles Gmail API authentication and operations
- Fetches emails from specified labels
- Extracts text from PDF attachments
- Manages email read/unread status

### LLMService
- Creates structured markdown summaries from email content
- Parses order data using Google Gemini
- Converts unstructured text to structured JSON

### MYOBService
- Validates sales orders with MYOB API
- Posts validated orders to MYOB EXO
- Handles MYOB API authentication and error handling

### Data Models (Pydantic)
- `CustomerDetails`: Customer information and order numbers
- `DeliveryAddress`: Shipping address details
- `OrderLine`: Individual order line items
- `ExtractedOrder`: Complete order structure

## Setup Instructions

### 1. Gmail API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable the Gmail API
4. Create OAuth 2.0 credentials (Desktop application)
5. Download credentials and save as `credentials.json`

### 2. Google Gemini API
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create an API key
3. Add to your `.env` file

### 3. MYOB EXO API
Ensure you have:
- MYOB EXO server access
- API user credentials
- API key and EXO token

## Usage

1. **First Run**: The application will open a browser for Gmail authorization
2. **Email Processing**: Automatically fetches and processes emails from configured labels
3. **Review Orders**: Each parsed order is presented with a markdown summary
4. **Approve/Deny**: Choose to approve, deny, or skip each order
5. **MYOB Integration**: Approved orders are automatically posted to MYOB

## Example Workflow Output

```
=============================================================
ORDER 1 of 3 (Email ID: abc123)
=============================================================

## Order Summary
- **Customer**: WOOLWORTHS LIMITED
- **Order Number**: LVA4401196688
- **Date**: 2024-01-15
- **Delivery Address**: 123 Distribution Center, Sydney NSW 2000
- **Shipping Method**: CAPITAL

## Order Items
| Stock Code | Description | Quantity | Unit Price | Total |
|------------|-------------|----------|------------|-------|
| SKU001     | Product A   | 100      | $10.50     | $1,050|
| SKU002     | Product B   | 50       | $25.00     | $1,250|

=============================================================

Approve this order? (a)pprove / (d)eny / (s)kip: a
```

## Error Handling

The application includes comprehensive error handling for:
- Gmail API authentication issues
- PDF extraction failures
- LLM parsing errors
- MYOB API communication problems
- Network connectivity issues

## Logging

Detailed logging is provided for:
- Authentication processes
- Email fetching and parsing
- LLM interactions
- MYOB API calls
- User decisions and outcomes

## Troubleshooting

### Common Issues

1. **Gmail Authentication Errors**
   - Delete `token.pickle` and re-run for fresh authentication
   - Verify `credentials.json` is valid and in correct location

2. **MYOB API Errors**
   - Check server connectivity and credentials
   - Verify API user has required permissions
   - Check logs for specific error messages

3. **LLM Parsing Issues**
   - Verify Gemini API key is valid
   - Check internet connectivity
   - Review email content for parsing complexity

4. **PDF Extraction Problems**
   - Some PDFs may be image-based (requires OCR)
   - Complex layouts may need manual review

## Dependencies

- `python-dotenv`: Environment variable management
- `google-api-python-client`: Gmail API client
- `google-auth-*`: Google authentication libraries
- `beautifulsoup4`: HTML parsing
- `PyMuPDF`: PDF text extraction
- `google-generativeai`: Gemini LLM integration
- `pydantic`: Data validation
- `requests`: HTTP requests for MYOB API

## Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive logging for new features
3. Include error handling for all external API calls
4. Update documentation for any configuration changes

## License

This project is for internal use. Please ensure compliance with all API terms of service for Gmail, Gemini, and MYOB.
