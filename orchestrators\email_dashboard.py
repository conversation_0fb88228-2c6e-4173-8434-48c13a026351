#!/usr/bin/env python3
"""
Enhanced Email Dashboard - A comprehensive web-based interface for email management and processing.
"""
import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from flask import Flask, render_template, request, jsonify, send_file
from dataclasses import asdict
import sqlite3
from pathlib import Path


# Add project root to Python path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.gmail_service import GmailService
from orchestrators.main_processor import EmailOrderProcessor
from utils.models import EmailData, ProcessedOrder

logger = logging.getLogger(__name__)

class EmailDashboard:
    """Enhanced dashboard for email management and processing."""
    
    def __init__(self):
        self.app = Flask(__name__)
        self.gmail_service = GmailService()
        self.processor = EmailOrderProcessor()
        self.db_path = "email_dashboard.db"
        self._init_database()
        self._setup_routes()
        
    def _init_database(self):
        """Initialize SQLite database for tracking email processing history."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create emails table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS emails (
                id TEXT PRIMARY KEY,
                subject TEXT NOT NULL,
                sender TEXT,
                timestamp TEXT,
                source_label TEXT,
                status TEXT DEFAULT 'unprocessed',
                processed_at TEXT,
                markdown_file TEXT,
                myob_file TEXT,
                error_message TEXT,
                attachment_count INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create processing_stats table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS processing_stats (
                date TEXT PRIMARY KEY,
                total_processed INTEGER DEFAULT 0,
                successful INTEGER DEFAULT 0,
                failed INTEGER DEFAULT 0,
                review INTEGER DEFAULT 0
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Database initialized successfully")
    
    def _setup_routes(self):
        """Setup Flask routes for the dashboard."""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard page."""
            return render_template('dashboard.html')
        
        @self.app.route('/api/emails')
        def get_emails():
            """API endpoint to get emails with filtering and pagination."""
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            status_filter = request.args.get('status', 'all')
            label_filter = request.args.get('label', 'all')
            search_query = request.args.get('search', '')
            
            emails = self._get_emails_from_db(
                page=page, per_page=per_page,
                status_filter=status_filter,
                label_filter=label_filter,
                search_query=search_query
            )
            
            return jsonify(emails)
        
        @self.app.route('/api/stats')
        def get_stats():
            """API endpoint to get processing statistics."""
            stats = self._get_processing_stats()
            return jsonify(stats)
        
        @self.app.route('/api/refresh-emails', methods=['POST'])
        def refresh_emails():
            """API endpoint to refresh emails from Gmail."""
            try:
                self._sync_emails_from_gmail()
                return jsonify({'status': 'success', 'message': 'Emails refreshed successfully'})
            except Exception as e:
                logger.error(f"Error refreshing emails: {e}")
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/api/process-emails', methods=['POST'])
        def process_emails():
            """API endpoint to process selected emails."""
            data = request.get_json()
            email_ids = data.get('email_ids', []) if data else []
            if not email_ids:
                return jsonify({'status': 'error', 'message': 'No emails selected'}), 400
            
            try:
                results = self._process_selected_emails(email_ids)
                return jsonify({'status': 'success', 'results': results})
            except Exception as e:
                logger.error(f"Error processing emails: {e}")
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/api/email/<email_id>')
        def get_email_details(email_id):
            """API endpoint to get detailed email information."""
            email_details = self._get_email_details(email_id)
            if email_details:
                return jsonify(email_details)
            else:
                return jsonify({'error': 'Email not found'}), 404
        
        @self.app.route('/api/email/<email_id>/mark-status', methods=['POST'])
        def mark_email_status(email_id):
            """API endpoint to manually mark email status."""
            data = request.get_json()
            status = data.get('status') if data else None
            if status not in ['processed', 'failed', 'review', 'unprocessed']:
                return jsonify({'status': 'error', 'message': 'Invalid status'}), 400
            
            try:
                self._update_email_status(email_id, status)
                # Also update Gmail label
                if status == 'processed':
                    self.gmail_service.mark_email_processed(email_id)
                elif status == 'failed':
                    self.gmail_service.mark_email_failed(email_id)
                elif status == 'review':
                    self.gmail_service.mark_email_review(email_id)
                    
                return jsonify({'status': 'success', 'message': f'Email marked as {status}'})
            except Exception as e:
                logger.error(f"Error updating email status: {e}")
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        @self.app.route('/api/download/<file_type>/<filename>')
        def download_file(file_type, filename):
            """API endpoint to download generated files."""
            try:
                if file_type == 'markdown':
                    file_path = os.path.join('markdown', filename)
                elif file_type == 'myob':
                    file_path = os.path.join('myob', filename)
                else:
                    return jsonify({'error': 'Invalid file type'}), 400
                
                if os.path.exists(file_path):
                    return send_file(file_path, as_attachment=True)
                else:
                    return jsonify({'error': 'File not found'}), 404
            except Exception as e:
                logger.error(f"Error downloading file: {e}")
                return jsonify({'error': str(e)}), 500
    
    def _sync_emails_from_gmail(self):
        """Sync emails from Gmail to local database."""
        logger.info("Syncing emails from Gmail...")
        
        # Get emails from Gmail using existing processor logic
        emails = []
        label_names = ['Brady', 'RSEA', 'Woolworths', 'Brierley', 'Gateway', 'Highgate', 'Sitecraft']
        
        for label_name in label_names:
            label_id = self.gmail_service.get_label_id(label_name)
            if label_id and hasattr(self.gmail_service, 'service') and self.gmail_service.service:
                try:
                    response = self.gmail_service.service.users().messages().list(
                        userId='me',
                        labelIds=[label_id],
                        maxResults=50,
                        q="has:attachment filename:pdf"
                    ).execute()
                    
                    messages = response.get('messages', [])
                    logger.info(f"Found {len(messages)} emails in label '{label_name}'")
                    
                    for msg_ref in messages:
                        email_data = self.gmail_service._get_email_details(msg_ref['id'], label_name)
                        if email_data:
                            emails.append(email_data)
                            
                except Exception as e:
                    logger.error(f"Error fetching emails from {label_name}: {e}")
        
        # Store emails in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for email in emails:
            # Check Gmail processing status
            gmail_status = self.gmail_service.has_processing_label(email.id)
            status = 'unprocessed'
            if gmail_status == 'Processed':
                status = 'processed'
            elif gmail_status == 'Failed':
                status = 'failed'
            elif gmail_status == 'Review':
                status = 'review'
            
            cursor.execute('''
                INSERT OR REPLACE INTO emails 
                (id, subject, sender, timestamp, source_label, status, attachment_count)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                email.id, email.subject, email.sender, email.timestamp,
                email.source_label, status, len(email.attachments)
            ))
        
        conn.commit()
        conn.close()
        logger.info(f"Synced {len(emails)} emails to database")
    
    def _get_emails_from_db(self, page=1, per_page=20, status_filter='all', 
                           label_filter='all', search_query=''):
        """Get emails from database with filtering and pagination."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Build query
        where_conditions = []
        params = []
        
        if status_filter != 'all':
            where_conditions.append("status = ?")
            params.append(status_filter)
        
        if label_filter != 'all':
            where_conditions.append("source_label = ?")
            params.append(label_filter)
        
        if search_query:
            where_conditions.append("(subject LIKE ? OR sender LIKE ?)")
            params.extend([f"%{search_query}%", f"%{search_query}%"])
        
        where_clause = " AND ".join(where_conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # Get total count
        count_query = f"SELECT COUNT(*) FROM emails {where_clause}"
        cursor.execute(count_query, params)
        total_count = cursor.fetchone()[0]
        
        # Get paginated results
        offset = (page - 1) * per_page
        query = f"""
            SELECT id, subject, sender, timestamp, source_label, status, 
                   processed_at, attachment_count, created_at
            FROM emails 
            {where_clause}
            ORDER BY timestamp DESC 
            LIMIT ? OFFSET ?
        """
        params.extend([per_page, offset])
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        emails = []
        for row in rows:
            emails.append({
                'id': row[0],
                'subject': row[1],
                'sender': row[2],
                'timestamp': row[3],
                'source_label': row[4],
                'status': row[5],
                'processed_at': row[6],
                'attachment_count': row[7],
                'created_at': row[8]
            })
        
        conn.close()
        
        return {
            'emails': emails,
            'total_count': total_count,
            'page': page,
            'per_page': per_page,
            'total_pages': (total_count + per_page - 1) // per_page
        }
    
    def _get_processing_stats(self):
        """Get processing statistics for dashboard."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get overall stats
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'review' THEN 1 ELSE 0 END) as review,
                SUM(CASE WHEN status = 'unprocessed' THEN 1 ELSE 0 END) as unprocessed
            FROM emails
        ''')
        overall_stats = cursor.fetchone()
        
        # Get daily stats for the last 7 days
        cursor.execute('''
            SELECT 
                DATE(timestamp) as date,
                COUNT(*) as total,
                SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'review' THEN 1 ELSE 0 END) as review
            FROM emails 
            WHERE timestamp >= date('now', '-7 days')
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        ''')
        daily_stats = cursor.fetchall()
        
        # Get stats by label
        cursor.execute('''
            SELECT 
                source_label,
                COUNT(*) as total,
                SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'review' THEN 1 ELSE 0 END) as review
            FROM emails
            GROUP BY source_label
            ORDER BY total DESC
        ''')
        label_stats = cursor.fetchall()
        
        conn.close()
        
        return {
            'overall': {
                'total': overall_stats[0],
                'processed': overall_stats[1],
                'failed': overall_stats[2],
                'review': overall_stats[3],
                'unprocessed': overall_stats[4]
            },
            'daily': [
                {
                    'date': row[0],
                    'total': row[1],
                    'processed': row[2],
                    'failed': row[3],
                    'review': row[4]
                } for row in daily_stats
            ],
            'by_label': [
                {
                    'label': row[0],
                    'total': row[1],
                    'processed': row[2],
                    'failed': row[3],
                    'review': row[4]
                } for row in label_stats
            ]
        }
    
    def _process_selected_emails(self, email_ids: List[str]):
        """Process selected emails and update database."""
        results = []
        
        for email_id in email_ids:
            try:
                # Get email from Gmail
                email_data = self.gmail_service._get_email_details(email_id, "Processing")
                if not email_data:
                    results.append({
                        'email_id': email_id,
                        'status': 'error',
                        'message': 'Email not found'
                    })
                    continue
                
                # Process the email
                processed_order = self.processor._process_single_email(email_data)
                
                # Update database
                self._update_email_status(
                    email_id, 'processed',
                    markdown_file=processed_order.markdown_filepath,
                    myob_file=processed_order.myob_filepath
                )
                
                results.append({
                    'email_id': email_id,
                    'status': 'success',
                    'message': 'Processed successfully',
                    'markdown_file': processed_order.markdown_filepath,
                    'myob_file': processed_order.myob_filepath
                })
                
            except Exception as e:
                logger.error(f"Error processing email {email_id}: {e}")
                self._update_email_status(email_id, 'failed', error_message=str(e))
                results.append({
                    'email_id': email_id,
                    'status': 'error',
                    'message': str(e)
                })
        
        return results
    
    def _update_email_status(self, email_id: str, status: str,
                           markdown_file: Optional[str] = None, myob_file: Optional[str] = None,
                           error_message: Optional[str] = None):
        """Update email status in database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE emails 
            SET status = ?, processed_at = ?, markdown_file = ?, 
                myob_file = ?, error_message = ?
            WHERE id = ?
        ''', (
            status, datetime.now().isoformat(), markdown_file,
            myob_file, error_message, email_id
        ))
        
        conn.commit()
        conn.close()
    
    def _get_email_details(self, email_id: str):
        """Get detailed email information."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM emails WHERE id = ?
        ''', (email_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            # Also get fresh data from Gmail if needed
            email_data = self.gmail_service._get_email_details(email_id, "Details")
            
            return {
                'id': row[0],
                'subject': row[1],
                'sender': row[2],
                'timestamp': row[3],
                'source_label': row[4],
                'status': row[5],
                'processed_at': row[6],
                'markdown_file': row[7],
                'myob_file': row[8],
                'error_message': row[9],
                'attachment_count': row[10],
                'body': email_data.body if email_data else '',
                'attachments': [att['filename'] for att in email_data.attachments] if email_data else []
            }
        
        return None
    
    def run(self, host='localhost', port=5000, debug=True):
        """Run the dashboard server."""
        # Ensure templates directory exists
        templates_dir = 'templates'
        os.makedirs(templates_dir, exist_ok=True)
        
        # Create the HTML template if it doesn't exist
        self._create_dashboard_template()
        
        print(f"\n{'='*60}")
        print("📧 EMAIL DASHBOARD SERVER")
        print(f"{'='*60}")
        print(f"🌐 Dashboard URL: http://{host}:{port}")
        print(f"📊 Features: Email management, processing, statistics")
        print(f"🔄 Auto-sync with Gmail labels")
        print(f"{'='*60}\n")
        
        # Initial sync
        try:
            self._sync_emails_from_gmail()
            print("✅ Initial email sync completed")
        except Exception as e:
            print(f"⚠️  Initial sync failed: {e}")
        
        self.app.run(host=host, port=port, debug=debug)
    
    def _create_dashboard_template(self):
        """Create the HTML template for the dashboard."""
        template_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Dashboard - TeamsysV0.1</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .status-unprocessed { background-color: #6c757d; }
        .status-processed { background-color: #198754; }
        .status-failed { background-color: #dc3545; }
        .status-review { background-color: #fd7e14; }
        
        .email-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .email-row:hover {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        
        .stats-card {
            border-left: 4px solid;
        }
        .stats-card.total { border-color: #6c757d; }
        .stats-card.processed { border-color: #198754; }
        .stats-card.failed { border-color: #dc3545; }
        .stats-card.review { border-color: #fd7e14; }
        .stats-card.unprocessed { border-color: #0d6efd; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar p-4">
                <h4><i class="fas fa-envelope"></i> Email Dashboard</h4>
                <hr>
                
                <!-- Statistics Cards -->
                <div id="stats-container">
                    <div class="card stats-card total mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Total Emails</h6>
                                    <h4 class="mb-0" id="stat-total">0</h4>
                                </div>
                                <i class="fas fa-envelope fa-2x text-muted"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card processed mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Processed</h6>
                                    <h4 class="mb-0" id="stat-processed">0</h4>
                                </div>
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card review mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Review</h6>
                                    <h4 class="mb-0" id="stat-review">0</h4>
                                </div>
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card failed mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Failed</h6>
                                    <h4 class="mb-0" id="stat-failed">0</h4>
                                </div>
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card stats-card unprocessed mb-3">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title mb-0">Unprocessed</h6>
                                    <h4 class="mb-0" id="stat-unprocessed">0</h4>
                                </div>
                                <i class="fas fa-clock fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="mb-3">
                    <label class="form-label">Status Filter</label>
                    <select class="form-select" id="status-filter">
                        <option value="all">All Statuses</option>
                        <option value="unprocessed">Unprocessed</option>
                        <option value="processed">Processed</option>
                        <option value="review">Review</option>
                        <option value="failed">Failed</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Label Filter</label>
                    <select class="form-select" id="label-filter">
                        <option value="all">All Labels</option>
                        <option value="Brady">Brady</option>
                        <option value="RSEA">RSEA</option>
                        <option value="Woolworths">Woolworths</option>
                        <option value="Brierley">Brierley</option>
                        <option value="Gateway">Gateway</option>
                        <option value="Highgate">Highgate</option>
                        <option value="Sitecraft">Sitecraft</option>
                    </select>
                </div>
                
                <!-- Actions -->
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" id="refresh-btn">
                        <i class="fas fa-sync"></i> Refresh Emails
                    </button>
                    <button class="btn btn-success" id="process-selected-btn" disabled>
                        <i class="fas fa-cog"></i> Process Selected
                    </button>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 p-4">
                <!-- Search and Tools -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search-input" 
                                   placeholder="Search emails by subject or sender...">
                            <button class="btn btn-outline-secondary" id="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-outline-primary" id="select-all-btn">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button class="btn btn-outline-secondary" id="clear-selection-btn">
                            <i class="fas fa-square"></i> Clear
                        </button>
                    </div>
                </div>
                
                <!-- Email List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> Email List
                            <span class="badge bg-secondary ms-2" id="email-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="select-all-checkbox">
                                        </th>
                                        <th>Subject</th>
                                        <th>Sender</th>
                                        <th>Label</th>
                                        <th>Status</th>
                                        <th>Attachments</th>
                                        <th>Date</th>
                                        <th width="100">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="emails-table-body">
                                    <!-- Emails will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Email Detail Modal -->
    <div class="modal fade" id="emailDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Email Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="email-detail-content">
                    <!-- Email details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let selectedEmails = new Set();
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadEmails();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Filter changes
            document.getElementById('status-filter').addEventListener('change', () => {
                currentPage = 1;
                loadEmails();
            });
            
            document.getElementById('label-filter').addEventListener('change', () => {
                currentPage = 1;
                loadEmails();
            });
            
            // Search
            document.getElementById('search-btn').addEventListener('click', () => {
                currentPage = 1;
                loadEmails();
            });
            
            document.getElementById('search-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    currentPage = 1;
                    loadEmails();
                }
            });
            
            // Refresh button
            document.getElementById('refresh-btn').addEventListener('click', refreshEmails);
            
            // Process selected button
            document.getElementById('process-selected-btn').addEventListener('click', processSelectedEmails);
            
            // Select all buttons
            document.getElementById('select-all-btn').addEventListener('click', selectAllEmails);
            document.getElementById('clear-selection-btn').addEventListener('click', clearSelection);
            document.getElementById('select-all-checkbox').addEventListener('change', toggleSelectAll);
        }
        
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                document.getElementById('stat-total').textContent = stats.overall.total;
                document.getElementById('stat-processed').textContent = stats.overall.processed;
                document.getElementById('stat-review').textContent = stats.overall.review;
                document.getElementById('stat-failed').textContent = stats.overall.failed;
                document.getElementById('stat-unprocessed').textContent = stats.overall.unprocessed;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }
        
        async function loadEmails() {
            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    per_page: 20,
                    status: document.getElementById('status-filter').value,
                    label: document.getElementById('label-filter').value,
                    search: document.getElementById('search-input').value
                });
                
                const response = await fetch(`/api/emails?${params}`);
                const data = await response.json();
                
                displayEmails(data.emails);
                updatePagination(data.page, data.total_pages);
                document.getElementById('email-count').textContent = data.total_count;
            } catch (error) {
                console.error('Error loading emails:', error);
            }
        }
        
        function displayEmails(emails) {
            const tbody = document.getElementById('emails-table-body');
            tbody.innerHTML = '';
            
            emails.forEach(email => {
                const row = document.createElement('tr');
                row.className = 'email-row';
                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="email-checkbox" value="${email.id}">
                    </td>
                    <td>
                        <a href="#" onclick="showEmailDetails('${email.id}')" class="text-decoration-none">
                            ${email.subject}
                        </a>
                    </td>
                    <td>${email.sender || 'Unknown'}</td>
                    <td><span class="badge bg-info">${email.source_label}</span></td>
                    <td>
                        <span class="badge status-badge status-${email.status}">
                            ${email.status.charAt(0).toUpperCase() + email.status.slice(1)}
                        </span>
                    </td>
                    <td>
                        <i class="fas fa-paperclip"></i> ${email.attachment_count}
                    </td>
                    <td>${formatDate(email.timestamp)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="showEmailDetails('${email.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" onclick="markEmailStatus('${email.id}', 'processed')">
                                        <i class="fas fa-check text-success"></i> Mark Processed
                                    </a></li>
                                    <li><a class="dropdown-item" onclick="markEmailStatus('${email.id}', 'review')">
                                        <i class="fas fa-exclamation-triangle text-warning"></i> Mark Review
                                    </a></li>
                                    <li><a class="dropdown-item" onclick="markEmailStatus('${email.id}', 'failed')">
                                        <i class="fas fa-times text-danger"></i> Mark Failed
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
            
            // Add event listeners to checkboxes
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedEmails);
            });
        }
        
        function updateSelectedEmails() {
            selectedEmails.clear();
            document.querySelectorAll('.email-checkbox:checked').forEach(checkbox => {
                selectedEmails.add(checkbox.value);
            });
            
            document.getElementById('process-selected-btn').disabled = selectedEmails.size === 0;
        }
        
        function updatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            // Previous button
            const prevBtn = document.createElement('li');
            prevBtn.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevBtn.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
            pagination.appendChild(prevBtn);
            
            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const pageBtn = document.createElement('li');
                pageBtn.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageBtn.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
                pagination.appendChild(pageBtn);
            }
            
            // Next button
            const nextBtn = document.createElement('li');
            nextBtn.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextBtn.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
            pagination.appendChild(nextBtn);
        }
        
        function changePage(page) {
            currentPage = page;
            loadEmails();
        }
        
        async function refreshEmails() {
            const btn = document.getElementById('refresh-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            
            try {
                const response = await fetch('/api/refresh-emails', { method: 'POST' });
                const result = await response.json();
                
                if (result.status === 'success') {
                    loadStats();
                    loadEmails();
                    showToast('Emails refreshed successfully', 'success');
                } else {
                    showToast('Error refreshing emails: ' + result.message, 'error');
                }
            } catch (error) {
                showToast('Error refreshing emails: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-sync"></i> Refresh Emails';
            }
        }
        
        async function processSelectedEmails() {
            if (selectedEmails.size === 0) return;
            
            const btn = document.getElementById('process-selected-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            
            try {
                const response = await fetch('/api/process-emails', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email_ids: Array.from(selectedEmails) })
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    showToast(`Processed ${result.results.length} emails`, 'success');
                    loadStats();
                    loadEmails();
                    clearSelection();
                } else {
                    showToast('Error processing emails: ' + result.message, 'error');
                }
            } catch (error) {
                showToast('Error processing emails: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-cog"></i> Process Selected';
            }
        }
        
        async function showEmailDetails(emailId) {
            try {
                const response = await fetch(`/api/email/${emailId}`);
                const email = await response.json();
                
                const content = document.getElementById('email-detail-content');
                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Subject:</strong> ${email.subject}<br>
                            <strong>Sender:</strong> ${email.sender}<br>
                            <strong>Label:</strong> <span class="badge bg-info">${email.source_label}</span><br>
                            <strong>Status:</strong> <span class="badge status-badge status-${email.status}">${email.status}</span><br>
                            <strong>Timestamp:</strong> ${formatDate(email.timestamp)}
                        </div>
                        <div class="col-md-6">
                            <strong>Attachments:</strong> ${email.attachment_count}<br>
                            ${email.attachments ? email.attachments.map(att => `<span class="badge bg-secondary me-1">${att}</span>`).join('') : ''}
                        </div>
                    </div>
                    <hr>
                    <div>
                        <strong>Email Body:</strong>
                        <div class="border p-3 mt-2" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                            ${email.body ? email.body.replace(/\n/g, '<br>') : 'No body content'}
                        </div>
                    </div>
                    ${email.markdown_file || email.myob_file ? `
                        <hr>
                        <div>
                            <strong>Generated Files:</strong><br>
                            ${email.markdown_file ? `<a href="/api/download/markdown/${email.markdown_file.split('/').pop()}" class="btn btn-sm btn-outline-primary me-2"><i class="fas fa-download"></i> Markdown</a>` : ''}
                            ${email.myob_file ? `<a href="/api/download/myob/${email.myob_file.split('/').pop()}" class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i> MYOB JSON</a>` : ''}
                        </div>
                    ` : ''}
                    ${email.error_message ? `
                        <hr>
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${email.error_message}
                        </div>
                    ` : ''}
                `;
                
                new bootstrap.Modal(document.getElementById('emailDetailModal')).show();
            } catch (error) {
                showToast('Error loading email details: ' + error.message, 'error');
            }
        }
        
        async function markEmailStatus(emailId, status) {
            try {
                const response = await fetch(`/api/email/${emailId}/mark-status`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ status })
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    showToast(result.message, 'success');
                    loadStats();
                    loadEmails();
                } else {
                    showToast('Error updating status: ' + result.message, 'error');
                }
            } catch (error) {
                showToast('Error updating status: ' + error.message, 'error');
            }
        }
        
        function selectAllEmails() {
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedEmails();
        }
        
        function clearSelection() {
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('select-all-checkbox').checked = false;
            updateSelectedEmails();
        }
        
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all-checkbox').checked;
            document.querySelectorAll('.email-checkbox').forEach(checkbox => {
                checkbox.checked = selectAll;
            });
            updateSelectedEmails();
        }
        
        function formatDate(dateString) {
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            } catch {
                return dateString;
            }
        }
        
        function showToast(message, type) {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>'''
        
        os.makedirs('templates', exist_ok=True)
        with open('templates/dashboard.html', 'w', encoding='utf-8') as f:
            f.write(template_content)

def main():
    """Run the email dashboard."""
    dashboard = EmailDashboard()
    dashboard.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    main()