"""
Gmail service for fetching and processing emails.
"""
import os
import base64
import logging
import pickle
import binascii
from typing import List, Optional
from bs4 import BeautifulSoup

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from config import config
from models import EmailData

logger = logging.getLogger(__name__)

class GmailService:
    """Service for Gmail API operations."""
    
    def __init__(self):
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Gmail API."""
        creds = None
        
        # Migrate existing JSON token to pickle if it exists
        json_token_path = config.GMAIL_TOKEN_FILE.replace('.pickle', '.json')
        if os.path.exists(json_token_path) and not os.path.exists(config.GMAIL_TOKEN_FILE):
            try:
                creds = Credentials.from_authorized_user_file(json_token_path, config.GMAIL_SCOPES)
                logger.info(f"Migrating token from {json_token_path} to {config.GMAIL_TOKEN_FILE}")
                # Save as pickle and remove old JSON file
                with open(config.GMAIL_TOKEN_FILE, 'wb') as token_file:
                    pickle.dump(creds, token_file)
                os.remove(json_token_path)
                logger.info("Token migration completed successfully")
            except Exception as e:
                logger.warning(f"Failed to migrate token from JSON: {e}")
                creds = None
        
        # Load existing pickle token
        if os.path.exists(config.GMAIL_TOKEN_FILE):
            try:
                with open(config.GMAIL_TOKEN_FILE, 'rb') as token_file:
                    creds = pickle.load(token_file)
                logger.info(f"Loaded Gmail credentials from {config.GMAIL_TOKEN_FILE}")
            except Exception as e:
                logger.warning(f"Failed to load credentials: {e}")
                if os.path.exists(config.GMAIL_TOKEN_FILE):
                    os.remove(config.GMAIL_TOKEN_FILE)
                creds = None
        
        # Refresh or get new credentials
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                    logger.info("Gmail token refreshed successfully")
                except Exception as e:
                    logger.warning(f"Token refresh failed: {e}")
                    creds = None
            
            if not creds or not creds.valid:
                flow = InstalledAppFlow.from_client_secrets_file(
                    config.GMAIL_CREDENTIALS_FILE, config.GMAIL_SCOPES
                )
                creds = flow.run_local_server(port=8080, open_browser=True)
                logger.info("Completed Gmail authentication flow")
        
        # Save credentials using pickle
        with open(config.GMAIL_TOKEN_FILE, 'wb') as token_file:
            pickle.dump(creds, token_file)
        
        self.service = build('gmail', 'v1', credentials=creds)
        logger.info("Gmail service initialized successfully")
    
    def get_label_id(self, label_name: str) -> Optional[str]:
        """Get Gmail label ID by name."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return None
        try:
            results = self.service.users().labels().list(userId='me').execute()
            labels = results.get('labels', [])
            for label in labels:
                if label['name'].lower() == label_name.lower():
                    return label['id']
            logger.error(f"Label '{label_name}' not found")
            return None
        except Exception as e:
            logger.error(f"Error getting label ID: {e}")
            return None
    
    def fetch_emails_from_labels(self) -> List[EmailData]:
        """Fetch emails from configured labels, filtering for PDF attachments and X-Original-Sender, only from last 48 hours."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return []
        from datetime import datetime, timedelta
        all_emails = []
        now = datetime.utcnow()
        after = (now - timedelta(hours=48)).strftime('%Y/%m/%d')
        # Gmail query for attachments, PDF, and date range
        date_query = f"after:{after} has:attachment filename:pdf"
        for label_name in config.GMAIL_LABELS_TO_PROCESS:
            logger.info(f"Processing label: {label_name}")
            label_id = self.get_label_id(label_name)
            if not label_id:
                continue
            try:
                response = self.service.users().messages().list(
                    userId='me',
                    labelIds=[label_id],
                    q=date_query,
                    maxResults=config.MAX_GMAIL_RESULTS
                ).execute()
                messages = response.get('messages', [])
                logger.info(f"Found {len(messages)} emails in label '{label_name}'")
                for msg_ref in messages:
                    email_data = self._get_email_details(msg_ref['id'], label_name)
                    if not email_data:
                        continue
                    if not email_data.attachments or not any(att['filename'].lower().endswith('.pdf') for att in email_data.attachments):
                        continue
                    msg = self.service.users().messages().get(userId='me', id=msg_ref['id'], format='metadata', metadataHeaders=['X-Original-Sender']).execute()
                    headers = msg.get('payload', {}).get('headers', [])
                    x_original_sender = next((h['value'] for h in headers if h['name'].lower() == 'x-original-sender'), None)
                    if not x_original_sender:
                        continue
                    email_data.sender = x_original_sender
                    all_emails.append(email_data)
            except HttpError as e:
                logger.error(f"HTTP error fetching emails from {label_name}: {e}")
            except Exception as e:
                logger.error(f"Error fetching emails from {label_name}: {e}")
        return all_emails
    
    def _get_email_details(self, message_id: str, source_label: str) -> Optional[EmailData]:
        """Get detailed email information including attachments."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return None
        try:
            msg = self.service.users().messages().get(
                userId='me', 
                id=message_id, 
                format='full'
            ).execute()
            
            headers = msg['payload']['headers']
            subject = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No Subject')
            sender = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown Sender')
            date_str = next((h['value'] for h in headers if h['name'].lower() == 'date'), 'Unknown Date')
            
            email_body = ""
            attachments = []
            
            def parse_parts(parts):
                nonlocal email_body
                for part in parts:
                    mime_type = part.get('mimeType', '')
                    filename = part.get('filename', '')
                    body = part.get('body', {})
                    
                    # Handle PDF attachments
                    if body.get('attachmentId') and filename.lower().endswith('.pdf'):
                        attachments.append({
                            'id': body['attachmentId'],
                            'filename': filename,
                            'mime_type': mime_type
                        })
                    
                    # Extract email body text
                    elif mime_type == 'text/plain' and body.get('data') and not filename:
                        email_body = base64.urlsafe_b64decode(body['data']).decode('utf-8', errors='replace')
                    elif mime_type == 'text/html' and body.get('data') and not filename and not email_body:
                        html_content = base64.urlsafe_b64decode(body['data']).decode('utf-8', errors='replace')
                        soup = BeautifulSoup(html_content, 'html.parser')
                        email_body = soup.get_text(separator='\\n', strip=True)
                    
                    # Recursively parse nested parts
                    if 'parts' in part:
                        parse_parts(part['parts'])
            
            if 'parts' in msg['payload']:
                parse_parts(msg['payload']['parts'])
            else:
                parse_parts([msg['payload']])
            
            # Download PDF data for each attachment
            for att in attachments:
                try:
                    att_bytes = self.download_attachment(message_id, att['id'])
                    att['data'] = att_bytes
                except Exception as e:
                    logger.error(f"Failed to download attachment {att['filename']} for email {message_id}: {e}")
                    att['data'] = None
            
            return EmailData(
                id=message_id,
                subject=subject,
                sender=sender,
                timestamp=date_str,
                body=email_body.strip(),
                attachments=attachments,
                source_label=source_label
            )
            
        except Exception as e:
            logger.error(f"Error getting email details for {message_id}: {e}")
            return None
    
    def download_attachment(
        self, message_id: str, attachment_id: str
    ) -> Optional[bytes]:
        """
        Download the content of an email attachment from Gmail.

        Args:
            message_id (str): The ID of the email message containing the attachment.
            attachment_id (str): The ID of the attachment to download.

        Returns:
            Optional[bytes]: The decoded attachment data as bytes if successful, otherwise None.

        Raises:
            None directly, but logs and handles:
                - KeyError: If expected keys are missing in the response.
                - HttpError: If the Gmail API request fails.
                - binascii.Error: If decoding fails due to malformed data.
                - ValueError: If the attachment data is missing or invalid.
        """
        if not self.service:
            logger.error(
                "Gmail service is not initialized. Cannot download attachment. "
                f"message_id={message_id}, attachment_id={attachment_id}"
            )
            return None

        try:
            response = (
                self.service.users()
                .messages()
                .attachments()
                .get(userId="me", messageId=message_id, id=attachment_id)
                .execute()
            )
        except HttpError as http_err:
            logger.error(
                f"HttpError while fetching attachment: message_id={message_id}, "
                f"attachment_id={attachment_id}, error={http_err}"
            )
            return None
        except Exception as api_err:
            logger.error(
                f"Unexpected error while fetching attachment: message_id={message_id}, "
                f"attachment_id={attachment_id}, error={api_err}"
            )
            return None

        # Validate response and extract data
        if not isinstance(response, dict):
            logger.error(
                f"Attachment response is not a dict: message_id={message_id}, "
                f"attachment_id={attachment_id}, response_type={type(response)}"
            )
            return None

        data_key = "data"
        if data_key not in response:
            logger.error(
                f"Attachment data missing in response: message_id={message_id}, "
                f"attachment_id={attachment_id}, keys={list(response.keys())}"
            )
            return None

        encoded_data = response[data_key]
        if not isinstance(encoded_data, str) or not encoded_data.strip():
            logger.error(
                f"Attachment data is empty or not a string: message_id={message_id}, "
                f"attachment_id={attachment_id}, data_type={type(encoded_data)}"
            )
            return None

        try:
            decoded_bytes = base64.urlsafe_b64decode(encoded_data.encode("utf-8"))
            return decoded_bytes
        except (ValueError, binascii.Error) as decode_err:
            logger.error(
                f"Failed to decode attachment data: message_id={message_id}, "
                f"attachment_id={attachment_id}, error={decode_err}"
            )
            return None
    
    def mark_as_read(self, message_id: str) -> bool:
        """Mark email as read."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return False
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            logger.info(f"Marked email {message_id} as read")
            return True
        except Exception as e:
            logger.error(f"Error marking email {message_id} as read: {e}")
            return False
    
    def send_email(self, to: str, subject: str, body: str, from_addr: str = "") -> bool:
        """Send a basic email using the Gmail API."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return False
        from email.mime.text import MIMEText
        from base64 import urlsafe_b64encode
        try:
            message = MIMEText(body, 'plain')
            message['to'] = to
            message['from'] = from_addr or os.getenv('USER_EMAIL', 'me')
            message['subject'] = subject
            raw = urlsafe_b64encode(message.as_bytes()).decode()
            send_body = {'raw': raw}
            self.service.users().messages().send(userId='me', body=send_body).execute()
            logger.info(f"Email sent to {to} via Gmail API")
            return True
        except Exception as e:
            logger.error(f"Failed to send email to {to}: {e}")
            return False
    
    def create_label(self, label_name: str) -> Optional[str]:
        """Create a Gmail label if it doesn't exist and return its ID."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return None
        
        try:
            # Check if label already exists
            existing_label_id = self.get_label_id(label_name)
            if existing_label_id:
                logger.info(f"Label '{label_name}' already exists with ID: {existing_label_id}")
                return existing_label_id
            
            # Create new label
            label_object = {
                'name': label_name,
                'messageListVisibility': 'show',
                'labelListVisibility': 'labelShow'
            }
            
            result = self.service.users().labels().create(userId='me', body=label_object).execute()
            label_id = result['id']
            logger.info(f"Created new label '{label_name}' with ID: {label_id}")
            return label_id
            
        except Exception as e:
            logger.error(f"Error creating label '{label_name}': {e}")
            return None
    
    def add_label_to_email(self, message_id: str, label_name: str) -> bool:
        """Add a label to an email message."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return False
        
        try:
            # Get or create the label
            label_id = self.create_label(label_name)
            if not label_id:
                logger.error(f"Failed to get or create label '{label_name}'")
                return False
            
            # Add label to the message
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'addLabelIds': [label_id]}
            ).execute()
            
            logger.info(f"Added label '{label_name}' to email {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding label '{label_name}' to email {message_id}: {e}")
            return False
    
    def remove_label_from_email(self, message_id: str, label_name: str) -> bool:
        """Remove a label from an email message."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return False
        
        try:
            # Get the label ID
            label_id = self.get_label_id(label_name)
            if not label_id:
                logger.error(f"Label '{label_name}' not found")
                return False
            
            # Remove label from the message
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': [label_id]}
            ).execute()
            
            logger.info(f"Removed label '{label_name}' from email {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing label '{label_name}' from email {message_id}: {e}")
            return False
    
    def create_label_with_color(self, label_name: str, color: str) -> Optional[str]:
        """Create a Gmail label with specified color if it doesn't exist and return its ID."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return None
        
        try:
            # Check if label already exists
            existing_label_id = self.get_label_id(label_name)
            if existing_label_id:
                logger.info(f"Label '{label_name}' already exists with ID: {existing_label_id}")
                return existing_label_id
            
            # Try creating with Gmail's predefined colors first
            gmail_color_map = {
                'red': {'textColor': '#ffffff', 'backgroundColor': '#ac2b16'},      # Gmail Red
                'amber': {'textColor': '#000000', 'backgroundColor': '#ffbc6b'},    # Gmail Orange
                'green': {'textColor': '#ffffff', 'backgroundColor': '#16a765'}     # Gmail Green
            }
            
            # First try with Gmail predefined colors
            try:
                label_object = {
                    'name': label_name,
                    'messageListVisibility': 'show',
                    'labelListVisibility': 'labelShow',
                    'color': gmail_color_map.get(color, gmail_color_map['amber'])
                }
                
                result = self.service.users().labels().create(userId='me', body=label_object).execute()
                label_id = result['id']
                logger.info(f"Created new label '{label_name}' with {color} color and ID: {label_id}")
                return label_id
                
            except Exception as color_error:
                logger.warning(f"Failed to create label with color: {color_error}")
                # Fallback: create label without custom color
                label_object = {
                    'name': label_name,
                    'messageListVisibility': 'show',
                    'labelListVisibility': 'labelShow'
                }
                
                result = self.service.users().labels().create(userId='me', body=label_object).execute()
                label_id = result['id']
                logger.info(f"Created new label '{label_name}' (no custom color) with ID: {label_id}")
                return label_id
            
        except Exception as e:
            logger.error(f"Error creating label '{label_name}': {e}")
            return None
    
    def get_or_create_processing_labels(self) -> dict:
        """Ensure all processing status labels exist with traffic light colors and return their IDs."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return {}
        
        processing_labels = {
            'processed': {'name': 'Processed', 'color': 'green'},
            'review': {'name': 'Review', 'color': 'amber'},
            'failed': {'name': 'Failed', 'color': 'red'}
        }
        
        label_ids = {}
        for key, config in processing_labels.items():
            label_id = self.create_label_with_color(config['name'], config['color'])
            if label_id:
                label_ids[key] = label_id
            else:
                logger.error(f"Failed to create/get label: {config['name']}")
        
        logger.info(f"Processing labels ready: {list(label_ids.keys())}")
        return label_ids
    
    def mark_email_processed(self, message_id: str) -> bool:
        """Mark an email as successfully processed (green)."""
        return self.add_label_to_email(message_id, 'Processed')
    
    def mark_email_failed(self, message_id: str) -> bool:
        """Mark an email as failed to process (red)."""
        return self.add_label_to_email(message_id, 'Failed')
    
    def mark_email_review(self, message_id: str) -> bool:
        """Mark an email as needing review (amber)."""
        return self.add_label_to_email(message_id, 'Review')
    
    def has_processing_label(self, message_id: str) -> Optional[str]:
        """Check if an email already has a processing status label."""
        if not self.service:
            logger.error("Gmail service is not initialized. Authentication may have failed.")
            return None
        
        try:
            msg = self.service.users().messages().get(
                userId='me',
                id=message_id,
                format='metadata'
            ).execute()
            
            label_ids = msg.get('labelIds', [])
            
            # Get all processing labels
            processing_labels = ['Processed', 'Failed', 'Review']
            
            for label_name in processing_labels:
                label_id = self.get_label_id(label_name)
                if label_id and label_id in label_ids:
                    return label_name
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking processing labels for email {message_id}: {e}")
            return None
