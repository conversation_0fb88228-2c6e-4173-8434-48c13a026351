# 📧 Enhanced Email Dashboard - TeamsysV0.1

## Overview

The Enhanced Email Dashboard provides a comprehensive web-based interface for managing and processing emails in the TeamsysV0.1 system. It replaces the basic command-line workflow with a modern, user-friendly dashboard that offers real-time email management, advanced filtering, batch processing, and detailed analytics.

## 🌟 Key Features

### 📊 Real-time Email Management
- **Live Gmail Sync**: Automatically syncs emails from configured Gmail labels
- **Traffic Light Status System**: 🟢 Processed, 🟡 Review, 🔴 Failed, ⚪ Unprocessed
- **Batch Operations**: Process multiple emails simultaneously
- **Individual Management**: Handle emails one-by-one with detailed views

### 🔍 Advanced Filtering & Search
- **Status Filtering**: Filter by processing status
- **Label Filtering**: Filter by Gmail labels (Brady, RSEA, Woolworths, etc.)
- **Text Search**: Search by email subject or sender
- **Smart Pagination**: Handle large email volumes efficiently

### 📈 Analytics & Statistics
- **Processing Overview**: Total, processed, failed, review counts
- **Daily Trends**: Processing statistics over time
- **Label Breakdown**: Performance metrics by email source
- **Real-time Updates**: Live statistics as emails are processed

### 🗂️ File Management
- **Direct Downloads**: Download generated markdown and MYOB files
- **File Tracking**: Track which files were generated for each email
- **Integrated Viewing**: Preview email content and attachments

## 🚀 Getting Started

### Prerequisites
```bash
# Install required dependencies
pip install -r requirements.txt
```

### Quick Start

#### Option 1: Dashboard Only
```bash
# Start the email dashboard server
python run_dashboard.py
```

#### Option 2: Enhanced Processing + Dashboard
```bash
# Process emails and start dashboard
python enhanced_email_processor.py --start-dashboard

# Or process emails and update dashboard (without starting server)
python enhanced_email_processor.py
```

#### Option 3: Traditional Processing (maintains compatibility)
```bash
# Use existing workflow
python main_processor.py
```

### Accessing the Dashboard
Once started, access the dashboard at:
- **Local**: http://localhost:5000
- **Network**: http://[your-ip]:5000

## 📋 Dashboard Interface

### Main Dashboard Layout

```
┌─ Sidebar ─────────────────┐ ┌─ Main Content ─────────────────────┐
│                           │ │                                    │
│  📊 Statistics Cards       │ │  🔍 Search & Filters              │
│  ├─ Total Emails          │ │  ├─ Subject/Sender Search          │
│  ├─ Processed (🟢)        │ │  ├─ Select All/Clear               │
│  ├─ Review (🟡)           │ │  └─ Bulk Actions                   │
│  ├─ Failed (🔴)           │ │                                    │
│  └─ Unprocessed (⚪)       │ │  📋 Email List Table              │
│                           │ │  ├─ Checkbox Selection             │
│  🔧 Filters               │ │  ├─ Subject & Sender               │
│  ├─ Status Filter         │ │  ├─ Status Badges                  │
│  └─ Label Filter          │ │  ├─ Attachment Count               │
│                           │ │  └─ Action Buttons                 │
│  ⚡ Actions               │ │                                    │
│  ├─ Refresh Emails        │ │  📄 Pagination                    │
│  └─ Process Selected      │ │                                    │
└───────────────────────────┘ └────────────────────────────────────┘
```

### Email Status System

| Status | Badge | Description | Gmail Label |
|--------|-------|-------------|-------------|
| 🟢 Processed | `Processed` | Successfully processed and files generated | `Processed` |
| 🟡 Review | `Review` | Needs manual attention or review | `Review` |
| 🔴 Failed | `Failed` | Processing failed with errors | `Failed` |
| ⚪ Unprocessed | `Unprocessed` | Not yet processed | None |

## 🔧 API Endpoints

The dashboard provides RESTful API endpoints for integration:

### Email Management
- `GET /api/emails` - Get paginated email list with filters
- `GET /api/email/{id}` - Get detailed email information
- `POST /api/email/{id}/mark-status` - Update email status
- `POST /api/refresh-emails` - Sync emails from Gmail
- `POST /api/process-emails` - Process selected emails

### Statistics
- `GET /api/stats` - Get processing statistics

### File Downloads
- `GET /api/download/markdown/{filename}` - Download markdown file
- `GET /api/download/myob/{filename}` - Download MYOB JSON file

## 💾 Database Schema

The dashboard uses SQLite for local data storage:

### `emails` Table
```sql
CREATE TABLE emails (
    id TEXT PRIMARY KEY,           -- Gmail message ID
    subject TEXT NOT NULL,         -- Email subject
    sender TEXT,                   -- Email sender
    timestamp TEXT,                -- Email timestamp
    source_label TEXT,             -- Gmail label source
    status TEXT DEFAULT 'unprocessed', -- Processing status
    processed_at TEXT,             -- Processing timestamp
    markdown_file TEXT,            -- Generated markdown file path
    myob_file TEXT,               -- Generated MYOB file path
    error_message TEXT,           -- Error details if failed
    attachment_count INTEGER DEFAULT 0, -- Number of attachments
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### `processing_stats` Table
```sql
CREATE TABLE processing_stats (
    date TEXT PRIMARY KEY,         -- Date
    total_processed INTEGER DEFAULT 0,
    successful INTEGER DEFAULT 0,
    failed INTEGER DEFAULT 0,
    review INTEGER DEFAULT 0
);
```

## 🔄 Integration with Existing System

The dashboard seamlessly integrates with the existing TeamsysV0.1 email processing system:

### Compatibility
- ✅ **Fully Compatible**: Existing [`main_processor.py`](main_processor.py) continues to work
- ✅ **Enhanced Processing**: [`enhanced_email_processor.py`](enhanced_email_processor.py) adds dashboard updates
- ✅ **Gmail Integration**: Uses existing [`GmailService`](gmail_service.py) class
- ✅ **File Generation**: Same markdown and MYOB file outputs

### Migration Path
1. **Phase 1**: Use existing processing with dashboard viewing
2. **Phase 2**: Switch to enhanced processing for dashboard integration
3. **Phase 3**: Full dashboard-based workflow

## 🎯 Use Cases

### Daily Email Management
```bash
# Morning routine: Start dashboard and review overnight emails
python run_dashboard.py

# Access http://localhost:5000
# - Review unprocessed emails
# - Process urgent orders individually
# - Batch process routine emails
# - Monitor failed emails for issues
```

### Batch Processing
```bash
# Process all emails and update dashboard
python enhanced_email_processor.py

# Then access dashboard for review and management
python run_dashboard.py
```

### Monitoring & Analytics
- **Real-time Status**: Monitor processing status across all labels
- **Performance Tracking**: Identify which suppliers need attention
- **Error Analysis**: Review failed emails and error patterns
- **Historical Trends**: Track processing volume and success rates

## 🛠️ Configuration

### Gmail Labels
Configure which Gmail labels to monitor in [`config.py`](config.py):
```python
GMAIL_LABELS_TO_PROCESS = [
    'Brady', 'RSEA', 'Woolworths', 'Brierley', 
    'Gateway', 'Highgate', 'Sitecraft'
]
```

### Dashboard Settings
```python
# Dashboard runs on
HOST = '0.0.0.0'  # Accessible from network
PORT = 5000        # Standard port
DEBUG = False      # Production mode
```

## 🔒 Security Considerations

- **Local Network Only**: Dashboard runs on local network by default
- **Gmail OAuth**: Uses existing OAuth credentials
- **File Access**: Only serves files from designated directories
- **Database**: Local SQLite database for tracking

## 🚨 Troubleshooting

### Common Issues

#### Dashboard Won't Start
```bash
# Check Flask installation
pip install flask

# Check port availability
netstat -an | grep 5000
```

#### Gmail Sync Issues
```bash
# Verify Gmail credentials
python -c "from gmail_service import GmailService; GmailService()"

# Check labels exist
# Ensure Brady, RSEA, etc. labels exist in Gmail
```

#### Processing Errors
```bash
# Check logs
tail -f email_dashboard.log

# Verify dependencies
pip install -r requirements.txt
```

### Performance Optimization

#### Large Email Volumes
- Increase pagination: `per_page` parameter in API calls
- Filter by date range: Use Gmail date filters
- Archive old emails: Move processed emails to archive labels

#### Database Maintenance
```bash
# Backup database
cp email_dashboard.db email_dashboard.db.backup

# Compact database (if needed)
sqlite3 email_dashboard.db "VACUUM;"
```

## 🔄 Workflow Examples

### Scenario 1: Morning Email Review
1. **Start Dashboard**: `python run_dashboard.py`
2. **Refresh Emails**: Click "Refresh Emails" button
3. **Review Unprocessed**: Filter by "Unprocessed" status
4. **Batch Process**: Select multiple emails and click "Process Selected"
5. **Monitor Results**: Watch status updates in real-time
6. **Handle Failures**: Review any failed emails for issues

### Scenario 2: Automated Processing
1. **Enhanced Processing**: `python enhanced_email_processor.py`
2. **Dashboard Update**: Automatic database sync
3. **Email Notification**: Receive completion email with dashboard link
4. **Review Results**: Access dashboard for detailed review

### Scenario 3: Individual Email Management
1. **Search Email**: Use search bar to find specific email
2. **View Details**: Click on email subject for full details
3. **Process Individual**: Use dropdown actions to process single email
4. **Download Files**: Click download buttons for generated files
5. **Update Status**: Manually mark status if needed

## 📊 Advanced Features

### Custom Filters
```javascript
// API call for custom filtering
fetch('/api/emails?' + new URLSearchParams({
    status: 'failed',
    label: 'Brady',
    search: 'urgent'
}))
```

### Bulk Operations
```javascript
// Process multiple emails
fetch('/api/process-emails', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({email_ids: ['id1', 'id2', 'id3']})
})
```

### Real-time Updates
The dashboard provides real-time updates through:
- Automatic status refreshing
- Live statistics updates
- Instant feedback on operations

## 🎨 Customization

### Theme Colors
Modify the status colors in the dashboard template:
```css
.status-processed { background-color: #198754; }  /* Green */
.status-review { background-color: #fd7e14; }     /* Orange */
.status-failed { background-color: #dc3545; }     /* Red */
.status-unprocessed { background-color: #6c757d; } /* Gray */
```

### Custom Labels
Add new Gmail labels to monitor:
1. Create label in Gmail
2. Add to `GMAIL_LABELS_TO_PROCESS` in config
3. Update dashboard label filter dropdown

## 📈 Future Enhancements

### Planned Features
- 📱 **Mobile Responsive Design**: Better mobile/tablet support
- 🔔 **Real-time Notifications**: Browser notifications for new emails
- 📊 **Advanced Analytics**: More detailed charts and trends
- 🔄 **Auto-refresh**: Automatic email sync on schedule
- 🎯 **Smart Filtering**: AI-powered email categorization
- 📤 **Export Functions**: Export data to CSV/Excel
- 👥 **Multi-user Support**: Role-based access control

### Integration Opportunities
- **MYOB Direct Posting**: Post directly from dashboard to MYOB
- **Webhook Support**: Real-time notifications to external systems
- **API Extensions**: Full REST API for external integrations
- **Slack Integration**: Status updates to Slack channels

## 📞 Support

For issues or questions:
1. Check this README for common solutions
2. Review the troubleshooting section
3. Check the application logs
4. Verify Gmail and system configurations

## 🏆 Benefits Summary

| Feature | Before | After |
|---------|--------|-------|
| **Email Visibility** | Command-line only | Web dashboard with visual status |
| **Processing Control** | Batch all-or-nothing | Individual + batch selection |
| **Status Tracking** | Gmail labels only | Database + visual indicators |
| **File Management** | Manual file browsing | Direct download links |
| **Error Handling** | Log files only | Visual error display + details |
| **Analytics** | None | Real-time statistics + trends |
| **User Experience** | Technical users only | Business user friendly |

The Enhanced Email Dashboard transforms TeamsysV0.1 from a technical email processing script into a comprehensive business tool for email order management.