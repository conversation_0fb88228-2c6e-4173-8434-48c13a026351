#!/usr/bin/env python3
"""
Setup utility for the Email Order Processor.
This script helps with initial configuration and setup tasks.
"""

import os
import shutil
from pathlib import Path

def create_env_file():
    """Create .env file from .env.example if it doesn't exist"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    try:
        shutil.copy2(env_example, env_file)
        print("✅ Created .env file from .env.example")
        print("📝 Please edit .env file with your actual configuration values")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def check_credentials():
    """Check if Gmail credentials file exists"""
    creds_file = Path('credentials.json')
    
    if creds_file.exists():
        print("✅ credentials.json found")
        return True
    
    print("❌ credentials.json not found")
    print("📋 To set up Gmail API credentials:")
    print("   1. Go to https://console.cloud.google.com/")
    print("   2. Create a new project or select existing")
    print("   3. Enable Gmail API")
    print("   4. Create OAuth 2.0 credentials (Desktop application)")
    print("   5. Download and save as 'credentials.json'")
    return False

def install_dependencies():
    """Install Python dependencies"""
    import subprocess
    import sys
    
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt not found")
        return False

def validate_env_vars():
    """Validate that required environment variables are set"""
    from dotenv import load_dotenv
    
    if not Path('.env').exists():
        print("❌ .env file not found - run setup first")
        return False
    
    load_dotenv()
    
    required_vars = {
        'GEMINI_API_KEY': 'Google Gemini API key',
        'EXO_IP': 'MYOB EXO server IP',
        'EXO_PORT': 'MYOB EXO server port',
        'USER': 'MYOB API username',
        'PWD': 'MYOB API password',
        'API_KEY': 'MYOB API key',
        'EXO_TOK': 'MYOB EXO token',
        'GMAIL_LABELS_TO_PROCESS': 'Gmail labels to monitor'
    }
    
    missing_vars = []
    placeholder_vars = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} ({description})")
        elif value.startswith('your_') or value in ['your_api_key_here', 'your_server_ip']:
            placeholder_vars.append(f"{var} ({description})")
    
    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
    
    if placeholder_vars:
        print("⚠️  Placeholder values found (please update):")
        for var in placeholder_vars:
            print(f"   - {var}")
    
    if not missing_vars and not placeholder_vars:
        print("✅ All required environment variables are configured")
        return True
    
    return False

def show_gmail_setup_instructions():
    """Show detailed Gmail API setup instructions"""
    print("\n📧 Gmail API Setup Instructions:")
    print("=" * 50)
    print("1. Go to Google Cloud Console: https://console.cloud.google.com/")
    print("2. Create a new project or select an existing one")
    print("3. Enable the Gmail API:")
    print("   - Go to 'APIs & Services' > 'Library'")
    print("   - Search for 'Gmail API'")
    print("   - Click 'Enable'")
    print("4. Create credentials:")
    print("   - Go to 'APIs & Services' > 'Credentials'")
    print("   - Click 'Create Credentials' > 'OAuth 2.0 Client IDs'")
    print("   - Choose 'Desktop application'")
    print("   - Download the JSON file")
    print("   - Save it as 'credentials.json' in this directory")
    print("5. Configure OAuth consent screen if prompted")

def show_gemini_setup_instructions():
    """Show Gemini API setup instructions"""
    print("\n🤖 Gemini API Setup Instructions:")
    print("=" * 50)
    print("1. Go to Google AI Studio: https://aistudio.google.com/app/apikey")
    print("2. Sign in with your Google account")
    print("3. Click 'Create API Key'")
    print("4. Copy the API key")
    print("5. Add it to your .env file as GEMINI_API_KEY=your_api_key_here")

def show_myob_setup_instructions():
    """Show MYOB setup instructions"""
    print("\n💼 MYOB EXO API Setup Instructions:")
    print("=" * 50)
    print("1. Ensure MYOB EXO API is enabled on your server")
    print("2. Obtain the following from your MYOB administrator:")
    print("   - Server IP address")
    print("   - API port (usually 8080)")
    print("   - API username and password")
    print("   - API key")
    print("   - EXO token")
    print("3. Add these values to your .env file")
    print("4. Ensure your server is accessible from this machine")

def main():
    """Main setup workflow"""
    print("🚀 Email Order Processor - Setup Utility")
    print("=" * 50)
    
    steps = [
        ("Create environment file", create_env_file),
        ("Check Gmail credentials", check_credentials),
        ("Install dependencies", install_dependencies),
        ("Validate environment variables", validate_env_vars)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"\n⚠️  Setup incomplete. Please address the issues above.")
            
            # Show relevant setup instructions
            if "credentials" in step_name.lower():
                show_gmail_setup_instructions()
            elif "environment" in step_name.lower():
                show_gemini_setup_instructions()
                show_myob_setup_instructions()
            
            return 1
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Edit .env file with your actual API keys and configuration")
    print("2. Ensure credentials.json is in place")
    print("3. Run: python test_setup.py")
    print("4. If tests pass, run: python email_order_processor.py")
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
