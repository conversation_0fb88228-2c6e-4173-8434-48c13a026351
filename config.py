"""
Configuration module for email order processor.
Loads environment variables and sets up application constants.
"""

import os
import base64
import logging
import colorlog
from dotenv import load_dotenv

# Load environment variables
load_dotenv(dotenv_path=".env")

# Logging Configuration
log_level_str = os.getenv("LOG_LEVEL", "INFO").upper()
log_level = getattr(logging, log_level_str, logging.INFO)
handler = colorlog.StreamHandler()
handler.setFormatter(
    colorlog.ColoredFormatter(
        "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
)
logging.root.handlers = [handler]
logging.root.setLevel(log_level)


class Config:
    """Configuration settings loaded from environment variables"""

    # Gmail API Configuration
    GMAIL_SCOPES = [
        "https://www.googleapis.com/auth/gmail.readonly",
        "https://www.googleapis.com/auth/gmail.send",
        "https://www.googleapis.com/auth/gmail.modify",
    ]
    GMAIL_CREDENTIALS_FILE = os.getenv("GMAIL_CREDENTIALS_FILE", "credentials.json")
    GMAIL_TOKEN_FILE = os.getenv("GMAIL_TOKEN_FILE", "token.pickle")
    # Parse labels as a list, not a string
    GMAIL_LABELS_TO_PROCESS = [
        label.strip()
        for label in os.getenv("GMAIL_LABELS_TO_PROCESS", "").split(",")
        if label.strip()
    ]
    GMAIL_UNREAD_ONLY_FLAG = os.getenv("GMAIL_UNREAD_ONLY", "False").lower() == "true"
    GMAIL_QUERY_FILTER_BASE = "has:attachment filename:pdf"
    GMAIL_QUERY_FILTER = os.getenv(
        "GMAIL_QUERY_FILTER",
    )
    MAX_GMAIL_RESULTS = int(
        os.getenv("MAX_GMAIL_RESULTS", "100")
    )  # Set to 100 for Brady processing

    # No date filtering for Brady emails - get all emails
    BRADY_DATE_FILTER = ""  # No date filter

    # Gemini LLM Configuration (Google Gen AI SDK 2025)
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.5-flash")

    # MYOB API Configuration
    EXO_IP = os.getenv("EXO_IP")
    EXO_PORT = os.getenv("EXO_PORT")
    USER = os.getenv("USER")
    PWD = os.getenv("PWD")
    API_KEY = os.getenv("API_KEY")
    EXO_TOK = os.getenv("EXO_TOK")

    @property
    def MYOB_BASE_URL(self):
        return f"http://{self.EXO_IP}:{self.EXO_PORT}"

    @property
    def MYOB_HEADERS(self):
        auth_string = f"{self.USER}:{self.PWD}"
        base64_auth = base64.b64encode(auth_string.encode()).decode()
        return {
            "Authorization": f"Basic {base64_auth}",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "x-myobapi-key": self.API_KEY,
            "x-myobapi-exoToken": self.EXO_TOK,
        }

    def validate_config(self):
        """Validate that all required configuration is present."""
        required_myob_vars = [
            self.EXO_IP,
            self.EXO_PORT,
            self.USER,
            self.PWD,
            self.API_KEY,
            self.EXO_TOK,
        ]
        if not all(required_myob_vars):
            raise ValueError("Missing one or more MYOB API configuration variables")

        if not self.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is not set")

        if not os.path.exists(self.GMAIL_CREDENTIALS_FILE):
            raise FileNotFoundError(
                f"Gmail credentials file '{self.GMAIL_CREDENTIALS_FILE}' not found"
            )


# Global config instance
config = Config()
