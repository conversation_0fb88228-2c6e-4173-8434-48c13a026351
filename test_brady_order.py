"""
Test script to validate one Brady order using minimal MYOB API validation approach.
"""
import random
import logging
from myob_service import MyobService

# Set up logging to see detailed info
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def generate_unique_order_number(prefix="BRADY"):
    """Generate a unique order number for testing."""
    return f"{prefix}{random.randint(100000, 999999)}"

def create_brady_test_payload():
    """Create a minimal Brady test payload."""
    return {
        "debtorid": 5760,  # Brady debtor ID
        "customerordernumber": generate_unique_order_number(),
        "status": 3,  # Quotation status
        "deliveryaddress": {
            "line1": "1 Brady Test Street",
            "line2": "Test Suburb", 
            "line3": "NSW",
            "line4": "2000",
            "line5": "AU"
        },
        "lines": [
            {
                "stockcode": "AC25-685",  # Brady stock code
                "orderquantity": 1.0
            }
        ],
        "extrafields": [
            {
                "key": "X_SHIPVIA",
                "value": "BEST WAY"
            }
        ]
    }

def test_brady_order():
    """Test creating a single Brady order using minimal validation approach."""
    print("🧪 TESTING BRADY ORDER WITH MINIMAL VALIDATION APPROACH")
    print("=" * 60)
    
    service = MyobService()
    payload = create_brady_test_payload()
    
    print(f"📦 Testing Brady order with PO: {payload['customerordernumber']}")
    print(f"🏪 Debtor ID: {payload['debtorid']}")
    print(f"📋 Stock Code: {payload['lines'][0]['stockcode']}")
    print(f"📦 Quantity: {payload['lines'][0]['orderquantity']}")
    print()
    
    try:
        # Step 1: Validate order with minimal fields
        print("🔍 STEP 1: Validating order with MYOB...")
        validation_result = service.validate_order(payload)
        
        if not validation_result or 'order' not in validation_result:
            print("❌ Validation failed. Check logs for details.")
            return False
            
        print("✅ Order validation successful!")
        validated_payload = validation_result['order']
        
        # Step 2: Create order using validated payload
        print("\n🏗️ STEP 2: Creating order with validated payload...")
        result = service.create_order(validated_payload)
        
        if result:
            order_id = result.get('id', 'Unknown')
            print(f"✅ Brady order created successfully!")
            print(f"📋 Order ID: {order_id}")
            print(f"📋 Customer PO: {payload['customerordernumber']}")
            return True
        else:
            print("❌ Order creation failed. Check logs for details.")
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

if __name__ == "__main__":
    success = test_brady_order()
    if success:
        print("\n🎉 Brady order test completed successfully!")
    else:
        print("\n💥 Brady order test failed!")
