#!/usr/bin/env python3
"""
Simple demo showing how to use the email labeling functionality.
"""
from gmail_service import GmailService

def demo_email_labeling():
    """Demonstrate the simplified traffic light email labeling."""
    print("🚦 Email Labeling Demo - Traffic Light System")
    print("=" * 50)
    
    # Initialize Gmail service
    gmail = GmailService()
    
    # Setup traffic light labels
    print("Creating traffic light labels...")
    labels = gmail.get_or_create_processing_labels()
    
    print(f"✅ Labels ready:")
    print(f"   🟢 Processed (green): Success")
    print(f"   🟡 Review (amber):   Needs attention")  
    print(f"   🔴 Failed (red):     Error occurred")
    
    print(f"\n📧 Available methods for integration:")
    print(f"   gmail.mark_email_processed(email_id)")
    print(f"   gmail.mark_email_review(email_id)")
    print(f"   gmail.mark_email_failed(email_id)")
    print(f"   gmail.has_processing_label(email_id)")

if __name__ == "__main__":
    demo_email_labeling()