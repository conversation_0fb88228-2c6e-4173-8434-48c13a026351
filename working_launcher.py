#!/usr/bin/env python3
"""
Working launcher for TeamsysV0.1 using root-level imports.
"""
import sys
import os

def test_imports():
    """Test that all imports work."""
    print("🧪 Testing Working Imports...")
    print("=" * 40)
    
    try:
        import config
        print("✅ config.py imported")
        print(f"   Gmail model: {config.config.GEMINI_MODEL}")
    except Exception as e:
        print(f"❌ config.py failed: {e}")
        return False
    
    try:
        import models
        print("✅ models.py imported")
        
        # Test model creation
        customer = models.CustomerDetails(debtor_id=6207)
        print(f"   Created customer: {customer.debtor_id}")
    except Exception as e:
        print(f"❌ models.py failed: {e}")
        return False
    
    try:
        import gmail_service
        print("✅ gmail_service.py imported")
    except Exception as e:
        print(f"❌ gmail_service.py failed: {e}")
        return False
    
    try:
        import llm_service
        print("✅ llm_service.py imported")
    except Exception as e:
        print(f"❌ llm_service.py failed: {e}")
        return False
    
    try:
        import myob_service
        print("✅ myob_service.py imported")
    except Exception as e:
        print(f"❌ myob_service.py failed: {e}")
        return False
    
    print("\n🎉 All imports working!")
    return True

def start_dashboard():
    """Start the email dashboard."""
    try:
        import email_dashboard
        print("🚀 Starting Email Dashboard...")
        dashboard = email_dashboard.EmailDashboard()
        dashboard.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Dashboard failed: {e}")
        import traceback
        traceback.print_exc()

def start_processor():
    """Start the main processor."""
    try:
        import main_processor
        print("🚀 Starting Main Processor...")
        processor = main_processor.EmailOrderProcessor()
        processor.run()
    except Exception as e:
        print(f"❌ Processor failed: {e}")
        import traceback
        traceback.print_exc()

def start_demo():
    """Start demo dashboard."""
    try:
        print("🚀 Starting Demo Dashboard...")
        import demo_dashboard
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Working TeamsysV0.1 Launcher')
    parser.add_argument('action', choices=['test', 'dashboard', 'processor', 'demo'], 
                       help='Action to perform')
    
    args = parser.parse_args()
    
    if args.action == 'test':
        success = test_imports()
        if success:
            print("\n✅ System is working! You can now run:")
            print("   python working_launcher.py dashboard")
            print("   python working_launcher.py processor") 
            print("   python working_launcher.py demo")
    elif args.action == 'dashboard':
        start_dashboard()
    elif args.action == 'processor':
        start_processor()
    elif args.action == 'demo':
        start_demo()
