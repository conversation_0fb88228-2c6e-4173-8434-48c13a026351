#!/usr/bin/env python3
"""
Main entry point for the restructured TeamsysV0.1 system.
Provides easy access to all major components with the new package structure.
"""
import sys
import argparse
from pathlib import Path

# Add current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

def main():
    parser = argparse.ArgumentParser(description='TeamsysV0.1 - Restructured Email Processing System')
    parser.add_argument('component', choices=[
        'system', 'dashboard', 'processor', 'enhanced', 'universal', 'polling'
    ], help='Component to run')
    parser.add_argument('--demo', action='store_true', help='Run in demo mode')
    parser.add_argument('--port', type=int, default=5000, help='Port for dashboard (default: 5000)')
    
    args = parser.parse_args()
    
    try:
        if args.component == 'system':
            from orchestrators.comprehensive_email_system import ComprehensiveEmailSystem
            print("🚀 Starting Comprehensive Email System...")
            system = ComprehensiveEmailSystem()
            system.start_system()
            
        elif args.component == 'dashboard':
            if args.demo:
                print("📊 Starting Dashboard in Demo Mode...")
                import demo_dashboard
            else:
                from orchestrators.email_dashboard import EmailDashboard
                print(f"📊 Starting Email Dashboard on port {args.port}...")
                dashboard = EmailDashboard()
                dashboard.run(host='0.0.0.0', port=args.port)
                
        elif args.component == 'processor':
            from orchestrators.main_processor import EmailOrderProcessor
            print("⚙️ Starting Main Email Processor...")
            processor = EmailOrderProcessor()
            processor.run()
            
        elif args.component == 'enhanced':
            from orchestrators.enhanced_email_processor import EnhancedEmailProcessor
            print("✨ Starting Enhanced Email Processor...")
            processor = EnhancedEmailProcessor()
            processor.run()
            
        elif args.component == 'universal':
            from agents.enhanced_universal_agent import EnhancedUniversalAgent
            print("🤖 Starting Universal Agent...")
            agent = EnhancedUniversalAgent()
            agent.run()
            
        elif args.component == 'polling':
            from agents.continuous_polling_agent import ContinuousPollingAgent
            print("🔄 Starting Continuous Polling Agent...")
            agent = ContinuousPollingAgent()
            agent.start()
            
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure all dependencies are installed and the project structure is correct.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("🎯 TeamsysV0.1 - Restructured Email Processing System")
    print("=" * 50)
    main()