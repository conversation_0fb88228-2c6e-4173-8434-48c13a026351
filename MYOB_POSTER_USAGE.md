# MYOB Poster Usage Guide

The MYOB poster has been successfully recreated with enhanced validation and improved functionality based on `myob_poster_old.py`.

## Features

✅ **Order Validation** - Validates orders before sending to MYOB
✅ **Enhanced Error Handling** - Comprehensive error reporting
✅ **Interactive Review Mode** - Manual review and approval process
✅ **Batch Processing** - Process multiple orders automatically
✅ **Email Notifications** - Completion notifications via email
✅ **Comprehensive Logging** - Detailed logging for troubleshooting

## Usage Options

### 1. Interactive Mode (Recommended)
```bash
python myob_poster.py
```
- Review orders one by one
- Manual approval before posting
- Best for careful review and validation

### 2. Batch Processing
```bash
python myob_poster.py batch
```
- Process all orders in the myob directory
- Automatic validation and posting
- Email summary when complete
- Best for bulk processing

### 3. Single Order Processing
```bash
python myob_poster.py single "order_id"
```
- Process a specific order by ID
- Direct validation and posting
- Best for testing or specific orders

## Order Processing Flow

1. **Load Order** - Reads JSON file from myob directory
2. **Structure Validation** - Validates required fields and data types
3. **MYOB API Validation** - Validates with MYOB service
4. **Order Creation** - Posts validated order to MYOB
5. **Confirmation** - Reports success/failure with details

## Validation Checks

### Structure Validation
- Required fields: `debtorid`, `status`, `lines`
- Valid debtor ID (positive integer)
- Valid status (1-5)
- Non-empty order lines with stockcode and quantity
- Valid delivery address format (if present)

### MYOB API Validation
- Stock code verification
- Customer validation
- Price and availability checks
- Business rule validation

## Order Directory Structure

Orders are pulled from the `myob/` directory:
```
myob/
├── order1.json
├── order2.json
└── order3.json
```

Each JSON file contains a complete order structure:
```json
{
  "debtorid": 6207,
  "status": 3,
  "lines": [
    {
      "stockcode": "IH051R2",
      "orderquantity": 10.0
    }
  ],
  "customerordernumber": "1880782",
  "deliveryaddress": {
    "line1": "3B/175 Blair Street",
    "line2": "Bunbury",
    "line3": "WA",
    "line4": "6230"
  },
  "extrafields": [
    {
      "key": "X_SHIPVIA",
      "value": "DIRECT FREIGHT EXPRESS"
    }
  ]
}
```

## Error Handling

The poster provides detailed error reporting:
- **Structure errors**: Missing fields, invalid data types
- **MYOB errors**: API validation failures, connection issues
- **Processing errors**: File access, JSON parsing issues

## Email Notifications

Batch processing sends completion emails with:
- Total orders processed
- Success/failure counts
- Detailed results for each order
- Configure `LOG_RECIPIENT_EMAIL` in environment

## Logging

Comprehensive logging includes:
- Order loading and validation
- MYOB API interactions
- Success/failure details
- Error diagnostics

Log level can be configured in the script initialization.

## Demo Script

Run the demo to see functionality:
```bash
python demo_myob_poster.py
```

This shows order review, validation, and batch processing information.

## Migration from Old Version

The new poster maintains compatibility with the old version while adding:
- Enhanced validation before MYOB submission
- Better error handling and reporting
- Improved user interface
- More flexible usage options

All existing order files in the myob directory will work with the new version.