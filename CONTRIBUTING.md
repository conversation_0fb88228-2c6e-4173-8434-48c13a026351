# Contributing to TeamsysV0.1

Thank you for your interest in contributing to TeamsysV0.1! This guide will help you get started with contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Code Standards](#code-standards)
- [Testing](#testing)
- [Documentation](#documentation)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)

## Code of Conduct

By participating in this project, you agree to maintain a respectful and inclusive environment for all contributors.

## Getting Started

### Prerequisites

- Python 3.8 or higher
- Git
- Access to Gmail API, Gemini AI, and MYOB EXO (for testing)

### Development Setup

1. **Fork and Clone**
```bash
git clone https://github.com/yourusername/TeamsysV0.1.git
cd TeamsysV0.1
```

2. **Set Up Development Environment**
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -r requirements_comprehensive.txt
pip install -r requirements_dev.txt  # If available
```

3. **Configure Environment**
```bash
# Copy and configure environment
cp env_template.txt .env
# Edit .env with your development credentials
```

4. **Run Setup**
```bash
python setup.py
```

5. **Verify Installation**
```bash
python test_system.py
```

## Contributing Guidelines

### Types of Contributions

We welcome several types of contributions:

- **Bug Fixes**: Fix issues in existing functionality
- **Feature Enhancements**: Improve existing features
- **New Features**: Add new capabilities
- **Documentation**: Improve or add documentation
- **Testing**: Add or improve test coverage
- **Performance**: Optimize system performance
- **Security**: Enhance security measures

### Before You Start

1. **Check Existing Issues**: Look for existing issues or discussions
2. **Create an Issue**: For new features or significant changes
3. **Discuss Approach**: Comment on the issue to discuss your approach
4. **Fork Repository**: Create your own fork for development

## Code Standards

### Python Code Style

We follow PEP 8 with some project-specific conventions:

```python
# Use type hints for all function parameters and returns
def process_email(email_data: EmailData) -> ProcessedOrder:
    """Process email and return structured order data."""
    pass

# Use descriptive variable names
customer_debtor_id = 6207  # Good
cid = 6207  # Avoid

# Use docstrings for all classes and functions
class EmailProcessor:
    """
    Processes emails for order extraction.
    
    This class handles the complete email processing pipeline
    from Gmail fetching to MYOB order creation.
    """
    
    def __init__(self, config: Config):
        """Initialize processor with configuration."""
        self.config = config
```

### Code Organization

#### File Structure
```
TeamsysV0.1/
├── core/                   # Core business logic
│   ├── models.py          # Data models
│   ├── config.py          # Configuration
│   └── exceptions.py      # Custom exceptions
├── services/              # External service integrations
│   ├── gmail_service.py   # Gmail API
│   ├── llm_service.py     # AI processing
│   └── myob_service.py    # MYOB API
├── processors/            # Processing logic
│   ├── email_processor.py # Email processing
│   └── order_processor.py # Order processing
├── dashboard/             # Web interface
│   ├── app.py            # Flask application
│   └── templates/        # HTML templates
├── tests/                 # Test files
├── docs/                  # Documentation
└── scripts/              # Utility scripts
```

#### Import Organization
```python
# Standard library imports
import os
import json
import logging
from typing import List, Optional, Dict, Any

# Third-party imports
import requests
from pydantic import BaseModel
from flask import Flask, render_template

# Local imports
from config import config
from models import EmailData, ExtractedOrder
from services.gmail_service import GmailService
```

### Naming Conventions

- **Classes**: PascalCase (`EmailProcessor`, `MyobService`)
- **Functions/Methods**: snake_case (`process_email`, `validate_order`)
- **Variables**: snake_case (`email_data`, `order_lines`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`, `DEFAULT_TIMEOUT`)
- **Files**: snake_case (`email_service.py`, `order_processor.py`)

### Error Handling

```python
# Use specific exception types
try:
    order = myob_service.create_order(payload)
except MyobServiceError as e:
    logger.error(f"MYOB order creation failed: {e}")
    raise
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise

# Provide meaningful error messages
if not email_data.attachments:
    raise ValueError("Email must contain at least one PDF attachment")
```

### Logging

```python
import logging

logger = logging.getLogger(__name__)

# Use appropriate log levels
logger.debug("Processing email with ID: %s", email_id)
logger.info("Successfully processed %d emails", count)
logger.warning("Retrying failed operation: %s", operation)
logger.error("Failed to connect to MYOB: %s", error)
logger.critical("System shutdown due to critical error: %s", error)
```

## Testing

### Test Structure

```python
import unittest
from unittest.mock import Mock, patch
from models import EmailData, ExtractedOrder
from services.email_processor import EmailProcessor

class TestEmailProcessor(unittest.TestCase):
    """Test cases for EmailProcessor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = EmailProcessor()
        self.sample_email = EmailData(
            id="test123",
            subject="Test Order",
            sender="<EMAIL>",
            timestamp="2024-01-01T10:00:00Z",
            body="Test email body",
            source_label="Test"
        )
    
    def test_process_valid_email(self):
        """Test processing of valid email."""
        result = self.processor.process_email(self.sample_email)
        self.assertIsInstance(result, ProcessedOrder)
        self.assertEqual(result.email_id, "test123")
    
    @patch('services.myob_service.MyobService.create_order')
    def test_myob_integration(self, mock_create):
        """Test MYOB service integration."""
        mock_create.return_value = {"id": "order123"}
        # Test implementation
```

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_email_processor.py

# Run with coverage
python -m pytest --cov=. tests/

# Run integration tests
python -m pytest tests/integration/
```

### Test Categories

1. **Unit Tests**: Test individual functions and classes
2. **Integration Tests**: Test component interactions
3. **End-to-End Tests**: Test complete workflows
4. **Performance Tests**: Test system performance
5. **Security Tests**: Test security measures

## Documentation

### Code Documentation

```python
def extract_order_data(self, email_data: EmailData) -> ExtractedOrder:
    """
    Extract structured order data from email content.
    
    Uses AI processing to analyze email content and attachments,
    extracting customer information, order lines, and delivery details.
    
    Args:
        email_data (EmailData): Email content and metadata
        
    Returns:
        ExtractedOrder: Structured order information
        
    Raises:
        LLMServiceError: If AI processing fails
        ValidationError: If extracted data is invalid
        
    Example:
        >>> processor = EmailProcessor()
        >>> email = EmailData(...)
        >>> order = processor.extract_order_data(email)
        >>> print(f"Customer: {order.customer_details.debtor_id}")
    """
```

### README Updates

When adding new features, update relevant documentation:

- Main README.md for user-facing changes
- API_DOCUMENTATION.md for API changes
- ARCHITECTURE.md for structural changes
- Component-specific README files

### Documentation Standards

- Use clear, concise language
- Include code examples
- Provide troubleshooting information
- Keep documentation up-to-date with code changes

## Pull Request Process

### Before Submitting

1. **Test Your Changes**
```bash
# Run tests
python -m pytest tests/

# Test specific functionality
python test_system.py

# Run demo scripts
python demo_llm.py
python demo_dashboard.py
```

2. **Update Documentation**
- Update relevant README files
- Add docstrings to new functions/classes
- Update API documentation if needed

3. **Check Code Quality**
```bash
# Format code (if using black)
black .

# Check style (if using flake8)
flake8 .

# Type checking (if using mypy)
mypy .
```

### Pull Request Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Documentation updated

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Comments added for complex logic
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

### Review Process

1. **Automated Checks**: CI/CD pipeline runs tests
2. **Code Review**: Maintainers review code quality
3. **Testing**: Reviewers test functionality
4. **Documentation**: Check documentation completeness
5. **Approval**: Maintainer approval required for merge

## Issue Reporting

### Bug Reports

Use this template for bug reports:

```markdown
## Bug Description
Clear description of the bug.

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen.

## Actual Behavior
What actually happens.

## Environment
- Python version:
- Operating System:
- TeamsysV0.1 version:

## Logs
```
Relevant log output
```

## Additional Context
Any other relevant information.
```

### Feature Requests

```markdown
## Feature Description
Clear description of the proposed feature.

## Use Case
Why is this feature needed?

## Proposed Solution
How should this feature work?

## Alternatives Considered
Other approaches considered.

## Additional Context
Any other relevant information.
```

## Development Workflow

### Branch Naming

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Commit Messages

```
type(scope): description

Examples:
feat(gmail): add label creation functionality
fix(myob): resolve order validation error
docs(api): update service documentation
refactor(models): improve data validation
```

### Release Process

1. **Version Bump**: Update version numbers
2. **Changelog**: Update CHANGELOG.md
3. **Testing**: Comprehensive testing
4. **Documentation**: Update documentation
5. **Tag Release**: Create git tag
6. **Deploy**: Deploy to production (if applicable)

## Getting Help

### Resources

- **Documentation**: Check existing documentation first
- **Issues**: Search existing issues for similar problems
- **Discussions**: Use GitHub discussions for questions
- **Code Review**: Ask for code review feedback

### Contact

- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Email**: [Contact information if available]

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation

Thank you for contributing to TeamsysV0.1!