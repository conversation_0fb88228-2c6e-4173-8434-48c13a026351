{"email": {"id": "197c854fbb0da64b", "headers": {"Delivered-To": "<EMAIL>", "Received": "from WOOLWORTHS.COM.AU (unknown [***********]) by wowsmtpgwmel2.woolworths.com.au (MTA) with ESMTP id 4bWznN4mBdzBWWTb; Wed,  2 Jul 2025 09:31:36 +1000 (AEST)", "X-Forwarded-Encrypted": "i=2; AJvYcCXNqyIw6Ja2jZyzRLIYlWrfCmlO/acejLale/cel9Z9TrEJR90eQqKBaOV5+61N2LbuTxwiqA==@teamsystems.net.au", "X-Google-Smtp-Source": "AGHT+IHXecrOWZ+h+MO7qy6uxDUR1aD86ogN1P6Fn4zv77HvWK4GZ+rHrgeXtkVXcY8+J7iDorJJ", "X-Received": "by 2002:a17:903:238e:b0:223:653e:eb09 with SMTP id d9443c01a7336-23c6e45213cmr6572795ad.7.1751412699050;        <PERSON><PERSON>, 01 Jul 2025 16:31:39 -0700 (PDT)", "ARC-Seal": "i=1; a=rsa-sha256; t=1751412699; cv=none;        d=google.com; s=arc-20240605;        b=UM4rlrgK+p+6KEY1y9yQW5ocyDm85CTru0I7Fjbp38dogjlrXZbc4SUykDcqlFfNRJ         xWPa9u4gV4AettPwZ14vf95hyCKp7iHzCSJqnv4AHswNMrNRYJkMPpUdackWkGRzmrS1         lg/vcli+EUqXp7pNW5tCJqe7Z/Ckkql5IJvEhNpLyy/tj8QPckZ+2v/n9PWcuRKmsurE         ao1JDCTX1HxgZ0kFbQBusciboISTjKm3hO3mOn6ITeq7saGIydLZDibkMDJzprP/g8/V         gNn9bAi4blQQls9WbUeryJJSYulHb0fsRdWZV/78d+HPZdaiBGWFV5LAnMnayQTiZwi6         hI3w==", "ARC-Message-Signature": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;        h=dkim-signature:importance:mime-version:message-id:to:subject:from         :date;        bh=457tsQeVXSE63sWrH4ya+LCNpi6MMC29fjytYGQU8Kg=;        fh=IbX48oA3ZeBTgts9MaF2ZT9pyVF0T+0Sr3yYLuU0p40=;        b=TalQU/BtfqwqE9/G/XgtIPLAJskWLwg6ShxsIkg9bmhLjouHiQmr7p8r31NPS2YmCw         RheM+qhdT11cDnsDF2GPBI5UqbVtyDLO+sUsRRYG3UnEyKrGOnXVNJOyjuZUjh/bzxls         sAl9jF4c2g2ce13VXDSMU0kZ/8ibjaW+s3FsLxHc59IXW3+/FUqijqVnXWOlD4yjjTOh         W7e2Ia9VSyWXgbvq0BH+nqY16rvzzxCJMw2E9AftkgEugrhOQPAW3Kj3NYWq/KXJNRIH         Eq1t3TtUh7g1R0JULoYhm+NTvZOz8UA3Nv+syN1DxLxbIM63CtVwm8yF1TCATaN4pZXc         lOWw==;        dara=google.com", "ARC-Authentication-Results": "i=1; mx.google.com;       dkim=pass header.i=@woolworths.com.au header.s=gateway202110 header.b=oXv5fwtA;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=woolworths.com.au", "Return-Path": "<<EMAIL>>", "Received-SPF": "pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;", "Authentication-Results": "mx.google.com;       dkim=pass header.i=@teamsystems-net-au.20230601.gappssmtp.com header.s=20230601 header.b=NlOOKAmM;       arc=pass (i=2 spf=pass spfdomain=woolworths.com.au dkim=pass dkdomain=woolworths.com.au dmarc=pass fromdomain=woolworths.com.au);       spf=pass (google.com: <NAME_EMAIL> designates 2607:f8b0:4864:20::646 as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=teamsystems.net.au;       dara=pass header.i=@teamsystems.net.au", "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=teamsystems-net-au.20230601.gappssmtp.com; s=20230601; t=**********; x=**********; darn=teamsystems.net.au;        h=list-unsubscribe:list-archive:list-help:list-post:list-id         :mailing-list:precedence:reply-to:x-original-authentication-results         :x-original-sender:importance:mime-version:message-id:to:subject         :from:date:from:to:cc:subject:date:message-id:reply-to;        bh=457tsQeVXSE63sWrH4ya+LCNpi6MMC29fjytYGQU8Kg=;        b=NlOOKAmMigac/p0uZGnNSBgziGW0t5U/I9EsL0JYHcpMf0PpvUzfo+8QP6GSvDSBQm         Ozz3JkemsidxiNHPBYQfALALa/QT8ES9lYBQFsiJvfHJP5FItcH1MynvjxTzVlDtBvOH         BIo0IoPB2uUvf7tHeNKiC/c+iSRyeG1HLCWJCSSDqDN7L459gekBVngVKNgUKKvWjqqn         Sh5HbSHuGcNvME4lJqFMo74yp12N2N6HSIq7lNFSMfes5hxXfmOvWxHZbPR+4Hg+NLfm         P2yQtGisN5UvSMGMGAEYKIOUifYV5/bop2m6Zr6+4+qvgVm8cXxA1viI/z3/ZUqDlCrC         6Aiw==", "X-Google-DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20230601; t=**********; x=**********;        h=list-unsubscribe:list-archive:list-help:list-post         :x-spam-checked-in-group:list-id:mailing-list:precedence:reply-to         :x-original-authentication-results:x-original-sender:importance         :mime-version:message-id:to:subject:from:date:x-beenthere         :x-gm-message-state:from:to:cc:subject:date:message-id:reply-to;        bh=457tsQeVXSE63sWrH4ya+LCNpi6MMC29fjytYGQU8Kg=;        b=Nh+WGHw1qbT7TcWOaoeCiM7zkoJTF+R2Tmfy5Ajupfiw4vURtZSi0q810/BtP8deg5         Bo0RMHYkoAzA1rbbqVvkQ0hRebgkObZu3LuGL4Oks1XaeWeqJrEmJQfd3JA+XwCZ28PJ         2EVYDeLJy/EQtErFmYyKxmw9GCGjBirWODyw+DvG2A9+aEYyitXYSC5LMSSae0ujexoi         58Dq54kFx/plpz51ziWrUvGYXAC3k+DI8Yixri5E6WpDTi8tKrbuG6erRcSv2zr0ATM5         UMgluWWzbYU6JwvaHNTO2/ISfzDkHq+WEbCsKXqVuxduwGDjNwF7g3SZ0LpduKAIl+/7         mPPw==", "X-Gm-Message-State": "AOJu0YxtWl8Ds5bXwxBWQVfPhotfO9zOS8jhSCBY2qGvkUF8wpcvJTA1 ZAxpwe/L8oHsEUbDaeFryh3xX1uH1Od/IFytSkHI7rAL3FdUTdU2EsZCMog5OcZhwkKTTQ==", "X-BeenThere": "<EMAIL>; h=AZMbMZdg08QNTKJfdWpM5gLOatryxciw+2NlJHOdoo2DQW+vyg==", "Date": "Wed, 2 Jul 2025 09:29:03 +1000 (AUSNSW)", "From": "UC4_BATCH UC4_BATCH <<EMAIL>>", "Subject": "Packing Slip for Purchase Order **********", "To": "<<EMAIL>>, <<EMAIL>>", "Message-ID": "<<EMAIL>>", "MIME-Version": "1.0", "Importance": "Normal", "X-Priority": "1 (Highest)", "X-Mailer": "SAP NetWeaver 750", "Content-Type": "multipart/mixed; boundary=\"=_00224810D9EB1FE095DA70DDD7E4D57B\"", "x-agari-authentication-results": "wowsmtpgw13;", "x-msw-jemd-newsletter": "false", "X-Original-Sender": "<EMAIL>", "X-Original-Authentication-Results": "mx.google.com;       dkim=pass header.i=@woolworths.com.au header.s=gateway202110 header.b=oXv5fwtA;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=woolworths.com.au", "X-Original-From": "UC4_BATCH UC4_BATCH <<EMAIL>>", "Reply-To": "UC4_BATCH UC4_BATCH <<EMAIL>>", "Precedence": "list", "Mailing-list": "list <EMAIL>; contact <EMAIL>", "List-ID": "<melbourne.teamsystems.net.au>", "X-Spam-Checked-In-Group": "<EMAIL>", "X-Google-Group-Id": "114953712296", "List-Post": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/post>, <mailto:<EMAIL>>", "List-Help": "<https://support.google.com/a/teamsystems.net.au/bin/topic.py?topic=25838>, <mailto:<EMAIL>>", "List-Archive": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/>", "List-Unsubscribe": "<mailto:<EMAIL>>, <https://groups.google.com/a/teamsystems.net.au/group/melbourne/subscribe>"}, "sender_display": "UC4_BATCH UC4_BATCH <<EMAIL>>", "sender_for_history": "\"'UC4_BATCH UC4_BATCH' via Melbourne\" <<EMAIL>>", "body": "", "attachments": [{"id": "ANGjdJ_MwHwBe2LTLA2XcPBZ5gIZsYmoXV3k9MYKypWte3ov-iH5xYoxDszuD-iU7tl0J2iriEeoGChyTQuUk4KgQZseBqMBH3TbCBBaOfAuSN7lJ8Mcw5dkKNKxR1J35YL6eDsiB5p8Sf5sCOpjmKwfzUpfuNpP-IIpb5M9XFC84xiLzK3wF7g0W5rZzuur_Un1jV07jEaT5YG1oLeYFcjfpLwCH4CFKzJ_MrpUfVLUi_pYsRb08CLxPKYP9zg3nyypFLC74U7tqpTpO5_OXjJI9sczgNolcsnja0_StRus_e56WgE9trw6P0o9iM--31OGYX6skHPy0xvhEOwtUsgHw1lgf7bitebpsduBKubMUpkfoD7xGrXzu_VYtWO18EBzI2yKHNlaM55dcQP7", "filename": "Packing Slip for Purchase Order **********.PDF", "mimeType": "application/pdf", "size": 20655, "local_path": "extracted_data/enhanced_context\\attachments\\197c854fbb0da64b_Packing_Slip_for_Purchase_Order_**********.PDF"}], "metadata": {"date": "Wed, 2 Jul 2025 09:29:03 +1000 (AUSNSW)", "threadId": "197c854fbb0da64b", "labelIds": ["IMPORTANT", "CATEGORY_FORUMS", "Label_9026918239996958878", "Label_14", "INBOX"], "snippet": "Please find attached Packing slip for Purchase Order #********** *********************************************************** CAUTION: This email and files included in its transmission are solely", "internalDate": "1751412543000"}}, "thread_context": [{"id": "197c854fbb0da64b", "headers": {"Delivered-To": "<EMAIL>", "Received": "from WOOLWORTHS.COM.AU (unknown [***********]) by wowsmtpgwmel2.woolworths.com.au (MTA) with ESMTP id 4bWznN4mBdzBWWTb; Wed,  2 Jul 2025 09:31:36 +1000 (AEST)", "X-Forwarded-Encrypted": "i=2; AJvYcCXNqyIw6Ja2jZyzRLIYlWrfCmlO/acejLale/cel9Z9TrEJR90eQqKBaOV5+61N2LbuTxwiqA==@teamsystems.net.au", "X-Google-Smtp-Source": "AGHT+IHXecrOWZ+h+MO7qy6uxDUR1aD86ogN1P6Fn4zv77HvWK4GZ+rHrgeXtkVXcY8+J7iDorJJ", "X-Received": "by 2002:a17:903:238e:b0:223:653e:eb09 with SMTP id d9443c01a7336-23c6e45213cmr6572795ad.7.1751412699050;        <PERSON><PERSON>, 01 Jul 2025 16:31:39 -0700 (PDT)", "ARC-Seal": "i=1; a=rsa-sha256; t=1751412699; cv=none;        d=google.com; s=arc-20240605;        b=UM4rlrgK+p+6KEY1y9yQW5ocyDm85CTru0I7Fjbp38dogjlrXZbc4SUykDcqlFfNRJ         xWPa9u4gV4AettPwZ14vf95hyCKp7iHzCSJqnv4AHswNMrNRYJkMPpUdackWkGRzmrS1         lg/vcli+EUqXp7pNW5tCJqe7Z/Ckkql5IJvEhNpLyy/tj8QPckZ+2v/n9PWcuRKmsurE         ao1JDCTX1HxgZ0kFbQBusciboISTjKm3hO3mOn6ITeq7saGIydLZDibkMDJzprP/g8/V         gNn9bAi4blQQls9WbUeryJJSYulHb0fsRdWZV/78d+HPZdaiBGWFV5LAnMnayQTiZwi6         hI3w==", "ARC-Message-Signature": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20240605;        h=dkim-signature:importance:mime-version:message-id:to:subject:from         :date;        bh=457tsQeVXSE63sWrH4ya+LCNpi6MMC29fjytYGQU8Kg=;        fh=IbX48oA3ZeBTgts9MaF2ZT9pyVF0T+0Sr3yYLuU0p40=;        b=TalQU/BtfqwqE9/G/XgtIPLAJskWLwg6ShxsIkg9bmhLjouHiQmr7p8r31NPS2YmCw         RheM+qhdT11cDnsDF2GPBI5UqbVtyDLO+sUsRRYG3UnEyKrGOnXVNJOyjuZUjh/bzxls         sAl9jF4c2g2ce13VXDSMU0kZ/8ibjaW+s3FsLxHc59IXW3+/FUqijqVnXWOlD4yjjTOh         W7e2Ia9VSyWXgbvq0BH+nqY16rvzzxCJMw2E9AftkgEugrhOQPAW3Kj3NYWq/KXJNRIH         Eq1t3TtUh7g1R0JULoYhm+NTvZOz8UA3Nv+syN1DxLxbIM63CtVwm8yF1TCATaN4pZXc         lOWw==;        dara=google.com", "ARC-Authentication-Results": "i=1; mx.google.com;       dkim=pass header.i=@woolworths.com.au header.s=gateway202110 header.b=oXv5fwtA;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=woolworths.com.au", "Return-Path": "<<EMAIL>>", "Received-SPF": "pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) client-ip=*************;", "Authentication-Results": "mx.google.com;       dkim=pass header.i=@teamsystems-net-au.20230601.gappssmtp.com header.s=20230601 header.b=NlOOKAmM;       arc=pass (i=2 spf=pass spfdomain=woolworths.com.au dkim=pass dkdomain=woolworths.com.au dmarc=pass fromdomain=woolworths.com.au);       spf=pass (google.com: <NAME_EMAIL> designates 2607:f8b0:4864:20::646 as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=NONE sp=NONE dis=NONE) header.from=teamsystems.net.au;       dara=pass header.i=@teamsystems.net.au", "DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=teamsystems-net-au.20230601.gappssmtp.com; s=20230601; t=**********; x=**********; darn=teamsystems.net.au;        h=list-unsubscribe:list-archive:list-help:list-post:list-id         :mailing-list:precedence:reply-to:x-original-authentication-results         :x-original-sender:importance:mime-version:message-id:to:subject         :from:date:from:to:cc:subject:date:message-id:reply-to;        bh=457tsQeVXSE63sWrH4ya+LCNpi6MMC29fjytYGQU8Kg=;        b=NlOOKAmMigac/p0uZGnNSBgziGW0t5U/I9EsL0JYHcpMf0PpvUzfo+8QP6GSvDSBQm         Ozz3JkemsidxiNHPBYQfALALa/QT8ES9lYBQFsiJvfHJP5FItcH1MynvjxTzVlDtBvOH         BIo0IoPB2uUvf7tHeNKiC/c+iSRyeG1HLCWJCSSDqDN7L459gekBVngVKNgUKKvWjqqn         Sh5HbSHuGcNvME4lJqFMo74yp12N2N6HSIq7lNFSMfes5hxXfmOvWxHZbPR+4Hg+NLfm         P2yQtGisN5UvSMGMGAEYKIOUifYV5/bop2m6Zr6+4+qvgVm8cXxA1viI/z3/ZUqDlCrC         6Aiw==", "X-Google-DKIM-Signature": "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20230601; t=**********; x=**********;        h=list-unsubscribe:list-archive:list-help:list-post         :x-spam-checked-in-group:list-id:mailing-list:precedence:reply-to         :x-original-authentication-results:x-original-sender:importance         :mime-version:message-id:to:subject:from:date:x-beenthere         :x-gm-message-state:from:to:cc:subject:date:message-id:reply-to;        bh=457tsQeVXSE63sWrH4ya+LCNpi6MMC29fjytYGQU8Kg=;        b=Nh+WGHw1qbT7TcWOaoeCiM7zkoJTF+R2Tmfy5Ajupfiw4vURtZSi0q810/BtP8deg5         Bo0RMHYkoAzA1rbbqVvkQ0hRebgkObZu3LuGL4Oks1XaeWeqJrEmJQfd3JA+XwCZ28PJ         2EVYDeLJy/EQtErFmYyKxmw9GCGjBirWODyw+DvG2A9+aEYyitXYSC5LMSSae0ujexoi         58Dq54kFx/plpz51ziWrUvGYXAC3k+DI8Yixri5E6WpDTi8tKrbuG6erRcSv2zr0ATM5         UMgluWWzbYU6JwvaHNTO2/ISfzDkHq+WEbCsKXqVuxduwGDjNwF7g3SZ0LpduKAIl+/7         mPPw==", "X-Gm-Message-State": "AOJu0YxtWl8Ds5bXwxBWQVfPhotfO9zOS8jhSCBY2qGvkUF8wpcvJTA1 ZAxpwe/L8oHsEUbDaeFryh3xX1uH1Od/IFytSkHI7rAL3FdUTdU2EsZCMog5OcZhwkKTTQ==", "X-BeenThere": "<EMAIL>; h=AZMbMZdg08QNTKJfdWpM5gLOatryxciw+2NlJHOdoo2DQW+vyg==", "Date": "Wed, 2 Jul 2025 09:29:03 +1000 (AUSNSW)", "From": "\"'UC4_BATCH UC4_BATCH' via Melbourne\" <<EMAIL>>", "Subject": "Packing Slip for Purchase Order **********", "To": "<<EMAIL>>, <<EMAIL>>", "Message-ID": "<<EMAIL>>", "MIME-Version": "1.0", "Importance": "Normal", "X-Priority": "1 (Highest)", "X-Mailer": "SAP NetWeaver 750", "Content-Type": "multipart/mixed; boundary=\"=_00224810D9EB1FE095DA70DDD7E4D57B\"", "x-agari-authentication-results": "wowsmtpgw13;", "x-msw-jemd-newsletter": "false", "X-Original-Sender": "<EMAIL>", "X-Original-Authentication-Results": "mx.google.com;       dkim=pass header.i=@woolworths.com.au header.s=gateway202110 header.b=oXv5fwtA;       spf=pass (google.com: <NAME_EMAIL> designates ************* as permitted sender) smtp.mailfrom=<EMAIL>;       dmarc=pass (p=REJECT sp=REJECT dis=NONE) header.from=woolworths.com.au", "X-Original-From": "UC4_BATCH UC4_BATCH <<EMAIL>>", "Reply-To": "UC4_BATCH UC4_BATCH <<EMAIL>>", "Precedence": "list", "Mailing-list": "list <EMAIL>; contact <EMAIL>", "List-ID": "<melbourne.teamsystems.net.au>", "X-Spam-Checked-In-Group": "<EMAIL>", "X-Google-Group-Id": "114953712296", "List-Post": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/post>, <mailto:<EMAIL>>", "List-Help": "<https://support.google.com/a/teamsystems.net.au/bin/topic.py?topic=25838>, <mailto:<EMAIL>>", "List-Archive": "<https://groups.google.com/a/teamsystems.net.au/group/melbourne/>", "List-Unsubscribe": "<mailto:<EMAIL>>, <https://groups.google.com/a/teamsystems.net.au/group/melbourne/subscribe>"}, "snippet": "Please find attached Packing slip for Purchase Order #********** *********************************************************** CAUTION: This email and files included in its transmission are solely", "date": "Wed, 2 Jul 2025 09:29:03 +1000 (AUSNSW)"}], "sender_history": [{"id": "17fcf2c34d52dc35", "subject": "Packing Slip for Purchase Order 4401044401", "date": "Mon, 28 Mar 2022 17:16:50 +1100 (AUSNSW)", "snippet": "Please find attached Packing slip for Purchase Order #4401044401 *********************************************************** CAUTION: This email and files included in its transmission are solely"}, {"id": "17fce19a888eff76", "subject": "Packing Slip for Purchase Order 4401045696", "date": "Mon, 28 Mar 2022 12:16:45 +1100 (AUSNSW)", "snippet": "Please find attached Packing slip for Purchase Order #4401045696 *********************************************************** CAUTION: This email and files included in its transmission are solely"}, {"id": "17fcde7387480696", "subject": "Packing Slip for Purchase Order 4401045604", "date": "Mon, 28 Mar 2022 11:17:15 +1100 (AUSNSW)", "snippet": "Please find attached Packing slip for Purchase Order #4401045604 *********************************************************** CAUTION: This email and files included in its transmission are solely"}, {"id": "17fbe0698a322a1d", "subject": "Packing Slip for Purchase Order 4401045211", "date": "Fri, 25 Mar 2022 09:20:11 +1100 (AUSNSW)", "snippet": "Please find attached Packing slip for Purchase Order #4401045211 *********************************************************** CAUTION: This email and files included in its transmission are solely"}, {"id": "17fbe0691a085c6c", "subject": "Packing Slip for Purchase Order 4401045208", "date": "Fri, 25 Mar 2022 09:20:09 +1100 (AUSNSW)", "snippet": "Please find attached Packing slip for Purchase Order #4401045208 *********************************************************** CAUTION: This email and files included in its transmission are solely"}], "label_context": "Woolworths", "extraction_time": "2025-07-03T05:24:29.757287"}