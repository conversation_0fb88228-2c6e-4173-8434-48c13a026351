#!/usr/bin/env python3
"""Simple system starter that works with the restructured project."""
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_dashboard():
    """Start the email dashboard."""
    try:
        from orchestrators.email_dashboard import EmailDashboard
        print("🚀 Starting Email Dashboard...")
        dashboard = EmailDashboard()
        dashboard.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Failed to start dashboard: {e}")
        import traceback
        traceback.print_exc()

def start_demo_dashboard():
    """Start demo dashboard."""
    try:
        print("🚀 Starting Demo Dashboard...")
        import demo_dashboard
    except Exception as e:
        print(f"❌ Failed to start demo dashboard: {e}")
        import traceback
        traceback.print_exc()

def start_main_processor():
    """Start main processor."""
    try:
        from orchestrators.main_processor import EmailOrderProcessor
        print("🚀 Starting Main Processor...")
        processor = EmailOrderProcessor()
        processor.run()
    except Exception as e:
        print(f"❌ Failed to start processor: {e}")
        import traceback
        traceback.print_exc()

def test_imports():
    """Test that all imports work."""
    print("🧪 Testing imports...")
    
    tests = [
        ("utils.config", "config"),
        ("utils.models", "EmailData"),
        ("services.gmail_service", "GmailService"),
        ("services.llm_service", "LLMService"),
        ("services.myob_service", "MyobService"),
        ("orchestrators.email_dashboard", "EmailDashboard"),
    ]
    
    passed = 0
    for module_name, class_name in tests:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
            passed += 1
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name}: {e}")
    
    print(f"\n📊 Import test results: {passed}/{len(tests)} passed")
    return passed == len(tests)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Simple TeamsysV0.1 Launcher')
    parser.add_argument('action', choices=['dashboard', 'demo', 'processor', 'test'], 
                       help='Action to perform')
    
    args = parser.parse_args()
    
    if args.action == 'dashboard':
        start_dashboard()
    elif args.action == 'demo':
        start_demo_dashboard()
    elif args.action == 'processor':
        start_main_processor()
    elif args.action == 'test':
        test_imports()
