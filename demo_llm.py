"""
Demo script to test LLM parsing capabilities with sample data.
"""
import json
from llm_service import LLMService
from models import ExtractedOrder

# Sample email content that might come from a Woolworths order
SAMPLE_EMAIL_CONTENT = """
From: <EMAIL>
To: <EMAIL>
Subject: Purchase Order LVA4401196688 - Urgent Delivery Required

Dear Supplier,

Please find attached Purchase Order LVA4401196688 for immediate processing.

Delivery Details:
- Customer: WOOLWORTHS LIMITED  
- Store: Woolworths Metro Sydney CBD
- Address: 123 George Street, Sydney NSW 2000
- Contact: Store Manager (02) 9123 4567

Items Required:
1. EQL-ORANGE-JUICE-1L (SKU: EQL001234) - Quantity: 100 units
2. EQL-APPLE-JUICE-1L (SKU: EQL001235) - Quantity: 50 units

Special Instructions:
- Delivery required by 15th January 2024
- Use CAPITAL shipping method
- Contact store before delivery

Please confirm receipt and estimated delivery time.

Best regards,
Woolworths Procurement Team
"""

SAMPLE_PDF_CONTENT = """
PURCHASE ORDER
Order Number: LVA4401196688
Date: 12/01/2024

DELIVER TO:
Woolworths Metro Sydney CBD
123 George Street
Sydney NSW 2000
Australia

SUPPLIER:
Your Company Name
456 Supply Street
Melbourne VIC 3000

ITEM DETAILS:
Line 1: 
Product: Orange Juice 1L
SKU: EQL001234
Quantity: 100
Unit Price: $3.50
Total: $350.00

Line 2:
Product: Apple Juice 1L  
SKU: EQL001235
Quantity: 50
Unit Price: $4.00
Total: $200.00

SHIPPING:
Method: CAPITAL
Required Date: 15/01/2024

ORDER TOTAL: $550.00
GST: $55.00
TOTAL INC GST: $605.00

Terms: 30 days net
"""

def demo_markdown_generation():
    """Demo markdown summary generation."""
    print("🔤 TESTING MARKDOWN GENERATION")
    print("=" * 50)
    
    try:
        llm_service = LLMService()
        
        # Create a mock email data object
        class MockEmailData:
            def __init__(self):
                self.subject = "Purchase Order LVA4401196688 - Urgent Delivery Required"
                self.sender = "<EMAIL>"
                self.body = SAMPLE_EMAIL_CONTENT
        
        mock_email = MockEmailData()
        
        print("Generating markdown summary...")
        markdown = llm_service.generate_markdown_summary(mock_email, SAMPLE_PDF_CONTENT)
        
        print("\\n📄 GENERATED MARKDOWN SUMMARY:")
        print("-" * 40)
        print(markdown)
        print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ Markdown generation failed: {e}")
        return False

def demo_order_extraction():
    """Demo order data extraction."""
    print("\\n\\n🔍 TESTING ORDER DATA EXTRACTION")
    print("=" * 50)
    
    try:
        llm_service = LLMService()
        
        # Combine email and PDF content
        full_content = f"{SAMPLE_EMAIL_CONTENT}\\n\\n--- PDF CONTENT ---\\n{SAMPLE_PDF_CONTENT}"
        
        print("Extracting order data...")
        extracted_data = llm_service.extract_order_data(full_content)
        
        if extracted_data:
            print("\\n📊 EXTRACTED ORDER DATA:")
            print("-" * 40)
            print(json.dumps(extracted_data, indent=2))
            print("-" * 40)
            
            # Try to validate with Pydantic
            try:
                validated_order = ExtractedOrder(**extracted_data)
                print("\\n✅ DATA VALIDATION SUCCESSFUL")
                print(f"   - Customer ID: {validated_order.customer_details.debtor_id}")
                print(f"   - PO Number: {validated_order.customer_details.customer_order_number}")
                print(f"   - Order Lines: {len(validated_order.order_lines)}")
                print(f"   - Ship Via: {validated_order.X_SHIPVIA}")
                
                return validated_order
                
            except Exception as validation_error:
                print(f"❌ Data validation failed: {validation_error}")
                return None
        else:
            print("❌ No data extracted")
            return None
            
    except Exception as e:
        print(f"❌ Order extraction failed: {e}")
        return None

def demo_myob_payload():
    """Demo MYOB payload generation."""
    print("\\n\\n🏢 TESTING MYOB PAYLOAD GENERATION")
    print("=" * 50)
    
    try:
        llm_service = LLMService()
        
        # Use extracted order from previous demo
        full_content = f"{SAMPLE_EMAIL_CONTENT}\\n\\n--- PDF CONTENT ---\\n{SAMPLE_PDF_CONTENT}"
        extracted_data = llm_service.extract_order_data(full_content)
        
        if extracted_data:
            validated_order = ExtractedOrder(**extracted_data)
            
            print("Generating MYOB payload...")
            myob_payload = llm_service.generate_myob_payload(validated_order)
            
            print("\\n🔧 GENERATED MYOB PAYLOAD:")
            print("-" * 40)
            print(json.dumps(myob_payload, indent=2))
            print("-" * 40)
            
            return True
        else:
            print("❌ No order data to convert to MYOB payload")
            return False
            
    except Exception as e:
        print(f"❌ MYOB payload generation failed: {e}")
        return False

def main():
    """Run the LLM demo."""
    print("=" * 60)
    print("EMAIL ORDER PROCESSOR - LLM DEMO")
    print("=" * 60)
    print("\\nThis demo tests the LLM parsing capabilities with sample")
    print("Woolworths order data (email + PDF content).")
    print("\\nNote: Requires GEMINI_API_KEY in your .env file")
    print("=" * 60)
    
    # Check if we have the LLM service available
    try:
        from config import config
        if not config.GEMINI_API_KEY or config.GEMINI_API_KEY == "your_gemini_api_key_here":
            print("⚠️  Please set GEMINI_API_KEY in your .env file to run this demo")
            return
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return
    
    # Run demos
    demos = [
        demo_markdown_generation,
        demo_order_extraction,
        demo_myob_payload
    ]
    
    passed = 0
    for demo in demos:
        try:
            if demo():
                passed += 1
        except Exception as e:
            print(f"❌ Demo failed with error: {e}")
    
    print("\\n" + "=" * 60)
    print(f"DEMO RESULTS: {passed}/{len(demos)} demos completed successfully")
    print("=" * 60)
    
    if passed == len(demos):
        print("🎉 All LLM demos passed! The system is working correctly.")
    else:
        print("⚠️  Some demos failed. Check your GEMINI_API_KEY and network connection.")

if __name__ == "__main__":
    main()
