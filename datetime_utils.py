"""
Modern datetime utilities for email processing.
Uses current best practices for timezone-aware datetime operations.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Union
import re

logger = logging.getLogger(__name__)


class ModernDateTimeHandler:
    """Modern datetime handler with timezone awareness and best practices."""
    
    @staticmethod
    def now_utc() -> datetime:
        """Get current UTC time with timezone awareness."""
        return datetime.now(timezone.utc)
    
    @staticmethod
    def now_local() -> datetime:
        """Get current local time with timezone awareness."""
        return datetime.now()
    
    @staticmethod
    def format_for_gmail_query(dt: datetime) -> str:
        """
        Format datetime for Gmail query.
        
        Args:
            dt: Datetime object to format.
            
        Returns:
            String formatted for Gmail query (YYYY/MM/DD).
        """
        # Ensure we're working with UTC for consistency
        if dt.tzinfo is None:
            # Assume naive datetime is UTC
            dt = dt.replace(tzinfo=timezone.utc)
        elif dt.tzinfo != timezone.utc:
            # Convert to UTC
            dt = dt.astimezone(timezone.utc)
        
        return dt.strftime('%Y/%m/%d')
    
    @staticmethod
    def get_date_filter_for_hours_ago(hours: int) -> str:
        """
        Get Gmail date filter for emails from N hours ago.
        
        Args:
            hours: Number of hours ago to filter from.
            
        Returns:
            Gmail query date filter string.
        """
        if hours <= 0:
            return ""
        
        cutoff_time = ModernDateTimeHandler.now_utc() - timedelta(hours=hours)
        return f"after:{ModernDateTimeHandler.format_for_gmail_query(cutoff_time)}"
    
    @staticmethod
    def get_date_filter_for_days_ago(days: int) -> str:
        """
        Get Gmail date filter for emails from N days ago.
        
        Args:
            days: Number of days ago to filter from.
            
        Returns:
            Gmail query date filter string.
        """
        if days <= 0:
            return ""
        
        cutoff_time = ModernDateTimeHandler.now_utc() - timedelta(days=days)
        return f"after:{ModernDateTimeHandler.format_for_gmail_query(cutoff_time)}"
    
    @staticmethod
    def parse_date_filter(date_filter: str) -> Optional[datetime]:
        """
        Parse a Gmail date filter string back to datetime.
        
        Args:
            date_filter: Gmail date filter string (e.g., "after:2024/01/01").
            
        Returns:
            Parsed datetime object or None if parsing fails.
        """
        try:
            # Extract date from filter
            match = re.search(r'after:(\d{4}/\d{2}/\d{2})', date_filter)
            if not match:
                return None
            
            date_str = match.group(1)
            return datetime.strptime(date_str, '%Y/%m/%d').replace(tzinfo=timezone.utc)
        except Exception as e:
            logger.warning(f"Failed to parse date filter '{date_filter}': {e}")
            return None
    
    @staticmethod
    def parse_gmail_date(date_str: str) -> Optional[datetime]:
        """
        Parse Gmail date string to timezone-aware datetime object.
        
        Args:
            date_str: Gmail date string.
            
        Returns:
            Parsed datetime object or None if parsing fails.
        """
        # Common Gmail date formats
        formats = [
            "%a, %d %b %Y %H:%M:%S %z",  # RFC 2822 with timezone
            "%d %b %Y %H:%M:%S %z",      # Simplified with timezone
            "%a, %d %b %Y %H:%M:%S %Z",  # RFC 2822 with timezone name
            "%d %b %Y %H:%M:%S %Z",      # Simplified with timezone name
            "%a, %d %b %Y %H:%M:%S",     # RFC 2822 without timezone
            "%d %b %Y %H:%M:%S",         # Simplified without timezone
        ]

        for fmt in formats:
            try:
                # Clean up the date string
                clean_date = re.sub(r'\s*\([^)]*\)$', '', date_str.strip())
                
                parsed_dt = datetime.strptime(clean_date, fmt)
                
                # If no timezone info, assume UTC
                if parsed_dt.tzinfo is None:
                    parsed_dt = parsed_dt.replace(tzinfo=timezone.utc)
                
                return parsed_dt
            except ValueError:
                continue

        # Fallback: try email.utils.parsedate_tz
        try:
            import email.utils
            parsed = email.utils.parsedate_tz(date_str)
            if parsed:
                # Convert to datetime with timezone
                dt = datetime(*parsed[:6])
                if parsed[9] is not None:
                    # Has timezone offset
                    tz_offset = timedelta(seconds=parsed[9])
                    dt = dt.replace(tzinfo=timezone(tz_offset))
                else:
                    # No timezone, assume UTC
                    dt = dt.replace(tzinfo=timezone.utc)
                return dt
        except Exception:
            pass

        logger.warning(f"Could not parse Gmail date: {date_str}")
        return None
    
    @staticmethod
    def format_for_display(dt: datetime) -> str:
        """
        Format datetime for human-readable display.
        
        Args:
            dt: Datetime object to format.
            
        Returns:
            Human-readable datetime string.
        """
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # Convert to local time for display
        local_dt = dt.astimezone()
        return local_dt.strftime('%Y-%m-%d %H:%M:%S %Z')
    
    @staticmethod
    def get_relative_time_description(dt: datetime) -> str:
        """
        Get relative time description (e.g., "2 hours ago").
        
        Args:
            dt: Datetime object to describe.
            
        Returns:
            Relative time description string.
        """
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        now = ModernDateTimeHandler.now_utc()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        else:
            return "just now"
    
    @staticmethod
    def validate_date_range(start_date: Optional[str], end_date: Optional[str]) -> tuple[bool, str]:
        """
        Validate a date range for Gmail queries.
        
        Args:
            start_date: Start date string (e.g., "2024/01/01").
            end_date: End date string (e.g., "2024/12/31").
            
        Returns:
            Tuple of (is_valid, error_message).
        """
        try:
            start_dt = None
            end_dt = None
            
            if start_date:
                start_dt = datetime.strptime(start_date, '%Y/%m/%d').replace(tzinfo=timezone.utc)
            
            if end_date:
                end_dt = datetime.strptime(end_date, '%Y/%m/%d').replace(tzinfo=timezone.utc)
            
            if start_dt and end_dt and start_dt > end_dt:
                return False, "Start date must be before end date"
            
            now = ModernDateTimeHandler.now_utc()
            if start_dt and start_dt > now:
                return False, "Start date cannot be in the future"
            
            if end_dt and end_dt > now:
                return False, "End date cannot be in the future"
            
            return True, ""
            
        except ValueError as e:
            return False, f"Invalid date format: {e}"


# Convenience functions for backward compatibility
def now_utc() -> datetime:
    """Get current UTC time (backward compatibility)."""
    return ModernDateTimeHandler.now_utc()


def format_for_gmail_query(dt: datetime) -> str:
    """Format datetime for Gmail query (backward compatibility)."""
    return ModernDateTimeHandler.format_for_gmail_query(dt)


def get_date_filter_for_hours_ago(hours: int) -> str:
    """Get Gmail date filter for emails from N hours ago (backward compatibility)."""
    return ModernDateTimeHandler.get_date_filter_for_hours_ago(hours)


def parse_gmail_date(date_str: str) -> Optional[datetime]:
    """Parse Gmail date string (backward compatibility)."""
    return ModernDateTimeHandler.parse_gmail_date(date_str)


if __name__ == "__main__":
    # Test the datetime utilities
    handler = ModernDateTimeHandler()
    
    print("🧪 Testing Modern DateTime Handler...")
    
    # Test current time
    now = handler.now_utc()
    print(f"Current UTC time: {handler.format_for_display(now)}")
    
    # Test date filters
    filter_48h = handler.get_date_filter_for_hours_ago(48)
    print(f"48 hours ago filter: {filter_48h}")
    
    filter_7d = handler.get_date_filter_for_days_ago(7)
    print(f"7 days ago filter: {filter_7d}")
    
    # Test relative time
    past_time = now - timedelta(hours=3, minutes=30)
    relative = handler.get_relative_time_description(past_time)
    print(f"Relative time: {relative}")
    
    print("✅ DateTime utilities working correctly!")
