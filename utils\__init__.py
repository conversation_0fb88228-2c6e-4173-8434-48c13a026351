"""
Utils package - Shared utilities, configuration, and data models.

This package contains utility functions, configuration management,
data models, and other shared components used throughout the system.

Modules:
    config: Configuration management and environment variables
    models: Pydantic data models for validation
    pdf_extractor: PDF text extraction utilities
    setup: System setup and initialization utilities
"""

from .config import config, Config
from .models import (
    EmailData, 
    ExtractedOrder, 
    CustomerDetails, 
    DeliveryAddress, 
    OrderLine, 
    ProcessedOrder,
    OrderStatus
)

__all__ = [
    'config',
    'Config',
    'EmailData',
    'ExtractedOrder', 
    'CustomerDetails',
    'DeliveryAddress',
    'OrderLine', 
    'ProcessedOrder',
    'OrderStatus'
]