#!/usr/bin/env python3
"""
Cleanup script to remove deprecated and temporary files from the restructured project.
"""
import os
import glob
from pathlib import Path

def remove_deprecated_files():
    """Remove deprecated and temporary files."""
    print("🧹 Cleaning up deprecated and temporary files...")
    
    # Files to remove
    deprecated_files = [
        # Already removed
        # "myob_poster_old.py",  # Old MYOB poster
        # "script.py",  # Original monolithic script
        
        # Temporary files (already removed)
        # "tmp_*",
        
        # Error and output files that are no longer needed
        "dashboard_error.txt",
        "demo_error.txt", 
        "demo_output.txt",
        "error.txt",
        "error_new.txt",
        "manual_error.txt",
        "manual_output.txt",
        "output.txt",
        "run_dash_error.txt",
        "run_dash_output.txt",
        "status_error.txt",
        "status_new.txt",
        "status_output.txt",
        "dashboard_output.txt",
        
        # Extracted data that's no longer needed (keep the directories)
        # We'll clean these selectively
    ]
    
    removed_count = 0
    
    for file_pattern in deprecated_files:
        if '*' in file_pattern:
            # Handle glob patterns
            for file_path in glob.glob(file_pattern):
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        print(f"  ✅ Removed: {file_path}")
                        removed_count += 1
                    except Exception as e:
                        print(f"  ❌ Failed to remove {file_path}: {e}")
        else:
            # Handle individual files
            if os.path.exists(file_pattern):
                try:
                    os.remove(file_pattern)
                    print(f"  ✅ Removed: {file_pattern}")
                    removed_count += 1
                except Exception as e:
                    print(f"  ❌ Failed to remove {file_pattern}: {e}")
    
    print(f"🗑️  Removed {removed_count} deprecated files")
    return removed_count

def clean_extracted_data():
    """Clean old extracted data files but keep recent ones."""
    print("\n🧹 Cleaning old extracted data...")
    
    # Clean old context files (keep directory structure)
    context_dir = Path("extracted_data/enhanced_context")
    if context_dir.exists():
        context_files = list(context_dir.glob("*.json"))
        if len(context_files) > 10:  # Keep only 10 most recent
            context_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            for old_file in context_files[10:]:
                try:
                    old_file.unlink()
                    print(f"  ✅ Removed old context: {old_file.name}")
                except Exception as e:
                    print(f"  ❌ Failed to remove {old_file}: {e}")
    
    print("📁 Extracted data cleanup completed")

def organize_remaining_files():
    """Organize remaining files into appropriate categories."""
    print("\n📂 Organizing remaining files...")
    
    # Files that should stay in root
    root_files = [
        "run_restructured_system.py",  # New main entry point
        "run_tests.py",  # Test runner
        "cleanup_deprecated.py",  # This script
        
        # Demo scripts (keep in root for easy access)
        "demo_dashboard.py",
        "demo_llm.py", 
        "demo_myob_poster.py",
        "run_dashboard.py",
        
        # Test scripts (keep in root for now)
        "test_*.py",
        
        # Configuration and data files
        "*.json", "*.yaml", "*.txt", "*.env*",
        "credentials.json", "token.pickle",
        "requirements*.txt",
        
        # Documentation
        "*.md",
        
        # Other important files
        "*.exe", "*.py" # Keep other Python files for now
    ]
    
    print("📋 Current file organization:")
    print("  📁 orchestrators/ - High-level workflow coordination")
    print("  📁 agents/ - Autonomous processing agents") 
    print("  📁 services/ - External API integrations")
    print("  📁 utils/ - Shared utilities and configuration")
    print("  📄 Root files - Entry points, demos, tests, config, docs")
    
    return True

def validate_restructure():
    """Validate that the restructure is complete and working."""
    print("\n✅ Validating restructured project...")
    
    required_dirs = ["orchestrators", "agents", "services", "utils"]
    required_files = {
        "orchestrators": ["__init__.py", "comprehensive_email_system.py", "main_processor.py"],
        "agents": ["__init__.py", "enhanced_universal_agent.py", "continuous_polling_agent.py"],
        "services": ["__init__.py", "gmail_service.py", "llm_service.py", "myob_service.py"],
        "utils": ["__init__.py", "config.py", "models.py"]
    }
    
    all_good = True
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"  ❌ Missing directory: {dir_name}")
            all_good = False
        else:
            print(f"  ✅ Directory exists: {dir_name}")
            
            for file_name in required_files[dir_name]:
                file_path = os.path.join(dir_name, file_name)
                if not os.path.exists(file_path):
                    print(f"    ❌ Missing file: {file_path}")
                    all_good = False
                else:
                    print(f"    ✅ File exists: {file_path}")
    
    if all_good:
        print("🎉 Project restructure validation PASSED!")
    else:
        print("⚠️  Project restructure validation FAILED!")
    
    return all_good

def main():
    """Main cleanup function."""
    print("=" * 70)
    print("🧹 TEAMSYSV0.1 - DEPRECATED FILE CLEANUP")
    print("=" * 70)
    print()
    
    # Step 1: Remove deprecated files
    removed_count = remove_deprecated_files()
    
    # Step 2: Clean extracted data
    clean_extracted_data()
    
    # Step 3: Organize remaining files
    organize_remaining_files()
    
    # Step 4: Validate restructure
    validation_passed = validate_restructure()
    
    # Final summary
    print("\n" + "=" * 70)
    print("📊 CLEANUP SUMMARY")
    print("=" * 70)
    print(f"🗑️  Files removed: {removed_count}")
    print(f"✅ Restructure valid: {'Yes' if validation_passed else 'No'}")
    print()
    
    if validation_passed:
        print("🎉 Cleanup completed successfully!")
        print("✅ Project is clean and ready for use")
        print()
        print("🚀 Next steps:")
        print("   1. Run tests: python run_tests.py")
        print("   2. Start system: python run_restructured_system.py system")
        print("   3. Or start dashboard: python run_restructured_system.py dashboard --demo")
    else:
        print("⚠️  Cleanup completed with issues")
        print("❌ Some required files may be missing")
        print("🔧 Check the validation output above")
    
    print()

if __name__ == "__main__":
    main()