#!/usr/bin/env python3
"""
Continuous Polling Agent - Monitors Gmail API for all incoming emails in real-time.
"""
import logging
import os
import json
import time
import threading
import queue
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Set, Any
from dataclasses import dataclass, field
from enum import Enum
import sqlite3
from concurrent.futures import ThreadPoolExecutor, as_completed
import signal
import sys


# Add project root to Python path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.gmail_service import GmailService
from services.llm_service import LLMService
from utils.pdf_extractor import extract_text_from_pdf
from utils.models import EmailData, ExtractedOrder, ProcessedOrder
from agents.enhanced_universal_agent import EnhancedUniversalAgent
from utils.config import config

logger = logging.getLogger(__name__)

class EmailPriority(Enum):
    """Email priority levels."""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4
    BULK = 5

class ProcessingStatus(Enum):
    """Processing status for emails."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY = "retry"
    SKIPPED = "skipped"

@dataclass
class EmailQueueItem:
    """Item in the processing queue."""
    email_id: str
    email_data: EmailData
    priority: EmailPriority
    status: ProcessingStatus
    category: str
    confidence: float
    retry_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    processing_started_at: Optional[datetime] = None
    processing_completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    processing_metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PollingConfig:
    """Configuration for continuous polling agent."""
    polling_interval_seconds: int = 30
    max_workers: int = 4
    max_retry_attempts: int = 3
    retry_delay_seconds: int = 60
    rate_limit_delay: int = 1
    batch_size: int = 10
    high_priority_threshold: float = 0.8
    enable_push_notifications: bool = False
    watch_all_labels: bool = True
    custom_filters: List[str] = field(default_factory=list)
    audit_retention_days: int = 30

class ContinuousPollingAgent:
    """Continuous polling agent for real-time email monitoring and processing."""
    
    def __init__(self, config_file: str = "polling_config.json"):
        self.gmail_service = GmailService()
        self.llm_service = LLMService()
        self.universal_agent = EnhancedUniversalAgent()
        
        self.config = self._load_config(config_file)
        self.processing_queue = queue.PriorityQueue()
        self.processed_emails: Set[str] = set()
        self.running = False
        self.last_poll_time = datetime.now()
        
        # Threading and processing
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_workers)
        self.processing_threads: List[threading.Thread] = []
        
        # Database for audit and status tracking
        self.db_path = "continuous_polling.db"
        self._init_database()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("Continuous Polling Agent initialized")
    
    def _load_config(self, config_file: str) -> PollingConfig:
        """Load polling configuration."""
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                    return PollingConfig(**config_data)
            except Exception as e:
                logger.warning(f"Error loading config: {e}, using defaults")
        
        # Create default config
        default_config = PollingConfig()
        self._save_config(config_file, default_config)
        return default_config
    
    def _save_config(self, config_file: str, config: PollingConfig):
        """Save polling configuration."""
        try:
            with open(config_file, 'w') as f:
                json.dump(config.__dict__, f, indent=2, default=str)
            logger.info(f"Configuration saved to {config_file}")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def _init_database(self):
        """Initialize SQLite database for audit and tracking."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Email processing audit table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS email_audit (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_id TEXT NOT NULL,
                    subject TEXT,
                    sender TEXT,
                    timestamp TEXT,
                    category TEXT,
                    priority TEXT,
                    status TEXT,
                    confidence REAL,
                    retry_count INTEGER DEFAULT 0,
                    processing_time_seconds REAL,
                    error_message TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Processing statistics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processing_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    total_processed INTEGER DEFAULT 0,
                    successful INTEGER DEFAULT 0,
                    failed INTEGER DEFAULT 0,
                    average_processing_time REAL DEFAULT 0,
                    api_calls_made INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # System events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    event_data TEXT,
                    severity TEXT DEFAULT 'INFO',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    def _log_system_event(self, event_type: str, event_data: str, severity: str = "INFO"):
        """Log system events to database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO system_events (event_type, event_data, severity)
                VALUES (?, ?, ?)
            ''', (event_type, event_data, severity))
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"Error logging system event: {e}")
    
    def categorize_and_prioritize_email(self, email: EmailData) -> tuple[str, EmailPriority, float]:
        """Categorize email and assign priority."""
        try:
            # Use the universal agent's categorization
            categorization = self.universal_agent.categorize_email(email)
            
            category = categorization.get('category', 'other')
            confidence = categorization.get('confidence', 0.0)
            ai_priority = categorization.get('priority', 3)
            
            # Map AI priority to our enum
            if ai_priority == 1 or confidence > self.config.high_priority_threshold:
                priority = EmailPriority.CRITICAL
            elif ai_priority == 2:
                priority = EmailPriority.HIGH
            elif ai_priority == 3:
                priority = EmailPriority.MEDIUM
            elif ai_priority == 4:
                priority = EmailPriority.LOW
            else:
                priority = EmailPriority.BULK
            
            # Override priority for certain categories
            if category == 'purchase_order':
                priority = EmailPriority.HIGH
            elif category == 'invoice':
                priority = EmailPriority.MEDIUM
            elif category == 'spam':
                priority = EmailPriority.BULK
            
            logger.debug(f"Email categorized: {category}, priority: {priority}, confidence: {confidence}")
            return category, priority, confidence
            
        except Exception as e:
            logger.error(f"Error categorizing email {email.id}: {e}")
            return 'other', EmailPriority.MEDIUM, 0.0
    
    def should_process_email(self, email: EmailData, category: str, confidence: float) -> bool:
        """Determine if email should be processed based on filters."""
        # Skip if already processed
        if email.id in self.processed_emails:
            logger.debug(f"Email {email.id} already processed, skipping")
            return False
        
        # Apply confidence threshold
        if confidence < 0.2:
            logger.debug(f"Email {email.id} below confidence threshold: {confidence}")
            return False
        
        # Apply custom filters
        for filter_pattern in self.config.custom_filters:
            if filter_pattern.lower() in email.subject.lower():
                logger.debug(f"Email {email.id} matches exclude filter: {filter_pattern}")
                return False
        
        # Skip certain categories
        skip_categories = ['spam', 'general_business']
        if category in skip_categories and confidence < 0.7:
            logger.debug(f"Email {email.id} skipped due to category: {category}")
            return False
        
        return True
    
    def poll_new_emails(self) -> List[EmailData]:
        """Poll Gmail for new emails since last poll."""
        try:
            new_emails = []
            
            # Calculate time window for polling
            since_time = self.last_poll_time - timedelta(minutes=5)  # 5 minute overlap for safety
            query = f"after:{since_time.strftime('%Y/%m/%d')}"
            
            if not self.gmail_service.service:
                logger.error("Gmail service not available")
                return []
            
            # Get all labels to monitor
            if self.config.watch_all_labels:
                labels_to_monitor = self.universal_agent.discover_labels()
            else:
                labels_to_monitor = ['INBOX']  # Default to inbox only
            
            for label_name in labels_to_monitor:
                try:
                    label_id = self.gmail_service.get_label_id(label_name)
                    if not label_id:
                        continue
                    
                    response = self.gmail_service.service.users().messages().list(
                        userId='me',
                        labelIds=[label_id],
                        q=query,
                        maxResults=self.config.batch_size
                    ).execute()
                    
                    messages = response.get('messages', [])
                    
                    for msg_ref in messages:
                        email_data = self.gmail_service._get_email_details(msg_ref['id'], label_name)
                        if email_data and email_data.id not in self.processed_emails:
                            new_emails.append(email_data)
                    
                    # Rate limiting
                    time.sleep(self.config.rate_limit_delay)
                    
                except Exception as e:
                    logger.error(f"Error polling label {label_name}: {e}")
                    continue
            
            self.last_poll_time = datetime.now()
            logger.info(f"Polled {len(new_emails)} new emails")
            return new_emails
            
        except Exception as e:
            logger.error(f"Error polling emails: {e}")
            return []
    
    def add_to_processing_queue(self, email: EmailData):
        """Add email to processing queue with priority."""
        try:
            category, priority, confidence = self.categorize_and_prioritize_email(email)
            
            if not self.should_process_email(email, category, confidence):
                return
            
            queue_item = EmailQueueItem(
                email_id=email.id,
                email_data=email,
                priority=priority,
                status=ProcessingStatus.PENDING,
                category=category,
                confidence=confidence
            )
            
            # Priority queue uses tuple (priority_value, item)
            self.processing_queue.put((priority.value, queue_item))
            
            logger.info(f"Added email to queue: {email.subject[:50]}... (Priority: {priority.name})")
            
        except Exception as e:
            logger.error(f"Error adding email to queue: {e}")
    
    def process_email_worker(self, queue_item: EmailQueueItem) -> Optional[ProcessedOrder]:
        """Process a single email from the queue."""
        start_time = datetime.now()
        queue_item.processing_started_at = start_time
        queue_item.status = ProcessingStatus.PROCESSING
        
        try:
            logger.info(f"Processing email: {queue_item.email_data.subject}")
            
            # Use universal agent for processing
            processed_order = self.universal_agent._process_single_email_universal(queue_item.email_data)
            
            if processed_order:
                queue_item.status = ProcessingStatus.COMPLETED
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Log to audit database
                self._log_processing_audit(queue_item, processing_time, success=True)
                
                # Mark as processed
                self.processed_emails.add(queue_item.email_id)
                
                logger.info(f"Successfully processed email {queue_item.email_id} in {processing_time:.2f}s")
                return processed_order
            else:
                queue_item.status = ProcessingStatus.FAILED
                queue_item.error_message = "No order data extracted"
                self._log_processing_audit(queue_item, (datetime.now() - start_time).total_seconds(), success=False)
                
        except Exception as e:
            queue_item.status = ProcessingStatus.FAILED
            queue_item.error_message = str(e)
            processing_time = (datetime.now() - start_time).total_seconds()
            self._log_processing_audit(queue_item, processing_time, success=False)
            
            logger.error(f"Error processing email {queue_item.email_id}: {e}")
            
            # Handle retry logic
            if queue_item.retry_count < self.config.max_retry_attempts:
                queue_item.retry_count += 1
                queue_item.status = ProcessingStatus.RETRY
                logger.info(f"Scheduling retry {queue_item.retry_count} for email {queue_item.email_id}")
                
                # Re-queue with delay
                threading.Timer(
                    self.config.retry_delay_seconds,
                    lambda: self.processing_queue.put((queue_item.priority.value, queue_item))
                ).start()
        
        finally:
            queue_item.processing_completed_at = datetime.now()
        
        return None
    
    def _log_processing_audit(self, queue_item: EmailQueueItem, processing_time: float, success: bool):
        """Log processing attempt to audit database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO email_audit (
                    email_id, subject, sender, timestamp, category, priority, status,
                    confidence, retry_count, processing_time_seconds, error_message
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                queue_item.email_id,
                queue_item.email_data.subject,
                queue_item.email_data.sender,
                queue_item.email_data.timestamp,
                queue_item.category,
                queue_item.priority.name,
                queue_item.status.value,
                queue_item.confidence,
                queue_item.retry_count,
                processing_time,
                queue_item.error_message
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging processing audit: {e}")
    
    def process_queue_worker(self):
        """Worker thread that processes items from the queue."""
        while self.running:
            try:
                # Get item from queue with timeout
                try:
                    priority, queue_item = self.processing_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Process the email
                self.process_email_worker(queue_item)
                
                # Mark task as done
                self.processing_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in queue worker: {e}")
                time.sleep(1)
    
    def cleanup_old_audit_data(self):
        """Clean up old audit data based on retention policy."""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.config.audit_retention_days)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                DELETE FROM email_audit 
                WHERE created_at < ?
            ''', (cutoff_date.isoformat(),))
            
            cursor.execute('''
                DELETE FROM system_events 
                WHERE created_at < ?
            ''', (cutoff_date.isoformat(),))
            
            deleted_rows = cursor.rowcount
            conn.commit()
            conn.close()
            
            if deleted_rows > 0:
                logger.info(f"Cleaned up {deleted_rows} old audit records")
                
        except Exception as e:
            logger.error(f"Error cleaning up audit data: {e}")
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Overall statistics
            cursor.execute('''
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    AVG(CASE WHEN processing_time_seconds > 0 THEN processing_time_seconds END) as avg_time
                FROM email_audit 
                WHERE created_at >= date('now', '-24 hours')
            ''')
            
            stats = cursor.fetchone()
            
            # Statistics by category
            cursor.execute('''
                SELECT category, COUNT(*) as count
                FROM email_audit 
                WHERE created_at >= date('now', '-24 hours')
                GROUP BY category
                ORDER BY count DESC
            ''')
            
            category_stats = cursor.fetchall()
            
            # Queue status
            queue_size = self.processing_queue.qsize()
            
            conn.close()
            
            return {
                'total_processed_24h': stats[0] if stats[0] else 0,
                'successful_24h': stats[1] if stats[1] else 0,
                'failed_24h': stats[2] if stats[2] else 0,
                'average_processing_time': stats[3] if stats[3] else 0,
                'current_queue_size': queue_size,
                'category_breakdown': dict(category_stats),
                'system_status': 'running' if self.running else 'stopped'
            }
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            return {}
    
    def start(self):
        """Start the continuous polling agent."""
        if self.running:
            logger.warning("Agent is already running")
            return
        
        self.running = True
        logger.info("Starting Continuous Polling Agent")
        
        # Log system startup
        self._log_system_event("AGENT_START", "Continuous polling agent started", "INFO")
        
        # Start worker threads
        for i in range(self.config.max_workers):
            worker_thread = threading.Thread(
                target=self.process_queue_worker,
                name=f"QueueWorker-{i+1}",
                daemon=True
            )
            worker_thread.start()
            self.processing_threads.append(worker_thread)
        
        logger.info(f"Started {self.config.max_workers} worker threads")
        
        # Main polling loop
        try:
            while self.running:
                try:
                    # Poll for new emails
                    new_emails = self.poll_new_emails()
                    
                    # Add emails to processing queue
                    for email in new_emails:
                        self.add_to_processing_queue(email)
                    
                    # Periodic cleanup
                    if datetime.now().hour == 0 and datetime.now().minute < 5:
                        self.cleanup_old_audit_data()
                    
                    # Wait for next poll
                    time.sleep(self.config.polling_interval_seconds)
                    
                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt, shutting down...")
                    break
                except Exception as e:
                    logger.error(f"Error in main polling loop: {e}")
                    self._log_system_event("POLLING_ERROR", str(e), "ERROR")
                    time.sleep(self.config.polling_interval_seconds)
        
        finally:
            self.stop()
    
    def stop(self):
        """Stop the continuous polling agent gracefully."""
        if not self.running:
            return
        
        logger.info("Stopping Continuous Polling Agent...")
        self.running = False
        
        # Log system shutdown
        self._log_system_event("AGENT_STOP", "Continuous polling agent stopped", "INFO")
        
        # Wait for queue to finish processing
        logger.info("Waiting for processing queue to finish...")
        self.processing_queue.join()
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        logger.info("Continuous Polling Agent stopped successfully")
    
    def status_report(self) -> str:
        """Generate a status report."""
        stats = self.get_processing_statistics()
        
        report = f"""
🤖 CONTINUOUS POLLING AGENT STATUS REPORT
{'='*60}
⏰ Last Poll: {self.last_poll_time.strftime('%Y-%m-%d %H:%M:%S')}
🔄 Status: {stats.get('system_status', 'unknown').upper()}
📊 Queue Size: {stats.get('current_queue_size', 0)} emails
⚡ Workers: {self.config.max_workers} active

📈 LAST 24 HOURS:
   • Total Processed: {stats.get('total_processed_24h', 0)}
   • Successful: {stats.get('successful_24h', 0)}
   • Failed: {stats.get('failed_24h', 0)}
   • Avg Processing Time: {stats.get('average_processing_time', 0):.2f}s

📂 CATEGORY BREAKDOWN:
"""
        
        for category, count in stats.get('category_breakdown', {}).items():
            report += f"   • {category}: {count} emails\n"
        
        report += "="*60
        return report

def main():
    """Main function for continuous polling agent."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Continuous Email Polling Agent')
    parser.add_argument('--config', default='polling_config.json', help='Configuration file path')
    parser.add_argument('--status', action='store_true', help='Show status and exit')
    parser.add_argument('--stats', action='store_true', help='Show statistics and exit')
    
    args = parser.parse_args()
    
    agent = ContinuousPollingAgent(args.config)
    
    if args.status or args.stats:
        print(agent.status_report())
        return
    
    try:
        print("\n" + "="*60)
        print("🚀 CONTINUOUS EMAIL POLLING AGENT")
        print("="*60)
        print("Starting real-time email monitoring...")
        print("Press Ctrl+C to stop")
        print("="*60 + "\n")
        
        agent.start()
        
    except KeyboardInterrupt:
        print("\n⚠️  Shutdown requested by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}")
    finally:
        agent.stop()

if __name__ == "__main__":
    main()
