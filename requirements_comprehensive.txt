# Comprehensive Email Processing System Requirements
# This system is designed to be flexible with package versions using standard APIs

# Core Gmail/Google APIs
google-auth>=2.20.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
google-api-python-client>=2.100.0

# Data validation and parsing
pydantic>=2.0.0

# Configuration and environment
python-dotenv>=1.0.0
PyYAML>=6.0

# Logging
colorlog>=6.7.0

# Web scraping and HTML parsing
beautifulsoup4>=4.12.0

# PDF Processing (preferred: PyMuPDF)
PyMuPDF>=1.23.0

# AI/ML for enhanced processing (updated to latest)
google-generativeai>=0.8.0

# Web framework for dashboard
flask>=2.3.0

# HTTP requests
requests>=2.31.0

# Memory/Vector database
chromadb>=0.4.0

# Optional enhanced features (uncomment if needed):
# openpyxl>=3.1.0         # Excel file processing
# python-docx>=0.8.11     # Word document processing  
# Pillow>=10.0.0          # Image processing
# pytesseract>=0.3.10     # OCR functionality
# psutil>=5.9.0           # System monitoring
# redis>=4.6.0            # Advanced caching/queuing
# prometheus-client>=0.17.0  # Metrics export

# Development and testing (optional)
# pytest>=7.4.0
# black>=23.0.0
# flake8>=6.0.0

# Note: Built-in Python modules used:
# - sqlite3 (database)
# - threading (concurrency)
# - queue (task queues)
# - signal (graceful shutdown)
# - concurrent.futures (parallel processing)
# - json, os, time, datetime, logging, etc.
