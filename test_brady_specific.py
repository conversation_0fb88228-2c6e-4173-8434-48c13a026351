"""
Test script for validating a specific Brady order using minimal MYOB API validation approach.
This tests the real Brady order from the JSON file: Purchase Order **********
"""
import json
import logging
from services.myob_service import MyobService

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_brady_order_**********():
    """Test the specific Brady order from the JSON file."""
    
    # Load the actual Brady order data
    brady_order_data = {
        "debtorid": 5760,
        "customerordernumber": "**********",
        "status": 0,
        "deliveryaddress": {
            "line1": "ALBURY WODONGA HEALTH ALBURY FOOD",
            "line2": "SERVICES",
            "line3": "BORELLA RD",
            "line4": "ALBURY NSW 2640",
        },
        "lines": [
            {
                "stockcode": "TS1AXL",
                "orderquantity": 1.0
            }
        ],
        "extrafields": [
            {
                "key": "X_SHIPVIA",
                "value": "BEST WAY"
            }
        ]
    }
    
    print("🔬 TESTING BRADY ORDER - Purchase Order **********")
    print("=" * 60)
    print(f"Customer: Brady (Debtor ID: {brady_order_data['debtorid']})")
    print(f"PO Number: {brady_order_data['customerordernumber']}")
    print(f"Product: {brady_order_data['lines'][0]['stockcode']} x {brady_order_data['lines'][0]['orderquantity']}")
    print(f"Delivery: {brady_order_data['deliveryaddress']['line1']}")
    print("=" * 60)
    
    try:
        # Initialize MYOB service
        service = MyobService()
        print("✅ MYOB service initialized successfully")
        
        # Step 1: Validate the order using minimal payload
        print("\n🔍 STEP 1: Validating order with MYOB API...")
        print("Sending minimal payload for validation...")
        
        validation_result = service.validate_order(brady_order_data)
        
        if not validation_result or 'order' not in validation_result:
            print("❌ Validation failed - no 'order' object in response")
            return False
            
        print("✅ Order validation successful!")
        print(f"Validation response keys: {list(validation_result.keys())}")
        
        # Get the validated payload from MYOB
        validated_payload = validation_result['order']
        print(f"Validated payload has {len(validated_payload)} fields")
        
        # Step 2: Create the order using MYOB's validated payload
        print("\n📝 STEP 2: Creating order with validated payload...")
        
        created_order = service.create_order(validated_payload)
        
        if created_order:
            print("✅ Order created successfully in MYOB!")
            print(f"Order ID: {created_order.get('id', 'N/A')}")
            print(f"Order Number: {created_order.get('ordernumber', 'N/A')}")
            return True
        else:
            print("❌ Order creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        logger.error(f"Brady order test failed: {e}", exc_info=True)
        return False

def main():
    """Main test function."""
    print("🧪 BRADY ORDER TEST - Minimal MYOB API Validation")
    print("Testing real Brady order: Purchase Order **********")
    print("This validates the minimal API approach works correctly\n")
    
    success = test_brady_order_**********()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Brady order test completed successfully!")
        print("✅ Minimal MYOB API validation approach is working")
    else:
        print("❌ FAILED: Brady order test failed")
        print("❌ Check logs for details")
    print("=" * 60)

if __name__ == "__main__":
    main()
